# This workflow will run in the context of the source of the PR.
# On a PR from a fork, the workflow will not have access to secrets, and so any parts of the build that require secrets will not run.
# If additional artifacts are needed, the failed "build-browser-target.yml" workflow held up by the check-run should be re-run.

name: Build Warden's Key Browser Extension

on:
  push:
    branches:
      - 'main'
      - 'develop'
    paths:
      - 'browser-source/**'
      - '.github/workflows/build-browser.yml'
  pull_request:
    types: [opened, synchronize]
    paths:
      - 'browser-source/**'
      - '.github/workflows/build-browser.yml'
  workflow_dispatch:
    inputs: {}

defaults:
  run:
    shell: bash

permissions: {}

jobs:
  setup:
    name: Setup
    runs-on: ubuntu-22.04
    outputs:
      repo_url: ${{ steps.gen_vars.outputs.repo_url }}
      adj_build_number: ${{ steps.gen_vars.outputs.adj_build_number }}
      node_version: ${{ steps.retrieve-node-version.outputs.node_version }}
    steps:
      - name: Check out repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{  github.event.pull_request.head.sha }}

      - name: Get Package Version
        id: gen_vars
        run: |
          repo_url=https://github.com/$GITHUB_REPOSITORY.git
          adj_build_num=${GITHUB_SHA:0:7}

          echo "repo_url=$repo_url" >> $GITHUB_OUTPUT
          echo "adj_build_number=$adj_build_num" >> $GITHUB_OUTPUT

      - name: Get Node Version
        id: retrieve-node-version
        working-directory: ./
        run: |
          NODE_NVMRC=$(cat .nvmrc)
          NODE_VERSION=${NODE_NVMRC/v/''}
          echo "node_version=$NODE_VERSION" >> $GITHUB_OUTPUT




  locales-test:
    name: Locales Test
    runs-on: ubuntu-22.04
    needs:
      - setup
    defaults:
      run:
        working-directory: apps/browser
    steps:
      - name: Check out repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{  github.event.pull_request.head.sha }}

      - name: Testing locales - extName length
        run: |
         found_error=false

         echo "Locales Test"
         echo "============"
         echo "extName string must be 40 characters or less"
         echo
         for locale in $(ls src/_locales/); do
           string_length=$(jq '.extName.message | length' src/_locales/$locale/messages.json)
           if [[ $string_length -gt 40 ]]; then
             echo "$locale: $string_length"
             found_error=true
           fi
         done

         if $found_error; then
           echo
           echo "Please fix 'extName' for the locales listed above."
           exit 1
         else
           echo "Test passed!"
         fi


  build-source:
    name: Build browser source
    runs-on: ubuntu-22.04
    needs:
      - setup
      - locales-test
    env:
      _BUILD_NUMBER: ${{ needs.setup.outputs.adj_build_number }}
      _NODE_VERSION: ${{ needs.setup.outputs.node_version }}
    steps:
      - name: Check out repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Set up Node
        uses: actions/setup-node@39370e3970a6d050c480ffad4ff0ed4d3fdee5af # v4.1.0
        with:
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'
          node-version: ${{ env._NODE_VERSION }}

      - name: Print environment
        run: |
          node --version
          npm --version

      - name: Build sources for reviewers
        run: |
          # Create a copy of browser-source for packaging
          cp -r browser-source browser-source-package

          # Remove .git directory if it exists
          rm -rf browser-source-package/.git

          # Create zip package
          zip -r browser-source.zip browser-source-package

      - name: Upload browser source
        uses: actions/upload-artifact@65c4c4a1ddee5b72f698fdd19549f0f0fb45cf08 # v4.6.0
        with:
          name: browser-source-${{ env._BUILD_NUMBER }}.zip
          path: browser-source.zip
          if-no-files-found: error


  build:
    name: Build
    runs-on: ubuntu-22.04
    needs:
      - setup
      - locales-test
      - build-source
    env:
      _BUILD_NUMBER: ${{ needs.setup.outputs.adj_build_number }}
      _NODE_VERSION: ${{ needs.setup.outputs.node_version }}
    strategy:
      matrix:
        include:
          - name: "chrome"
            npm_command: "dist:chrome"
            archive_name: "dist-chrome.zip"
            artifact_name: "dist-chrome-MV3"
          - name: "edge"
            npm_command: "dist:edge"
            archive_name: "dist-edge.zip"
            artifact_name: "dist-edge-MV3"
          - name: "firefox"
            npm_command: "dist:firefox"
            archive_name: "dist-firefox.zip"
            artifact_name: "dist-firefox"
          - name: "firefox-mv3"
            npm_command: "dist:firefox:mv3"
            archive_name: "dist-firefox.zip"
            artifact_name: "DO-NOT-USE-FOR-PROD-dist-firefox-MV3"
          - name: "opera-mv3"
            npm_command: "dist:opera:mv3"
            archive_name: "dist-opera.zip"
            artifact_name: "dist-opera-MV3"
    steps:
      - name: Check out repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{  github.event.pull_request.head.sha }}

      - name: Set up Node
        uses: actions/setup-node@39370e3970a6d050c480ffad4ff0ed4d3fdee5af # v4.1.0
        with:
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'
          node-version: ${{ env._NODE_VERSION }}

      - name: Print environment
        run: |
          node --version
          npm --version

      - name: Download browser source
        uses: actions/download-artifact@fa0a91b85d4f404e444e00e005971372dc801d16 # v4.1.8
        with:
          name: browser-source-${{ env._BUILD_NUMBER }}.zip

      - name: Unzip browser source artifact
        run: |
          unzip browser-source.zip
          rm browser-source.zip

      - name: NPM setup
        run: npm ci
        working-directory: browser-source/



      - name: Check source file size
        if: ${{ startsWith(matrix.name, 'firefox') }}
        run: |
          # Declare variable as indexed array
          declare -a FILES

          # Search for source files that are greater than 4M
          TARGET_DIR='./browser-source/apps/browser'
          while IFS=' ' read -r RESULT; do
              FILES+=("$RESULT")
          done < <(find $TARGET_DIR -size +4M)

          # Validate results and provide messaging
          if [[ ${#FILES[@]} -ne 0 ]]; then
              echo "File(s) exceeds size limit: 4MB"
              for FILE in ${FILES[@]}; do
                  echo "- $(du --si $FILE)"
              done
              echo "ERROR Firefox rejects extension uploads that contain files larger than 4MB"
              # Invoke failure
              exit 1
          fi

      - name: Build extension
        run: npm run ${{ matrix.npm_command }}
        working-directory: browser-source/apps/browser

      - name: Upload extension artifact
        uses: actions/upload-artifact@65c4c4a1ddee5b72f698fdd19549f0f0fb45cf08 # v4.6.0
        with:
          name: ${{ matrix.artifact_name }}-${{ env._BUILD_NUMBER }}.zip
          path: browser-source/apps/browser/dist/${{ matrix.archive_name }}
          if-no-files-found: error







  check-failures:
    name: Check for failures
    if: always()
    runs-on: ubuntu-22.04
    needs:
      - setup
      - locales-test
      - build-source
      - build
    steps:
      - name: Check if any job failed
        if: |
          github.event_name != 'pull_request_target'
          && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
          && contains(needs.*.result, 'failure')
        run: exit 1

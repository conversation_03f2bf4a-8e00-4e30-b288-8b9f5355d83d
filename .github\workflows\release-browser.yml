name: Release Browser
run-name: Release Browser ${{ inputs.release_type }}

on:
  workflow_dispatch:
    inputs:
      release_type:
        description: 'Release Options'
        required: true
        default: 'Initial Release'
        type: choice
        options:
          - Initial Release
          - Redeploy
          - Dry Run

defaults:
  run:
    shell: bash

jobs:
  setup:
    name: Setup
    runs-on: ubuntu-22.04
    permissions:
      contents: read
    outputs:
      release_version: ${{ steps.version.outputs.version }}
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Branch check
        if: ${{ github.event.inputs.release_type != 'Dry Run' }}
        run: |
          if [[ "$GITHUB_REF" != "refs/heads/rc" ]] && [[ "$GITHUB_REF" != "refs/heads/hotfix-rc-browser" ]]; then
            echo "==================================="
            echo "[!] Can only release from the 'rc' or 'hotfix-rc-browser' branches"
            echo "==================================="
            exit 1
          fi

      - name: Check Release Version
        id: version
        uses: bitwarden/gh-actions/release-version-check@main
        with:
          release-type: ${{ github.event.inputs.release_type }}
          project-type: ts
          file: apps/browser/src/manifest.json
          monorepo: true
          monorepo-project: browser


  locales-test:
    name: Locales Test
    runs-on: ubuntu-22.04
    needs: setup
    permissions:
      contents: read
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Testing locales - extName length
        run: |
          found_error=false

          echo "Locales Test"
          echo "============"
          echo "extName string must be 40 characters or less"
          echo
          for locale in $(ls src/_locales/); do
            string_length=$(jq '.extName.message | length' src/_locales/$locale/messages.json)
            if [[ $string_length -gt 40 ]]; then
              echo "$locale: $string_length"
              found_error=true
            fi
          done

          if $found_error; then
            echo
            echo "Please fix 'extName' for the locales listed above."
            exit 1
          else
            echo "Test passed!"
          fi
        working-directory: apps/browser


  release:
    name: Create GitHub Release
    runs-on: ubuntu-22.04
    needs:
      - setup
      - locales-test
    permissions:
      contents: write
    steps:
      - name: Download latest Release build artifacts
        if: ${{ github.event.inputs.release_type != 'Dry Run' }}
        uses: bitwarden/gh-actions/download-artifacts@main
        with:
          workflow: build-browser.yml
          workflow_conclusion: success
          branch: ${{ github.ref_name }}
          artifacts: 'browser-source-*.zip,
                      dist-chrome-*.zip,
                      dist-opera-*.zip,
                      dist-firefox-*.zip,
                      dist-edge-*.zip'

      - name: Dry Run - Download latest build artifacts
        if: ${{ github.event.inputs.release_type == 'Dry Run' }}
        uses: bitwarden/gh-actions/download-artifacts@main
        with:
          workflow: build-browser.yml
          workflow_conclusion: success
          branch: main
          artifacts: 'browser-source-*.zip,
                      dist-chrome-*.zip,
                      dist-opera-*.zip,
                      dist-firefox-*.zip,
                      dist-edge-*.zip'

      - name: Rename build artifacts
        env:
          PACKAGE_VERSION: ${{ needs.setup.outputs.release_version }}
        run: |
          mv browser-source.zip browser-source-$PACKAGE_VERSION.zip
          mv dist-chrome.zip dist-chrome-$PACKAGE_VERSION.zip
          mv dist-opera.zip dist-opera-$PACKAGE_VERSION.zip
          mv dist-firefox.zip dist-firefox-$PACKAGE_VERSION.zip
          mv dist-edge.zip dist-edge-$PACKAGE_VERSION.zip

      - name: Create release
        if: ${{ github.event.inputs.release_type != 'Dry Run' }}
        uses: ncipollo/release-action@cdcc88a9acf3ca41c16c37bb7d21b9ad48560d87 # v1.15.0
        with:
          artifacts: 'browser-source-${{ needs.setup.outputs.release_version }}.zip,
                      dist-chrome-${{ needs.setup.outputs.release_version }}.zip,
                      dist-opera-${{ needs.setup.outputs.release_version }}.zip,
                      dist-firefox-${{ needs.setup.outputs.release_version }}.zip,
                      dist-edge-${{ needs.setup.outputs.release_version }}.zip'
          commit: ${{ github.sha }}
          tag: "browser-v${{ needs.setup.outputs.release_version }}"
          name: "Browser v${{ needs.setup.outputs.release_version }}"
          body: "<insert release notes here>"
          token: ${{ secrets.GITHUB_TOKEN }}
          draft: true

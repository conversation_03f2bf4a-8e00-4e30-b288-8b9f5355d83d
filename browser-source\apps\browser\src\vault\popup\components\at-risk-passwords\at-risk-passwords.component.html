<popup-page [loading]="loading$ | async">
  <popup-header
    slot="header"
    [pageTitle]="'atRiskPasswords' | i18n"
    showBackButton
    [backAction]="navigateToVault.bind(this)"
  >
    <ng-container slot="end">
      <app-pop-out></app-pop-out>
    </ng-container>
  </popup-header>

  <bit-callout
    *ngIf="showAutofillCallout$ | async"
    type="info"
    [title]="'changeAtRiskPasswordsFaster' | i18n"
    data-testid="autofill-callout"
  >
    <p bitTypography="body2">{{ "changeAtRiskPasswordsFasterDesc" | i18n }}</p>
    <button
      type="button"
      bitButton
      buttonType="primary"
      (click)="activateInlineAutofillMenuVisibility()"
      data-testid="turn-on-autofill-button"
      class="tw-mr-2"
    >
      {{ "turnOnAutofill" | i18n }}
    </button>
    <button
      type="button"
      bitButton
      buttonType="secondary"
      (click)="dismissCallout()"
      data-testid="dismiss-callout-button"
    >
      {{ "dismiss" | i18n }}
    </button>
  </bit-callout>

  <ng-container *ngIf="atRiskItems$ | async as items">
    <p bitTypography="body2">{{ pageDescription$ | async }}</p>

    <bit-item-group>
      <bit-item *ngFor="let cipher of items">
        <button
          bit-item-content
          type="button"
          [appA11yTitle]="'viewItemTitle' | i18n: cipher.name"
          (click)="viewCipher(cipher)"
        >
          <div slot="start" class="tw-justify-start tw-w-7 tw-flex">
            <app-vault-icon [cipher]="cipher"></app-vault-icon>
          </div>
          <span data-testid="item-name">{{ cipher.name }}</span>
          <i
            *ngIf="cipher.hasAttachments"
            class="bwi bwi-paperclip bwi-sm"
            [appA11yTitle]="'attachments' | i18n"
          ></i>
          <span slot="secondary">{{ cipher.subTitle }}</span>
        </button>
        <bit-item-action slot="end">
          <button
            type="button"
            bitBadge
            variant="primary"
            appStopProp
            (click)="launchChangePassword(cipher)"
            [title]="'changeButtonTitle' | i18n: cipher.name"
            [attr.aria-label]="'changeButtonTitle' | i18n: cipher.name"
            [disabled]="launchingCipher() == cipher"
          >
            <ng-container *ngIf="launchingCipher() != cipher">
              {{ "change" | i18n }}
            </ng-container>
            <i
              *ngIf="launchingCipher() == cipher"
              class="bwi bwi-spinner bwi-spin"
              aria-hidden="true"
            ></i>
          </button>
        </bit-item-action>
      </bit-item>
    </bit-item-group>
  </ng-container>
</popup-page>

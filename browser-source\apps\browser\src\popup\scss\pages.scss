@import "variables.scss";

app-home {
  position: fixed;
  height: 100%;
  width: 100%;

  .center-content {
    margin-top: -50px;
    height: calc(100% + 50px);
  }

  img {
    width: 284px;
    margin: 0 auto;
  }

  p.lead {
    margin: 30px 0;
  }

  .btn + .btn {
    margin-top: 10px;
  }

  button.settings-icon {
    position: absolute;
    top: 10px;
    left: 10px;

    @include themify($themes) {
      color: themed("mutedColor");
    }

    &:not(:hover):not(:focus) {
      span {
        clip: rect(0 0 0 0);
        clip-path: inset(50%);
        height: 1px;
        overflow: hidden;
        position: absolute;
        white-space: nowrap;
        width: 1px;
      }
    }

    &:hover,
    &:focus {
      text-decoration: none;

      @include themify($themes) {
        color: themed("primaryColor");
      }
    }
  }
}

body.body-sm,
body.body-xs {
  app-home {
    .center-content {
      margin-top: 0;
      height: 100%;
    }

    p.lead {
      margin: 15px 0;
    }
  }
}

body.body-full {
  app-home {
    .center-content {
      margin-top: -80px;
      height: calc(100% + 80px);
    }
  }
}

.createAccountLink {
  padding: 30px 10px 0 10px;
}

.remember-email-check {
  padding-top: 18px;
  padding-left: 10px;
  padding-bottom: 18px;
}

.login-buttons > button {
  margin: 15px 0 15px 0;
}

.useBrowserlink {
  margin-left: 5px;
  margin-top: 20px;

  span {
    font-weight: 700;
    font-size: $font-size-small;
  }
}

.fido2-browser-selector-dropdown {
  @include themify($themes) {
    background-color: themed("boxBackgroundColor");
  }
  padding: 8px;
  width: 100%;
  box-shadow:
    0 2px 2px 0 rgba(0, 0, 0, 0.14),
    0 3px 1px -2px rgba(0, 0, 0, 0.12),
    0 1px 5px 0 rgba(0, 0, 0, 0.2);
  border-radius: $border-radius;
}

.fido2-browser-selector-dropdown-item {
  @include themify($themes) {
    color: themed("textColor") !important;
  }
  width: 100%;
  text-align: left;
  padding: 0px 15px 0px 5px;
  margin-bottom: 5px;
  border-radius: 3px;
  border: 1px solid transparent;
  transition: all 0.2s ease-in-out;

  &:hover {
    @include themify($themes) {
      background-color: themed("listItemBackgroundHoverColor") !important;
    }
  }

  &:last-child {
    margin-bottom: 0;
  }
}

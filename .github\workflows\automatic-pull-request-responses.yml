name: Automatic pull request responses
on:
  pull_request:
    types:
      - labeled
jobs:
  close-issue:
    name: 'Close pull request with automatic response'
    runs-on: ubuntu-22.04
    permissions:
      pull-requests: write
    steps:
      # Translation PR / Crowdin
      - if: github.event.label.name == 'translation-pr'
        name: Translation-PR
        uses: peter-evans/close-issue@276d7966e389d888f011539a86c8920025ea0626  # v3.0.1
        with:
          comment: |
            We really appreciate you taking the time to improve our translations.

            However we use a translation tool called [Crowdin](https://crowdin.com/) to help manage our localization efforts across many different languages. Our translations can only be updated using Crowdin, so we'll have to close this PR, but would really appreciate if you'd consider joining our awesome translation community over at Crowdin.

            More information can be found in the [localization section](https://contributing.bitwarden.com/contributing/#localization-l10n) of our [Contribution Guidelines](https://contributing.bitwarden.com/contributing/)

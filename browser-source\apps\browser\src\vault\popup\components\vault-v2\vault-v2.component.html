<popup-page [loading]="loading$ | async" disablePadding>
  <popup-header slot="header" [pageTitle]="'vault' | i18n">
    <ng-container slot="end">
      <app-new-item-dropdown [initialValues]="newItemItemValues$ | async"></app-new-item-dropdown>

      <app-pop-out></app-pop-out>
      <app-current-account></app-current-account>
    </ng-container>
  </popup-header>

  <div
    *ngIf="vaultState === VaultStateEnum.Empty"
    class="tw-flex tw-flex-col tw-h-full tw-justify-center"
  >
    <bit-no-items [icon]="vaultIcon">
      <ng-container slot="title">{{ "yourVaultIsEmpty" | i18n }}</ng-container>
      <ng-container slot="description">
        <p bitTypography="body2" class="tw-mx-6 tw-mt-2">{{ "emptyVaultDescription" | i18n }}</p>
      </ng-container>
      <a slot="button" bitButton buttonType="secondary" [routerLink]="['/add-cipher']">
        {{ "newLogin" | i18n }}
      </a>
    </bit-no-items>
  </div>

  <blocked-injection-banner
    *ngIf="vaultState !== VaultStateEnum.Empty"
    slot="full-width-notice"
  ></blocked-injection-banner>

  <!-- Show search & filters outside of the scroll area of the page -->
  <ng-container slot="above-scroll-area">
    <ng-container *ngIf="vaultState === VaultStateEnum.Empty && showEmptyVaultSpotlight$ | async">
      <bit-spotlight
        [title]="'emptyVaultNudgeTitle' | i18n"
        [subtitle]="'emptyVaultNudgeBody' | i18n"
        [buttonText]="'emptyVaultNudgeButton' | i18n"
        (onButtonClick)="navigateToImport()"
        (onDismiss)="dismissVaultNudgeSpotlight(NudgeType.EmptyVaultNudge)"
      >
      </bit-spotlight>
    </ng-container>
    <ng-container *ngIf="vaultState !== VaultStateEnum.Empty">
      <div class="tw-mb-4" *ngIf="showHasItemsVaultSpotlight$ | async">
        <bit-spotlight
          [title]="'hasItemsVaultNudgeTitle' | i18n"
          (onDismiss)="dismissVaultNudgeSpotlight(NudgeType.HasVaultItems)"
        >
          <ul class="tw-pl-4 tw-text-main tw-mb-0" bitTypography="body2">
            <li>{{ "hasItemsVaultNudgeBodyOne" | i18n }}</li>
            <li>{{ "hasItemsVaultNudgeBodyTwo" | i18n }}</li>
            <li>{{ "hasItemsVaultNudgeBodyThree" | i18n }}</li>
          </ul>
        </bit-spotlight>
      </div>
      <vault-at-risk-password-callout></vault-at-risk-password-callout>
      <app-vault-header-v2></app-vault-header-v2>
    </ng-container>
  </ng-container>

  <ng-container *ngIf="vaultState !== VaultStateEnum.Empty">
    <div
      *ngIf="vaultState === VaultStateEnum.NoResults"
      class="tw-flex tw-flex-col tw-justify-center tw-h-auto tw-pt-12"
    >
      <bit-no-items [icon]="noResultsIcon">
        <ng-container slot="title">{{ "noItemsMatchSearch" | i18n }}</ng-container>
        <ng-container slot="description">{{ "clearFiltersOrTryAnother" | i18n }}</ng-container>
      </bit-no-items>
    </div>

    <!-- For better consistency with screen readers, the role/aria needs to be on an element that isn't dynamic by *ngIf -->
    <div role="status" aria-live="polite">
      <div
        *ngIf="vaultState === VaultStateEnum.DeactivatedOrg"
        class="tw-flex tw-flex-col tw-justify-center tw-h-auto tw-pt-12"
      >
        <bit-no-items [icon]="deactivatedIcon">
          <ng-container slot="title">
            {{ "organizationIsDeactivated" | i18n }}
          </ng-container>
          <ng-container slot="description">{{ "contactYourOrgAdmin" | i18n }}</ng-container>
        </bit-no-items>
      </div>
    </div>

    <div
      *ngIf="vaultState === null"
      cdkVirtualScrollingElement
      class="tw-h-full tw-p-3 bit-compact:tw-p-2 tw-styled-scrollbar"
    >
      <app-autofill-vault-list-items></app-autofill-vault-list-items>
      <app-vault-list-items-container
        [title]="'favorites' | i18n"
        [ciphers]="(favoriteCiphers$ | async) || []"
        id="favorites"
        collapsibleKey="favorites"
      ></app-vault-list-items-container>
      <app-vault-list-items-container
        [title]="'allItems' | i18n"
        [ciphers]="(remainingCiphers$ | async) || []"
        id="allItems"
        disableSectionMargin
        collapsibleKey="allItems"
      ></app-vault-list-items-container>
    </div>
  </ng-container>
</popup-page>

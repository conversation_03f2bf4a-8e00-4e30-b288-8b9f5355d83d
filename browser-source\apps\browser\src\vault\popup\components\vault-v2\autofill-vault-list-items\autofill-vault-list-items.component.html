<app-vault-list-items-container
  *ngIf="autofillCiphers$ | async as ciphers"
  [ciphers]="ciphers"
  [title]="((currentURIIsBlocked$ | async) ? 'itemSuggestions' : 'autofillSuggestions') | i18n"
  [showRefresh]="showRefresh"
  (onRefresh)="refreshCurrentTab()"
  [description]="(showEmptyAutofillTip$ | async) ? ('autofillSuggestionsTip' | i18n) : null"
  showAutofillButton
  [disableDescriptionMargin]="showEmptyAutofillTip$ | async"
  [primaryActionAutofill]="clickItemsToAutofillVaultView$ | async"
  [groupByType]="groupByType()"
></app-vault-list-items-container>

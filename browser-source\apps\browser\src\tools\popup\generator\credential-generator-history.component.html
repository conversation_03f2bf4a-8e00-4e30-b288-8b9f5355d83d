<popup-page>
  <popup-header slot="header" [pageTitle]="'generatorHistory' | i18n" showBackButton>
    <ng-container slot="end">
      <app-pop-out></app-pop-out>
    </ng-container>
  </popup-header>
  <bit-empty-credential-history *ngIf="!(hasHistory$ | async)" style="display: contents" />
  <bit-credential-generator-history [account]="account$ | async" *ngIf="hasHistory$ | async" />
  <popup-footer slot="footer">
    <button
      [disabled]="!(hasHistory$ | async)"
      bitButton
      type="submit"
      buttonType="primary"
      (click)="clear()"
    >
      {{ "clearHistory" | i18n }}
    </button>
  </popup-footer>
</popup-page>

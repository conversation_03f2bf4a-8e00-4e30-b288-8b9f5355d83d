<header
  class="tw-p-3 bit-compact:tw-p-2 tw-pl-4 bit-compact:tw-pl-3 tw-transition-colors tw-duration-200 tw-border-0 tw-border-b tw-border-solid"
  [ngClass]="{
    'tw-bg-background-alt tw-border-transparent':
      this.background === 'alt' && !pageContentScrolled(),
    'tw-bg-background tw-border-secondary-300':
      (this.background === 'alt' && pageContentScrolled()) || this.background === 'default',
  }"
>
  <div class="tw-max-w-screen-sm tw-mx-auto tw-flex tw-justify-between tw-w-full">
    <div class="tw-inline-flex tw-items-center tw-gap-2 tw-h-9">
      <button
        class="-tw-ml-1"
        bitIconButton="bwi-angle-left"
        type="button"
        *ngIf="showBackButton"
        [title]="'back' | i18n"
        [attr.aria-label]="'back' | i18n"
        [bitAction]="backAction"
      ></button>
      <h1 *ngIf="pageTitle" bitTypography="h3" class="!tw-mb-0.5">
        {{ pageTitle }}
      </h1>
      <ng-content></ng-content>
    </div>
    <div class="tw-inline-flex tw-items-center tw-gap-2 tw-h-9">
      <ng-content select="[slot=end]"></ng-content>
    </div>
  </div>
</header>

name: <PERSON><PERSON> Sync

on:
  workflow_dispatch:
    inputs: {}
  schedule:
    - cron: '0 0 * * 5'

jobs:
  crowdin-sync:
    name: Autosync
    runs-on: ubuntu-24.04
    strategy:
      fail-fast: false
      matrix:
        include:
          - app_name: browser
            crowdin_project_id: "268134"
          - app_name: desktop
            crowdin_project_id: "299360"
          - app_name: web
            crowdin_project_id: "308189"
    steps:
      - name: Generate GH App token
        uses: actions/create-github-app-token@30bf6253fa41bdc8d1501d202ad15287582246b4 # v2.0.3
        id: app-token
        with:
          app-id: ${{ secrets.BW_GHAPP_ID }}
          private-key: ${{ secrets.BW_GHAPP_KEY }}

      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          token: ${{ steps.app-token.outputs.token }}

      - name: Login to Azure
        uses: Azure/login@e15b166166a8746d1a47596803bd8c1b595455cf # v1.6.0
        with:
          creds: ${{ secrets.AZURE_KV_CI_SERVICE_PRINCIPAL }}

      - name: Retrieve secrets
        id: retrieve-secrets
        uses: bitwarden/gh-actions/get-keyvault-secrets@main
        with:
          keyvault: "bitwarden-ci"
          secrets: "crowdin-api-token, github-gpg-private-key, github-gpg-private-key-passphrase"

      - name: Download translations
        uses: bitwarden/gh-actions/crowdin@main
        env:
          GITHUB_TOKEN: ${{ steps.app-token.outputs.token }}
          CROWDIN_API_TOKEN: ${{ steps.retrieve-secrets.outputs.crowdin-api-token }}
          CROWDIN_PROJECT_ID: ${{ matrix.crowdin_project_id }}
        with:
          config: crowdin.yml
          crowdin_branch_name: main
          upload_sources: false
          upload_translations: false
          download_translations: true
          github_user_name: "bitwarden-devops-bot"
          github_user_email: "<EMAIL>"
          commit_message: "Autosync the updated translations"
          localization_branch_name: crowdin-auto-sync-${{ matrix.app_name }}
          create_pull_request: true
          pull_request_title: "Autosync Crowdin Translations for ${{ matrix.app_name }}"
          pull_request_body: "Autosync the updated translations"
          working_directory: apps/${{ matrix.app_name }}
          gpg_private_key: ${{ steps.retrieve-secrets.outputs.github-gpg-private-key }}
          gpg_passphrase: ${{ steps.retrieve-secrets.outputs.github-gpg-private-key-passphrase }}

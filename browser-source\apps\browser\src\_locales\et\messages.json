{"appName": {"message": "Bitwarden"}, "appLogoLabel": {"message": "Bitwarden logo"}, "extName": {"message": "Bitwardeni paroolihaldur", "description": "Extension name, MUST be less than 40 characters (Safari restriction)"}, "extDesc": {"message": "<PERSON><PERSON>, t<PERSON><PERSON><PERSON> ja teel - Bitwarden hoiustab imelihtsalt kõik su paroolid, pääsuvõtmed ja tundliku info", "description": "Extension description, MUST be less than 112 characters (Safari restriction)"}, "loginOrCreateNewAccount": {"message": "Logi oma olem<PERSON>olevasse kontosse sisse või loo uus konto."}, "inviteAccepted": {"message": "<PERSON><PERSON><PERSON> vastu v<PERSON>ud"}, "createAccount": {"message": "Konto loomine"}, "newToBitwarden": {"message": "<PERSON><PERSON><PERSON>e kord?"}, "logInWithPasskey": {"message": "Logi sisse pääsuvõtmega"}, "useSingleSignOn": {"message": "Use single sign-on"}, "welcomeBack": {"message": "<PERSON><PERSON> tule<PERSON>t tagasi"}, "setAStrongPassword": {"message": "<PERSON><PERSON><PERSON><PERSON> parool"}, "finishCreatingYourAccountBySettingAPassword": {"message": "Lõpeta konto loomine parooli luues"}, "enterpriseSingleSignOn": {"message": "Ettevõtte ühekordne sisselogimine"}, "cancel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "close": {"message": "Sulge"}, "submit": {"message": "<PERSON><PERSON><PERSON>"}, "emailAddress": {"message": "E-posti aadress"}, "masterPass": {"message": "Ülemparool"}, "masterPassDesc": {"message": "Ülemparool on parool, millega pääsed oma kontole ligi. On äärmiselt tähtis, et ülemparool ei ununeks. Selle parooli taastamine ei ole mingil moel võima<PERSON>."}, "masterPassHintDesc": {"message": "<PERSON><PERSON><PERSON> võib abiks o<PERSON>, kui o<PERSON> unustanud."}, "masterPassHintText": {"message": "Kui sa unustad oma parooli, saad saata parooli vihje e-mailile.\n$CURRENT$/$MAXIMUM$ tähepiirang.", "placeholders": {"current": {"content": "$1", "example": "0"}, "maximum": {"content": "$2", "example": "50"}}}, "reTypeMasterPass": {"message": "Sisesta ülemparool uuesti"}, "masterPassHint": {"message": "Ülemparooli vihje (ei ole kohustuslik)"}, "passwordStrengthScore": {"message": "Password strength score $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "joinOrganization": {"message": "Liitu organisatsiooniga"}, "joinOrganizationName": {"message": "Liitu $ORGANIZATIONNAME$ organisatsiooniga", "placeholders": {"organizationName": {"content": "$1", "example": "My Org Name"}}}, "finishJoiningThisOrganizationBySettingAMasterPassword": {"message": "Lõpeta organisatsiooniga liitumine määrates ülemparool."}, "tab": {"message": "<PERSON><PERSON>"}, "vault": {"message": "<PERSON><PERSON>"}, "myVault": {"message": "<PERSON><PERSON> se<PERSON>"}, "allVaults": {"message": "<PERSON><PERSON><PERSON> se<PERSON>"}, "tools": {"message": "Tööriistad"}, "settings": {"message": "Seaded"}, "currentTab": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "copyPassword": {"message": "<PERSON><PERSON><PERSON>"}, "copyPassphrase": {"message": "Copy passphrase"}, "copyNote": {"message": "<PERSON><PERSON><PERSON>"}, "copyUri": {"message": "Kopeeri URI"}, "copyUsername": {"message": "<PERSON><PERSON><PERSON>"}, "copyNumber": {"message": "Kopeeri number"}, "copySecurityCode": {"message": "<PERSON><PERSON><PERSON>"}, "copyName": {"message": "<PERSON><PERSON><PERSON>"}, "copyCompany": {"message": "Ko<PERSON>eri firma nimi"}, "copySSN": {"message": "<PERSON><PERSON><PERSON>"}, "copyPassportNumber": {"message": "Ko<PERSON>eri passi number"}, "copyLicenseNumber": {"message": "<PERSON><PERSON><PERSON> number"}, "copyPrivateKey": {"message": "Copy private key"}, "copyPublicKey": {"message": "Copy public key"}, "copyFingerprint": {"message": "Copy fingerprint"}, "copyCustomField": {"message": "Copy $FIELD$", "placeholders": {"field": {"content": "$1", "example": "Custom field label"}}}, "copyWebsite": {"message": "Copy website"}, "copyNotes": {"message": "Copy notes"}, "copy": {"message": "Copy", "description": "Copy to clipboard"}, "fill": {"message": "Fill", "description": "This string is used on the vault page to indicate autofilling. Horizontal space is limited in the interface here so try and keep translations as concise as possible."}, "autoFill": {"message": "Automaatne täitmine"}, "autoFillLogin": {"message": "Täida andmed automaatselt"}, "autoFillCard": {"message": "Täida automaatselt kaardi andmed"}, "autoFillIdentity": {"message": "Täida identiteet"}, "fillVerificationCode": {"message": "Fill verification code"}, "fillVerificationCodeAria": {"message": "Fill Verification Code", "description": "Aria label for the heading displayed the inline menu for totp code autofill"}, "generatePasswordCopied": {"message": "<PERSON><PERSON><PERSON> (kopeeritakse)"}, "copyElementIdentifier": {"message": "<PERSON><PERSON><PERSON> kohanda<PERSON>d välja nimi"}, "noMatchingLogins": {"message": "Sobivaid kontoandmeid ei leitud"}, "noCards": {"message": "<PERSON><PERSON><PERSON>"}, "noIdentities": {"message": "Identiteedid puuduvad"}, "addLoginMenu": {"message": "<PERSON> konto and<PERSON>"}, "addCardMenu": {"message": "<PERSON>"}, "addIdentityMenu": {"message": "<PERSON> identiteet"}, "unlockVaultMenu": {"message": "<PERSON> hoidla"}, "loginToVaultMenu": {"message": "<PERSON><PERSON> hoid<PERSON>e sisse"}, "autoFillInfo": {"message": "<PERSON><PERSON> v<PERSON>ekaardi automaatseks täitmiseks puuduvad kirjed."}, "addLogin": {"message": "<PERSON> konto and<PERSON>"}, "addItem": {"message": "<PERSON> ese"}, "accountEmail": {"message": "Account email"}, "requestHint": {"message": "Request hint"}, "requestPasswordHint": {"message": "Request password hint"}, "enterYourAccountEmailAddressAndYourPasswordHintWillBeSentToYou": {"message": "Enter your account email address and your password hint will be sent to you"}, "getMasterPasswordHint": {"message": "<PERSON><PERSON>a ülempar<PERSON> vih<PERSON>ga me<PERSON>e"}, "continue": {"message": "Jätka"}, "sendVerificationCode": {"message": "<PERSON><PERSON> kin<PERSON>od oma e-postile"}, "sendCode": {"message": "<PERSON><PERSON> kood"}, "codeSent": {"message": "<PERSON><PERSON> on saadetud"}, "verificationCode": {"message": "Kinnituskood"}, "confirmIdentity": {"message": "Jätkamiseks kinnita oma identiteet."}, "changeMasterPassword": {"message": "<PERSON><PERSON>"}, "continueToWebApp": {"message": "Jätka veebibrauseris?"}, "continueToWebAppDesc": {"message": "Uuri teisi Bitwardeni konto funktsioone veebirakenduses."}, "continueToHelpCenter": {"message": "Kas soovid minna Abikeskusesse?"}, "continueToHelpCenterDesc": {"message": "Uuri teisigi Bitwardeni kasutusvõimalusi Abikeskuses."}, "continueToBrowserExtensionStore": {"message": "Mine edasi veebilaienduste poodi?"}, "continueToBrowserExtensionStoreDesc": {"message": "<PERSON>ta meil jõuda rohkemate inimesteni. Külasta enda laienduste veebipoodi ja jäta sinna positiivne hinnang."}, "changeMasterPasswordOnWebConfirmation": {"message": "Ülemparooli saab muuta Bitwardeni veebirakenduses."}, "fingerprintPhrase": {"message": "Unikaalne sõna<PERSON>da", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "yourAccountsFingerprint": {"message": "Konto sõrmejälje fraas", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "twoStepLogin": {"message": "Kaheastmeline kinnitamine"}, "logOut": {"message": "Logi välja"}, "aboutBitwarden": {"message": "<PERSON><PERSON>"}, "about": {"message": "Rakenduse info"}, "moreFromBitwarden": {"message": "<PERSON><PERSON><PERSON><PERSON> kohta"}, "continueToBitwardenDotCom": {"message": "Mine edasi bitwarden.com-i?"}, "bitwardenForBusiness": {"message": "Bitwarden Ärikliendile"}, "bitwardenAuthenticator": {"message": "Bitwarden Authenticator"}, "continueToAuthenticatorPageDesc": {"message": "Bitwardeni Autentiteerijaga saad sa hoiustada autentiteerimise võtmeid ja luua TOTP koode kaheastmeliseks kinnitamiseks. Uuri lähemalt veebilehelt bitwarden.com"}, "bitwardenSecretsManager": {"message": "Bitwarden Secrets Manager"}, "continueToSecretsManagerPageDesc": {"message": "<PERSON><PERSON><PERSON>, halda ja jaga turvaliselt arendajate saladusi läbi Bitwarden Secrets Manageri. Uuri lähemalt veebilehelt bitwarden.com."}, "passwordlessDotDev": {"message": "Passwordless.dev"}, "continueToPasswordlessDotDevPageDesc": {"message": "Loo sujuv ja turvaline kogemus sisselogimisel Passwordless.dev-iga ja ilma traditsiooniliste paroolideta. Uuri lähemalt veebilehelt bitwarden.com."}, "freeBitwardenFamilies": {"message": "<PERSON><PERSON><PERSON>"}, "freeBitwardenFamiliesPageDesc": {"message": "Sul on võimalik saada endale tasuta Bitwarden Families plaan. Lunasta see pakkumine meie veebirakenduses."}, "version": {"message": "Versioon"}, "save": {"message": "Salvesta"}, "move": {"message": "<PERSON><PERSON><PERSON>"}, "addFolder": {"message": "<PERSON><PERSON><PERSON> lisamine"}, "name": {"message": "<PERSON><PERSON>"}, "editFolder": {"message": "<PERSON><PERSON> ka<PERSON>a"}, "editFolderWithName": {"message": "Edit folder: $FOLDERNAME$", "placeholders": {"foldername": {"content": "$1", "example": "Social"}}}, "newFolder": {"message": "<PERSON><PERSON>"}, "folderName": {"message": "<PERSON><PERSON><PERSON> nimi"}, "folderHintText": {"message": "<PERSON><PERSON>a teise kasuta panemiseks lisa si<PERSON>kausta nimi, millele jä<PERSON> \"/\". Näiteks: Sotsiaalmeedia/Foorumid"}, "noFoldersAdded": {"message": "<PERSON>i lisanud <PERSON>egi kausta"}, "createFoldersToOrganize": {"message": "<PERSON><PERSON>, et oma hoidla kirjeid organiseeri<PERSON>"}, "deleteFolderPermanently": {"message": "Kas sa oled kindel, et soovid selle kausta j<PERSON>da<PERSON>t kustutada?"}, "deleteFolder": {"message": "<PERSON><PERSON><PERSON>"}, "folders": {"message": "<PERSON><PERSON><PERSON>"}, "noFolders": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>, mida kuvada."}, "helpFeedback": {"message": "<PERSON><PERSON> ja tag<PERSON>"}, "helpCenter": {"message": "Bitwardeni abikeskus"}, "communityForums": {"message": "Ava <PERSON> foorum"}, "contactSupport": {"message": "Võta Bitwardeniga ühendust"}, "sync": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "syncVaultNow": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ho<PERSON>la"}, "lastSync": {"message": "<PERSON><PERSON><PERSON><PERSON>:"}, "passGen": {"message": "<PERSON><PERSON><PERSON> genereerimine"}, "generator": {"message": "<PERSON><PERSON><PERSON>", "description": "Short for 'credential generator'."}, "passGenInfo": {"message": "Loo oma kontodele tugevaid ja unikaalseid paroole."}, "bitWebVaultApp": {"message": "Bitward<PERSON> ve<PERSON>"}, "importItems": {"message": "<PERSON><PERSON><PERSON> and<PERSON>"}, "select": {"message": "Vali"}, "generatePassword": {"message": "Loo parool"}, "generatePassphrase": {"message": "Generate passphrase"}, "passwordGenerated": {"message": "Password generated"}, "passphraseGenerated": {"message": "Passphrase generated"}, "usernameGenerated": {"message": "Username generated"}, "emailGenerated": {"message": "Email generated"}, "regeneratePassword": {"message": "Genereeri parool uuesti"}, "options": {"message": "<PERSON><PERSON><PERSON>"}, "length": {"message": "Pikkus"}, "include": {"message": "<PERSON><PERSON><PERSON>", "description": "Card header for password generator include block"}, "uppercaseDescription": {"message": "<PERSON><PERSON><PERSON> trü<PERSON>ähti", "description": "Tooltip for the password generator uppercase character checkbox"}, "uppercaseLabel": {"message": "A-Z", "description": "Label for the password generator uppercase character checkbox"}, "lowercaseDescription": {"message": "<PERSON><PERSON><PERSON>", "description": "Full description for the password generator lowercase character checkbox"}, "lowercaseLabel": {"message": "a-z", "description": "Label for the password generator lowercase character checkbox"}, "numbersDescription": {"message": "<PERSON><PERSON><PERSON>", "description": "Full description for the password generator numbers checkbox"}, "numbersLabel": {"message": "0-9", "description": "Label for the password generator numbers checkbox"}, "specialCharactersDescription": {"message": "<PERSON><PERSON><PERSON>", "description": "Full description for the password generator special characters checkbox"}, "numWords": {"message": "Sõnade arv"}, "wordSeparator": {"message": "Sõna eralda<PERSON>"}, "capitalize": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Make the first letter of a work uppercase."}, "includeNumber": {"message": "Lisa number"}, "minNumbers": {"message": "<PERSON><PERSON><PERSON> a<PERSON>v <PERSON>"}, "minSpecial": {"message": "<PERSON><PERSON><PERSON> arv spetsiaalmärke"}, "avoidAmbiguous": {"message": "Väldi raskesti eristatavaid tähti ja sümboleid", "description": "Label for the avoid ambiguous characters checkbox."}, "generatorPolicyInEffect": {"message": "Enterprise policy requirements have been applied to your generator options.", "description": "Indicates that a policy limits the credential generator screen."}, "searchVault": {"message": "<PERSON><PERSON><PERSON> ho<PERSON>"}, "edit": {"message": "<PERSON><PERSON>"}, "view": {"message": "<PERSON><PERSON><PERSON>"}, "noItemsInList": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kirjed, mida kuvada."}, "itemInformation": {"message": "<PERSON><PERSON><PERSON> and<PERSON>"}, "username": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "password": {"message": "<PERSON><PERSON><PERSON>"}, "totp": {"message": "Salajane autentikaatori võti"}, "passphrase": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "favorite": {"message": "<PERSON><PERSON><PERSON>"}, "unfavorite": {"message": "<PERSON><PERSON><PERSON>"}, "itemAddedToFavorites": {"message": "<PERSON>se lisatud lemm<PERSON>"}, "itemRemovedFromFavorites": {"message": "<PERSON>se eema<PERSON> le<PERSON>"}, "notes": {"message": "Märkmed"}, "privateNote": {"message": "Private note"}, "note": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "editItem": {"message": "<PERSON><PERSON><PERSON>"}, "folder": {"message": "<PERSON><PERSON>"}, "deleteItem": {"message": "Kustuta kirje"}, "viewItem": {"message": "<PERSON><PERSON><PERSON>"}, "launch": {"message": "Käivita"}, "launchWebsite": {"message": "<PERSON>"}, "launchWebsiteName": {"message": "Launch website $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret item"}}}, "website": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "toggleVisibility": {"message": "Näita"}, "manage": {"message": "<PERSON><PERSON>"}, "other": {"message": "<PERSON><PERSON>"}, "unlockMethods": {"message": "Ava<PERSON> valikud"}, "unlockMethodNeededToChangeTimeoutActionDesc": {"message": "<PERSON><PERSON><PERSON> a<PERSON> tegevuse muutmiseks vali esmalt lahtilukustamise meetod."}, "unlockMethodNeeded": {"message": "Määra avamise meetod seadetes"}, "sessionTimeoutHeader": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "vaultTimeoutHeader": {"message": "Vault timeout"}, "otherOptions": {"message": "<PERSON><PERSON> valikud"}, "rateExtension": {"message": "Hinda seda laiendust"}, "browserNotSupportClipboard": {"message": "Ka<PERSON>tatav brauser ei toeta lihtsat lõikelaua kopeerimist. Kopeeri see käsitsi."}, "verifyYourIdentity": {"message": "Verify your identity"}, "weDontRecognizeThisDevice": {"message": "We don't recognize this device. Enter the code sent to your email to verify your identity."}, "continueLoggingIn": {"message": "Continue logging in"}, "yourVaultIsLocked": {"message": "<PERSON><PERSON><PERSON> on lukus. Jätkamiseks sisesta ülemparool."}, "yourVaultIsLockedV2": {"message": "Your vault is locked"}, "yourAccountIsLocked": {"message": "Your account is locked"}, "or": {"message": "or"}, "unlock": {"message": "Lukusta lahti"}, "loggedInAsOn": {"message": "Sisse logitud kontosse $EMAIL$ aadressil $HOSTNAME$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "hostname": {"content": "$2", "example": "bitwarden.com"}}}, "invalidMasterPassword": {"message": "Vale ülemparool"}, "vaultTimeout": {"message": "<PERSON><PERSON><PERSON>"}, "vaultTimeout1": {"message": "Timeout"}, "lockNow": {"message": "Lukusta paroolihoidla"}, "lockAll": {"message": "Lukusta kõik"}, "immediately": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "tenSeconds": {"message": "10 sekundi pärast"}, "twentySeconds": {"message": "20 sekundi pärast"}, "thirtySeconds": {"message": "30 sekundi pärast"}, "oneMinute": {"message": "1 minuti pärast"}, "twoMinutes": {"message": "2 minuti pärast"}, "fiveMinutes": {"message": "5 minuti pärast"}, "fifteenMinutes": {"message": "15 minuti pärast"}, "thirtyMinutes": {"message": "30 minuti pärast"}, "oneHour": {"message": "1 tunni pärast"}, "fourHours": {"message": "4 tunni p<PERSON>rast"}, "onLocked": {"message": "Arvutist välja<PERSON><PERSON><PERSON>l"}, "onRestart": {"message": "Brauseri taaskäivitamisel"}, "never": {"message": "<PERSON><PERSON> k<PERSON>"}, "security": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "confirmMasterPassword": {"message": "<PERSON><PERSON><PERSON>"}, "masterPassword": {"message": "Ülemparool"}, "masterPassImportant": {"message": "Ülemparooli ei saa taastada, kui sa selle unustama peaksid!"}, "masterPassHintLabel": {"message": "<PERSON><PERSON><PERSON>"}, "errorOccurred": {"message": "Ilmnes viga"}, "emailRequired": {"message": "E-posti aadress on <PERSON><PERSON><PERSON><PERSON>."}, "invalidEmail": {"message": "Vigane e-posti aadress."}, "masterPasswordRequired": {"message": "<PERSON><PERSON><PERSON> on ülemparooli sisestamine."}, "confirmMasterPasswordRequired": {"message": "<PERSON><PERSON><PERSON> on ülemparooli uuesti sisestamine."}, "masterPasswordMinlength": {"message": "Ülemparool peab olema vähemalt $VALUE$ tähemärgi pikkune.", "description": "The Master Password must be at least a specific number of characters long.", "placeholders": {"value": {"content": "$1", "example": "8"}}}, "masterPassDoesntMatch": {"message": "Ülemparoolid ei ühti."}, "newAccountCreated": {"message": "Konto on loodud! Võid nüüd sisse logida."}, "newAccountCreated2": {"message": "Uus konto loodud!"}, "youHaveBeenLoggedIn": {"message": "<PERSON>te sisse logitud!"}, "youSuccessfullyLoggedIn": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "youMayCloseThisWindow": {"message": "<PERSON><PERSON><PERSON> selle akna sulgeda"}, "masterPassSent": {"message": "Ülemparooli vihje saadeti sinu e-postile."}, "verificationCodeRequired": {"message": "<PERSON><PERSON><PERSON><PERSON> on kinnituskood."}, "webauthnCancelOrTimeout": {"message": "Autentimine tühistati või kestis liiga kaua aega. Palun proovi uuesti."}, "invalidVerificationCode": {"message": "<PERSON>"}, "valueCopied": {"message": "$VALUE$ on kopeeritud", "description": "Value has been copied to the clipboard.", "placeholders": {"value": {"content": "$1", "example": "Password"}}}, "autofillError": {"message": "Automaatne täitmine ebaõnnestus. Palun kopeeri informatsioon käsitsi."}, "totpCaptureError": {"message": "<PERSON><PERSON> skännida sellelt lehelt QR-kood"}, "totpCaptureSuccess": {"message": "Autentimise võti on lisatud"}, "totpCapture": {"message": "Skänneeri see QR-kood läbi autentikaatori"}, "totpHelperTitle": {"message": "Muuda 2-astmeline kinnitamine sujuvaks"}, "totpHelper": {"message": "Bitwarden saab hoiustada ja täita 2-as<PERSON><PERSON><PERSON> kinnitamise koode. Kopeeri ja kleebi võti siia."}, "totpHelperWithCapture": {"message": "Bitwarden saab hoiustada ja täita 2-ast<PERSON><PERSON> kinnitamise koode. <PERSON><PERSON><PERSON> ka<PERSON> i<PERSON>, et teha ekraanipilt autentiteerimise QR koodist või kopeeri ja kleebi võti siia."}, "learnMoreAboutAuthenticators": {"message": "<PERSON><PERSON> l<PERSON>t autentikaatorite kohta"}, "copyTOTP": {"message": "Kopeeri autentiteerimise v<PERSON> (TOTP)"}, "loggedOut": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "loggedOutDesc": {"message": "Sa logisid oma kontolt välja."}, "loginExpired": {"message": "<PERSON><PERSON><PERSON> on aegun<PERSON>."}, "logIn": {"message": "<PERSON><PERSON> sisse"}, "logInToBitwarden": {"message": "Log in to Bitwarden"}, "enterTheCodeSentToYourEmail": {"message": "Enter the code sent to your email"}, "enterTheCodeFromYourAuthenticatorApp": {"message": "Enter the code from your authenticator app"}, "pressYourYubiKeyToAuthenticate": {"message": "Press your YubiKey to authenticate"}, "duoTwoFactorRequiredPageSubtitle": {"message": "Duo two-step login is required for your account. Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingIn": {"message": "Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingInWithSecurityKey": {"message": "Follow the steps below to finish logging in with your security key."}, "restartRegistration": {"message": "Alusta registreerimist uuesti"}, "expiredLink": {"message": "Aegunud link"}, "pleaseRestartRegistrationOrTryLoggingIn": {"message": "<PERSON><PERSON><PERSON> alusta registreerimist uuesti või proovi sisse logida."}, "youMayAlreadyHaveAnAccount": {"message": "You may already have an account"}, "logOutConfirmation": {"message": "Oled kindel, et soovid välja logida?"}, "yes": {"message": "<PERSON><PERSON>"}, "no": {"message": "<PERSON>i"}, "location": {"message": "Location"}, "unexpectedError": {"message": "Tekkis ootamatu viga."}, "nameRequired": {"message": "<PERSON><PERSON> on kohustuslik."}, "addedFolder": {"message": "<PERSON><PERSON> on lisatud"}, "twoStepLoginConfirmation": {"message": "Kaheastmeline kinnitamine aitab konto turvalisust tõsta. Lisaks paroolile pead kontole ligipääsemiseks kinnitama sisselogimise päringu SMS-ga, telefonikõnega, autentimise rakendusega või e-postiga. Kaheastmelist kinnitust saab sisse lülitada bitwarden.com veebihoidlas. <PERSON>vid seda kohe avada?"}, "twoStepLoginConfirmationContent": {"message": "Make your account more secure by setting up two-step login in the Bitwarden web app."}, "twoStepLoginConfirmationTitle": {"message": "Continue to web app?"}, "editedFolder": {"message": "<PERSON><PERSON> on muudetud"}, "deleteFolderConfirmation": {"message": "Oled kindel, et soovid seda kausta kustutada?"}, "deletedFolder": {"message": "Kaust on kustutatud"}, "gettingStartedTutorial": {"message": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>"}, "gettingStartedTutorialVideo": {"message": "Vaata meie alustamise juhendit, et brauseri lisa kohta rohkem teavet saada."}, "syncingComplete": {"message": "Sünkroniseerimine on lõpetatud"}, "syncingFailed": {"message": "Sünkroniseerimine nurjus"}, "passwordCopied": {"message": "Parool on kopeeritud"}, "uri": {"message": "URI"}, "uriPosition": {"message": "URI $POSITION$", "description": "A listing of URIs. Ex: URI 1, URI 2, URI 3, etc.", "placeholders": {"position": {"content": "$1", "example": "2"}}}, "newUri": {"message": "Uus URI"}, "addDomain": {"message": "Add domain", "description": "'Domain' here refers to an internet domain name (e.g. 'bitwarden.com') and the message in whole described the act of putting a domain value into the context."}, "addedItem": {"message": "<PERSON><PERSON><PERSON> on lisatud"}, "editedItem": {"message": "<PERSON><PERSON><PERSON> on muudetud"}, "deleteItemConfirmation": {"message": "<PERSON><PERSON> t<PERSON>i selle kirje kustutada?"}, "deletedItem": {"message": "<PERSON><PERSON><PERSON> on kustutatud"}, "overwritePassword": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "overwritePasswordConfirmation": {"message": "Oled kindel, et soovid olemasolevat parooli üle kirjutada?"}, "overwriteUsername": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "overwriteUsernameConfirmation": {"message": "Oled kindel, et soovid praegust kasuta<PERSON> ü<PERSON> kirjutada? "}, "searchFolder": {"message": "<PERSON><PERSON><PERSON> ka<PERSON>a"}, "searchCollection": {"message": "<PERSON>tsi kogumi<PERSON>ku"}, "searchType": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "noneFolder": {"message": "<PERSON><PERSON>", "description": "This is the folder for uncategorized items"}, "enableAddLoginNotification": {"message": "<PERSON><PERSON><PERSON> \"<PERSON> konto and<PERSON>\""}, "vaultSaveOptionsTitle": {"message": "Save to vault options"}, "addLoginNotificationDesc": {"message": "\"<PERSON> konto andmed\" tea<PERSON>tus ilmub pärast esimest sisselogimist ning võimaldab kontoandmeid automaatselt Bitwardenisse lisada."}, "addLoginNotificationDescAlt": {"message": "Ask to add an item if one isn't found in your vault. Applies to all logged in accounts."}, "showCardsInVaultViewV2": {"message": "Always show cards as Autofill suggestions on Vault view"}, "showCardsCurrentTab": {"message": "<PERSON><PERSON> \"<PERSON><PERSON>\" vaa<PERSON> ka<PERSON>"}, "showCardsCurrentTabDesc": {"message": "<PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" v<PERSON><PERSON>, et neid saaks kiiresti si<PERSON>tada"}, "showIdentitiesInVaultViewV2": {"message": "Always show identities as Autofill suggestions on Vault view"}, "showIdentitiesCurrentTab": {"message": "<PERSON><PERSON> \"<PERSON><PERSON>\" vaates identiteete"}, "showIdentitiesCurrentTabDesc": {"message": "<PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" vaates identiteete, et neid saaks kiiresti si<PERSON>tada"}, "clickToAutofillOnVault": {"message": "Click items to autofill on Vault view"}, "clickToAutofill": {"message": "Click items in autofill suggestion to fill"}, "clearClipboard": {"message": "<PERSON><PERSON><PERSON><PERSON>a sisu kustutamine", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "clearClipboardDesc": {"message": "Kustutab automaatselt lõikelauale kopeeritud sisu.", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "notificationAddDesc": {"message": "Kas bitwarden peaks seda parooli meeles pidama?"}, "notificationAddSave": {"message": "<PERSON><PERSON>, salves<PERSON> see"}, "notificationViewAria": {"message": "View $ITEMNAME$, opens in new window", "placeholders": {"itemName": {"content": "$1"}}, "description": "Aria label for the view button in notification bar confirmation message"}, "notificationNewItemAria": {"message": "New Item, opens in new window", "description": "Aria label for the new item button in notification bar confirmation message when error is prompted"}, "notificationEditTooltip": {"message": "Edit before saving", "description": "Tooltip and Aria label for edit button on cipher item"}, "newNotification": {"message": "New notification"}, "labelWithNotification": {"message": "$LABEL$: New notification", "description": "Label for the notification with a new login suggestion.", "placeholders": {"label": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "notificationLoginSaveConfirmation": {"message": "saved to Bitwarden.", "description": "Shown to user after item is saved."}, "notificationLoginUpdatedConfirmation": {"message": "updated in Bitwarden.", "description": "Shown to user after item is updated."}, "selectItemAriaLabel": {"message": "Select $ITEMTYPE$, $ITEMNAME$", "description": "Used by screen readers. $1 is the item type (like vault or folder), $2 is the selected item name.", "placeholders": {"itemType": {"content": "$1"}, "itemName": {"content": "$2"}}}, "saveAsNewLoginAction": {"message": "Save as new login", "description": "Button text for saving login details as a new entry."}, "updateLoginAction": {"message": "Update login", "description": "Button text for updating an existing login entry."}, "unlockToSave": {"message": "Unlock to save this login", "description": "User prompt to take action in order to save the login they just entered."}, "saveLogin": {"message": "Save login", "description": "Prompt asking the user if they want to save their login details."}, "updateLogin": {"message": "Update existing login", "description": "Prompt asking the user if they want to update an existing login entry."}, "loginSaveSuccess": {"message": "<PERSON><PERSON> saved", "description": "Message displayed when login details are successfully saved."}, "loginUpdateSuccess": {"message": "Login updated", "description": "Message displayed when login details are successfully updated."}, "loginUpdateTaskSuccess": {"message": "Great job! You took the steps to make you and $ORGANIZATION$ more secure.", "placeholders": {"organization": {"content": "$1"}}, "description": "Shown to user after login is updated."}, "loginUpdateTaskSuccessAdditional": {"message": "Thank you for making $ORGANIZATION$ more secure. You have $TASK_COUNT$ more passwords to update.", "placeholders": {"organization": {"content": "$1"}, "task_count": {"content": "$2"}}, "description": "Shown to user after login is updated."}, "nextSecurityTaskAction": {"message": "Change next password", "description": "Message prompting user to undertake completion of another security task."}, "saveFailure": {"message": "Error saving", "description": "Error message shown when the system fails to save login details."}, "saveFailureDetails": {"message": "Oh no! We couldn't save this. Try entering the details manually.", "description": "Detailed error message shown when saving login details fails."}, "enableChangedPasswordNotification": {"message": "<PERSON><PERSON> and<PERSON><PERSON>"}, "changedPasswordNotificationDesc": {"message": "<PERSON>i veeb<PERSON>hel tuvastatakse olemasolevate and<PERSON>e muutmine, siis pakutakse nende andmete uuendamist Bitwardenis."}, "changedPasswordNotificationDescAlt": {"message": "Ask to update a login's password when a change is detected on a website. Applies to all logged in accounts."}, "enableUsePasskeys": {"message": "K<PERSON>si luba pääsuvõtmete salvestamiseks ja kasutamiseks"}, "usePasskeysDesc": {"message": "Ask to save new passkeys or log in with passkeys stored in your vault. Applies to all logged in accounts."}, "notificationChangeDesc": {"message": "<PERSON><PERSON> seda parooli ka Bitwardenis uuendada?"}, "notificationChangeSave": {"message": "Jah, uuenda"}, "notificationUnlockDesc": {"message": "Ava Bitwardeni ho<PERSON>, et automaattäide lõpuni viia."}, "notificationUnlock": {"message": "Lukusta lahti"}, "additionalOptions": {"message": "Additional options"}, "enableContextMenuItem": {"message": "<PERSON>va parema kliki men<PERSON>ü valikud"}, "contextMenuItemDesc": {"message": "Võimaldab parema kliki menüüs ka<PERSON> Bitwardeni valikuid, nt kontoandmete täitmist või parooli genereerimist. "}, "contextMenuItemDescAlt": {"message": "Use a secondary click to access password generation and matching logins for the website. Applies to all logged in accounts."}, "defaultUriMatchDetection": {"message": "Vaike URI sobivuse tuvastamine", "description": "Default URI match detection for autofill."}, "defaultUriMatchDetectionDesc": {"message": "<PERSON><PERSON>, kuidas kirje ja URI sobivus tuvastatakse. Seda kasutatakse näiteks siis, kui lehele üritatakse automaatselt and<PERSON><PERSON> si<PERSON>."}, "theme": {"message": "<PERSON><PERSON>"}, "themeDesc": {"message": "Muuda rakenduse värvikujundust."}, "themeDescAlt": {"message": "Change the application's color theme. Applies to all logged in accounts."}, "dark": {"message": "<PERSON><PERSON>", "description": "Dark color"}, "light": {"message": "<PERSON><PERSON>", "description": "Light color"}, "exportFrom": {"message": "Export from"}, "exportVault": {"message": "Ekspordi hoidla"}, "fileFormat": {"message": "Failivorming"}, "fileEncryptedExportWarningDesc": {"message": "This file export will be password protected and require the file password to decrypt."}, "filePassword": {"message": "File password"}, "exportPasswordDescription": {"message": "This password will be used to export and import this file"}, "accountRestrictedOptionDescription": {"message": "Use your account encryption key, derived from your account's username and Master Password, to encrypt the export and restrict import to only the current Bitwarden account."}, "passwordProtectedOptionDescription": {"message": "Set a file password to encrypt the export and import it to any Bitwarden account using the password for decryption."}, "exportTypeHeading": {"message": "Export type"}, "accountRestricted": {"message": "Account restricted"}, "filePasswordAndConfirmFilePasswordDoNotMatch": {"message": "“File password”  and “Confirm file password“ do not match."}, "warning": {"message": "HOIATUS", "description": "WARNING (should stay in capitalized letters if the language permits)"}, "warningCapitalized": {"message": "Warning", "description": "Warning (should maintain locale-relevant capitalization)"}, "confirmVaultExport": {"message": "Hoidla eksportimise kinnitamine"}, "exportWarningDesc": {"message": "Eksporditav fail sisal<PERSON><PERSON> hoidla sisu, mis on krüpteeringuta. Seda faili ei tohiks kaua käidelda ning mitte mingil juhul ebaturvaliselt saata (näiteks e-postiga). Kustuta see koheselt pärast kasutamist."}, "encExportKeyWarningDesc": {"message": "Eksporditavate andmete krüpteerimiseks kasutatakse kontol olevat krüpteerimisvõtit. Kui sa peaksid seda krüpteerimise võtit roteerima, ei saa sa järgnevalt eksporditavaid andmeid enam dekrü<PERSON>eerida."}, "encExportAccountWarningDesc": {"message": "Iga Bitwardeni kasutaja krü<PERSON><PERSON><PERSON><PERSON>ti on unikaalne. Eksporditud andmeid ei saa importida teise Bitwardeni kasutajakontosse."}, "exportMasterPassword": {"message": "<PERSON><PERSON><PERSON> olevate and<PERSON><PERSON> eksportimiseks on vajalik ülemparooli sisestamine."}, "shared": {"message": "Jagatud"}, "bitwardenForBusinessPageDesc": {"message": "Bitwarden for Business allows you to share your vault items with others by using an organization. Learn more on the bitwarden.com website."}, "moveToOrganization": {"message": "<PERSON><PERSON><PERSON> organ<PERSON>"}, "movedItemToOrg": {"message": "$ITEMNAME$ teisaldati $ORGNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "orgname": {"content": "$2", "example": "Company Name"}}}, "moveToOrgDesc": {"message": "Vali organisatsioon, kuhu soovid seda kirjet teisaldada. Teisaldamisega saab kirje omanikuks organisatsioon. Pärast kirje teisaldamist ei ole sa enam selle otsene omanik."}, "learnMore": {"message": "<PERSON>e edasi"}, "authenticatorKeyTotp": {"message": "Autentimise võti (TOTP)"}, "verificationCodeTotp": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> (TOTP)"}, "copyVerificationCode": {"message": "<PERSON><PERSON><PERSON>"}, "attachments": {"message": "Manused"}, "deleteAttachment": {"message": "<PERSON><PERSON><PERSON> manus"}, "deleteAttachmentConfirmation": {"message": "Oled kindel, et soovid manuse kustutada?"}, "deletedAttachment": {"message": "Manus on kustutatud"}, "newAttachment": {"message": "<PERSON> uus manus"}, "noAttachments": {"message": "Manused puuduvad."}, "attachmentSaved": {"message": "<PERSON><PERSON> on salvestatud."}, "file": {"message": "Fail"}, "fileToShare": {"message": "File to share"}, "selectFile": {"message": "Vali fail."}, "maxFileSize": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> faili suurus on 500 MB."}, "featureUnavailable": {"message": "Funktsioon pole saadaval"}, "legacyEncryptionUnsupported": {"message": "Legacy encryption is no longer supported. Please contact support to recover your account."}, "premiumMembership": {"message": "Premium versioon"}, "premiumManage": {"message": "Halda Premium versiooni"}, "premiumManageAlert": {"message": "Saad Premium versiooni hallata bitwarden.com veebihoidlas. <PERSON><PERSON> seda kohe teha?"}, "premiumRefresh": {"message": "<PERSON><PERSON><PERSON> tellimust"}, "premiumNotCurrentMember": {"message": "Sa ei ole hetkel premium versiooni kasutaja."}, "premiumSignUpAndGet": {"message": "Premium versiooni lisab järgmised eelised:"}, "ppremiumSignUpStorage": {"message": "1 GB ulatuses krü<PERSON><PERSON><PERSON><PERSON> salves<PERSON>."}, "premiumSignUpEmergency": {"message": "Emergency access."}, "premiumSignUpTwoStepOptions": {"message": "Proprietary two-step login options such as YubiKey and Duo."}, "ppremiumSignUpReports": {"message": "<PERSON><PERSON><PERSON>, konto se<PERSON> ja and<PERSON><PERSON><PERSON> raportid aitavad hoidlat turvalisena hoida."}, "ppremiumSignUpTotp": {"message": "TOTP kinnituskoodide (2FA) genereerija hoidlas olevatele kasutajakontodele."}, "ppremiumSignUpSupport": {"message": "<PERSON><PERSON><PERSON>."}, "ppremiumSignUpFuture": {"message": "Tulevased premium funktsioonid - tasuta!"}, "premiumPurchase": {"message": "Osta Premium"}, "premiumPurchaseAlertV2": {"message": "You can purchase Premium from your account settings on the Bitwarden web app."}, "premiumCurrentMember": {"message": "Oled premium kasutaja!"}, "premiumCurrentMemberThanks": {"message": "<PERSON><PERSON><PERSON>, et toetad Bitwardenit."}, "premiumFeatures": {"message": "Upgrade to Premium and receive:"}, "premiumPrice": {"message": "<PERSON><PERSON><PERSON> see ainult $PRICE$ / aastas!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "premiumPriceV2": {"message": "All for just $PRICE$ per year!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "refreshComplete": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "enableAutoTotpCopy": {"message": "TOTP automaatne kopeerimine"}, "disableAutoTotpCopyDesc": {"message": "Kui sinu sisselogimise and<PERSON><PERSON> on juurde lisatud autentimise võti, kopeeritakse TOTP kood automaatse täitmise kasutamisel lõikelauale."}, "enableAutoBiometricsPrompt": {"message": "Küsi avamisel biomeetriat"}, "premiumRequired": {"message": "V<PERSON>lik on Premium versioon"}, "premiumRequiredDesc": {"message": "<PERSON><PERSON> on vajalik tasulist kontot omada."}, "authenticationTimeout": {"message": "Authentication timeout"}, "authenticationSessionTimedOut": {"message": "The authentication session timed out. Please restart the login process."}, "verificationCodeEmailSent": {"message": "Kinnituskood saadeti e-posti aadressile $EMAIL$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "dontAskAgainOnThisDeviceFor30Days": {"message": "Don't ask again on this device for 30 days"}, "selectAnotherMethod": {"message": "Select another method", "description": "Select another two-step login method"}, "useYourRecoveryCode": {"message": "Use your recovery code"}, "insertU2f": {"message": "Sisesta oma turvaline võti arvuti USB porti. <PERSON><PERSON> sellel on nupp, siis vajuta seda."}, "openInNewTab": {"message": "Open in new tab"}, "webAuthnAuthenticate": {"message": "WebAuthn kinnitamine"}, "readSecurityKey": {"message": "Read security key"}, "awaitingSecurityKeyInteraction": {"message": "Awaiting security key interaction..."}, "loginUnavailable": {"message": "Sisselogimine ei ole sa<PERSON>val"}, "noTwoStepProviders": {"message": "<PERSON><PERSON><PERSON> kontol on aktiveeritud kaheastmeline kinnitus. Si<PERSON>i ei toeta konkreetne brauser ühtegi aktiveeritud kaheastmelise kinnitamise teenust."}, "noTwoStepProviders2": {"message": "<PERSON><PERSON><PERSON> ka<PERSON>ta <PERSON> brauserit (näiteks Chrome) ja/või lisa uus kaheastmelise teenuse pakku<PERSON>, mis töötab rohkemates brauserites (näiteks mõni autentimise rakendus)."}, "twoStepOptions": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> sisselogimise valikud"}, "selectTwoStepLoginMethod": {"message": "Select two-step login method"}, "recoveryCodeDesc": {"message": "<PERSON><PERSON><PERSON><PERSON> ligip<PERSON>äs kaheastmelise kinnitamise teenusele? <PERSON><PERSON><PERSON> k<PERSON>, et kaheastmeline kinnitamine oma kontol välja lü<PERSON>."}, "recoveryCodeTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> kood"}, "authenticatorAppTitle": {"message": "Autentimise rakendus"}, "authenticatorAppDescV2": {"message": "Enter a code generated by an authenticator app like Bitwarden Authenticator.", "description": "'Bitwarden Authenticator' is a product name and should not be translated."}, "yubiKeyTitleV2": {"message": "Yubico OTP Security Key"}, "yubiKeyDesc": {"message": "Ka<PERSON>ta kontole ligipääsemiseks YubiKey-d. See töötab YubiKey 4, 4 <PERSON><PERSON>, 4C ja NEO seadmetega."}, "duoDescV2": {"message": "Enter a code generated by Duo Security.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "duoOrganizationDesc": {"message": "Kinnita organisatsiooni jaoks Duo Security abil, kasutades selleks Duo Mobile rakendust, SMS-i, telefonikõnet või U2F turvavõtit.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "webAuthnTitle": {"message": "FIDO2 WebAuthn"}, "webAuthnDesc": {"message": "<PERSON><PERSON><PERSON> mistahes WebAuthn toetavat turvalist v<PERSON><PERSON><PERSON>, et oma kontole ligi p<PERSON>."}, "emailTitle": {"message": "E-post"}, "emailDescV2": {"message": "Enter a code sent to your email."}, "selfHostedEnvironment": {"message": "Self-hosted Environment"}, "selfHostedBaseUrlHint": {"message": "Specify the base URL of your on-premises hosted Bitwarden installation. Example: https://bitwarden.company.com"}, "selfHostedCustomEnvHeader": {"message": "For advanced configuration, you can specify the base URL of each service independently."}, "selfHostedEnvFormInvalid": {"message": "You must add either the base Server URL or at least one custom environment."}, "customEnvironment": {"message": "Kohandatud keskkond"}, "baseUrl": {"message": "Serveri URL"}, "selfHostBaseUrl": {"message": "Self-host server URL", "description": "Label for field requesting a self-hosted integration service URL"}, "apiUrl": {"message": "API serveri URL"}, "webVaultUrl": {"message": "Veebihoidla serveri URL"}, "identityUrl": {"message": "Identity Server URL"}, "notificationsUrl": {"message": "Teavitus serveri URL"}, "iconsUrl": {"message": "Ikoonide serveri URL"}, "environmentSaved": {"message": "The environment URLs have been saved."}, "showAutoFillMenuOnFormFields": {"message": "Show autofill menu on form fields", "description": "Represents the message for allowing the user to enable the autofill overlay"}, "autofillSuggestionsSectionTitle": {"message": "Autofill suggestions"}, "autofillSpotlightTitle": {"message": "Easily find autofill suggestions"}, "autofillSpotlightDesc": {"message": "Turn off your browser's autofill settings, so they don't conflict with Bitwarden."}, "turnOffBrowserAutofill": {"message": "Turn off $BROWSER$ autofill", "placeholders": {"browser": {"content": "$1", "example": "Chrome"}}}, "turnOffAutofill": {"message": "Turn off autofill"}, "showInlineMenuLabel": {"message": "Show autofill suggestions on form fields"}, "showInlineMenuIdentitiesLabel": {"message": "Display identities as suggestions"}, "showInlineMenuCardsLabel": {"message": "Display cards as suggestions"}, "showInlineMenuOnIconSelectionLabel": {"message": "Display suggestions when icon is selected"}, "showInlineMenuOnFormFieldsDescAlt": {"message": "Applies to all logged in accounts."}, "turnOffBrowserBuiltInPasswordManagerSettings": {"message": "Turn off your browser's built in password manager settings to avoid conflicts."}, "turnOffBrowserBuiltInPasswordManagerSettingsLink": {"message": "Edit browser settings."}, "autofillOverlayVisibilityOff": {"message": "Off", "description": "Overlay setting select option for disabling autofill overlay"}, "autofillOverlayVisibilityOnFieldFocus": {"message": "When field is selected (on focus)", "description": "Overlay appearance select option for showing the field on focus of the input element"}, "autofillOverlayVisibilityOnButtonClick": {"message": "When autofill icon is selected", "description": "Overlay appearance select option for showing the field on click of the overlay icon"}, "enableAutoFillOnPageLoadSectionTitle": {"message": "Autofill on page load"}, "enableAutoFillOnPageLoad": {"message": "Luba kontoandmete t<PERSON>"}, "enableAutoFillOnPageLoadDesc": {"message": "Sisselogimise vormi tuvastamisel sisestatakse sinna kontoandmed automaatselt."}, "experimentalFeature": {"message": "Häkitud või ebausaldusväärsed veebilehed võivad lehe laadimisel automaatset sisestamist kuritarvitada."}, "learnMoreAboutAutofillOnPageLoadLinkText": {"message": "Learn more about risks"}, "learnMoreAboutAutofill": {"message": "Rohkem infot automaattäite kohta"}, "defaultAutoFillOnPageLoad": {"message": "Vaikevalik kontoandmete täitmiseks"}, "defaultAutoFillOnPageLoadDesc": {"message": "\"Luba kontoandmete täitmine\" sissel<PERSON>litamisel saad selle siiski individuaalselt iga kirje seadetes välja lülitada. See seadistus rakend<PERSON> kõikidele kirjetele, mida pole eraldi konfigureeritud."}, "itemAutoFillOnPageLoad": {"message": "Luba kontoandmete täit<PERSON> (kui see on aktiveeritud)"}, "autoFillOnPageLoadUseDefault": {"message": "<PERSON><PERSON><PERSON> v<PERSON>eadist<PERSON>"}, "autoFillOnPageLoadYes": {"message": "<PERSON><PERSON><PERSON> kontoand<PERSON> lehe la<PERSON>"}, "autoFillOnPageLoadNo": {"message": "Ära täida kontoandmeid lehe la<PERSON>l"}, "commandOpenPopup": {"message": "Ava hoidla uues aknas"}, "commandOpenSidebar": {"message": "<PERSON> ho<PERSON>"}, "commandAutofillLoginDesc": {"message": "Autofill the last used login for the current website"}, "commandAutofillCardDesc": {"message": "Autofill the last used card for the current website"}, "commandAutofillIdentityDesc": {"message": "Autofill the last used identity for the current website"}, "commandGeneratePasswordDesc": {"message": "Loo ja kopeeri uus juhus<PERSON>ult koostatud parool lõ<PERSON>."}, "commandLockVaultDesc": {"message": "Lukusta hoidla"}, "customFields": {"message": "Kohandatud väljad"}, "copyValue": {"message": "<PERSON><PERSON><PERSON> k<PERSON>"}, "value": {"message": "Väärtus"}, "newCustomField": {"message": "<PERSON>us kohandatud väli"}, "dragToSort": {"message": "<PERSON><PERSON><PERSON> sort<PERSON><PERSON><PERSON>"}, "dragToReorder": {"message": "Drag to reorder"}, "cfTypeText": {"message": "Tekst"}, "cfTypeHidden": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "cfTypeBoolean": {"message": "Boolean"}, "cfTypeCheckbox": {"message": "Märkeruut"}, "cfTypeLinked": {"message": "Ühenduses", "description": "This describes a field that is 'linked' (tied) to another field."}, "linkedValue": {"message": "Ühendatud väärtus", "description": "This describes a value that is 'linked' (tied) to another value."}, "popup2faCloseMessage": {"message": "See a<PERSON> su<PERSON>, kui klikid oma e-posti aknale, et sealt kinnituskoodi vaadata. <PERSON><PERSON> selle hüpi<PERSON>na uues aknas avada, et seda ei juhtuks?"}, "popupU2fCloseMessage": {"message": "Ka<PERSON>tatav brauser ei suuda selles aknas U2F päringuid töödelda. Kas avan uue akna, et saaksid U2F abil sisse logida?"}, "enableFavicon": {"message": "<PERSON><PERSON> veeb<PERSON> i<PERSON>"}, "faviconDesc": {"message": "Kuvab iga kirje kõrval lehekülje ikooni."}, "faviconDescAlt": {"message": "Näita väikest tuttavat i<PERSON>oni iga kirje kõrval. Kehtib ka sisselogitud kontodele."}, "enableBadgeCounter": {"message": "<PERSON><PERSON> kirjete arvu"}, "badgeCounterDesc": {"message": "<PERSON><PERSON><PERSON> <PERSON> konk<PERSON> veebilehel olevate kirjete arvu."}, "cardholderName": {"message": "Kaardiomani<PERSON> nimi"}, "number": {"message": "Number"}, "brand": {"message": "Väljastaja"}, "expirationMonth": {"message": "<PERSON>eg<PERSON><PERSON> kuu"}, "expirationYear": {"message": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>a"}, "expiration": {"message": "Aegumine"}, "january": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "february": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "march": {"message": "<PERSON><PERSON><PERSON>"}, "april": {"message": "Aprill"}, "may": {"message": "<PERSON>"}, "june": {"message": "<PERSON><PERSON>"}, "july": {"message": "<PERSON><PERSON>"}, "august": {"message": "August"}, "september": {"message": "September"}, "october": {"message": "Oktoober"}, "november": {"message": "November"}, "december": {"message": "Detsember"}, "securityCode": {"message": "Turvakood"}, "ex": {"message": "nt."}, "title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "mr": {"message": "Hr"}, "mrs": {"message": "Mrs"}, "ms": {"message": "Pr"}, "dr": {"message": "Dr"}, "mx": {"message": "Mx"}, "firstName": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "middleName": {"message": "<PERSON><PERSON>"}, "lastName": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fullName": {"message": "Täisnimi"}, "identityName": {"message": "Identiteedi nimi"}, "company": {"message": "Ettevõte"}, "ssn": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "passportNumber": {"message": "Passi number"}, "licenseNumber": {"message": "Li<PERSON>entsi number"}, "email": {"message": "E-post"}, "phone": {"message": "Telefoninumber"}, "address": {"message": "<PERSON><PERSON><PERSON>"}, "address1": {"message": "Aadress 1"}, "address2": {"message": "Aadress 2"}, "address3": {"message": "Aadress 3"}, "cityTown": {"message": "Linn / asula"}, "stateProvince": {"message": "Ma<PERSON><PERSON> / vald"}, "zipPostalCode": {"message": "Postiindeks"}, "country": {"message": "Riik"}, "type": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "typeLogin": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> andmed"}, "typeLogins": {"message": "<PERSON><PERSON><PERSON>"}, "typeSecureNote": {"message": "<PERSON><PERSON><PERSON> märkus"}, "typeCard": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "typeIdentity": {"message": "Identiteet"}, "typeSshKey": {"message": "SSH key"}, "newItemHeader": {"message": "Uus $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "editItemHeader": {"message": "Edit $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "viewItemHeader": {"message": "View $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "passwordHistory": {"message": "<PERSON><PERSON><PERSON> a<PERSON>"}, "generatorHistory": {"message": "Generator history"}, "clearGeneratorHistoryTitle": {"message": "Clear generator history"}, "cleargGeneratorHistoryDescription": {"message": "If you continue, all entries will be permanently deleted from generator's history. Are you sure you want to continue?"}, "back": {"message": "Tagasi"}, "collections": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "nCollections": {"message": "$COUNT$ kogumikku", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "favorites": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "popOutNewWindow": {"message": "<PERSON> u<PERSON> aknas"}, "refresh": {"message": "<PERSON><PERSON><PERSON>"}, "cards": {"message": "Pangakaardid"}, "identities": {"message": "Identiteedid"}, "logins": {"message": "<PERSON><PERSON><PERSON>"}, "secureNotes": {"message": "<PERSON>rvalis<PERSON> märkmed"}, "sshKeys": {"message": "SSH Keys"}, "clear": {"message": "T<PERSON><PERSON><PERSON><PERSON>", "description": "To clear something out. example: To clear browser history."}, "checkPassword": {"message": "<PERSON><PERSON><PERSON>, kas parool on lekkin<PERSON>."}, "passwordExposed": {"message": "See parool on erinevates andmeleketes kokku $VALUE$ korda lekkinud. Peaksid selle ära muutma.", "placeholders": {"value": {"content": "$1", "example": "2"}}}, "passwordSafe": {"message": "Seda parooli ei <PERSON> andmeleketest leida. Parooli edasi kasu<PERSON> peaks olema turvaline."}, "baseDomain": {"message": "Baasdomeen", "description": "Domain name. Ex. website.com"}, "baseDomainOptionRecommended": {"message": "<PERSON><PERSON> nimi [base domain] (soovitatav)", "description": "Domain name. Ex. website.com"}, "domainName": {"message": "<PERSON><PERSON> ni<PERSON>", "description": "Domain name. Ex. website.com"}, "host": {"message": "Host", "description": "A URL's host value. For example, the host of https://sub.domain.com:443 is 'sub.domain.com:443'."}, "exact": {"message": "Täpne"}, "startsWith": {"message": "Algab"}, "regEx": {"message": "RegEx", "description": "A programming term, also known as 'RegEx'."}, "matchDetection": {"message": "Sobivuse tuvastamine", "description": "URI match detection for autofill."}, "defaultMatchDetection": {"message": "Vaike sobivuse tuvastamine", "description": "Default URI match detection for autofill."}, "toggleOptions": {"message": "<PERSON><PERSON> sisse"}, "toggleCurrentUris": {"message": "<PERSON><PERSON> praegused <PERSON><PERSON>'d", "description": "Toggle the display of the URIs of the currently open tabs in the browser."}, "currentUri": {"message": "Praegune URI", "description": "The URI of one of the current open tabs in the browser."}, "organization": {"message": "Organisatsioon", "description": "An entity of multiple related people (ex. a team or business organization)."}, "types": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "allItems": {"message": "K<PERSON>ik kirjed"}, "noPasswordsInList": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> par<PERSON>, mida kuvada."}, "clearHistory": {"message": "Clear history"}, "nothingToShow": {"message": "Nothing to show"}, "nothingGeneratedRecently": {"message": "You haven't generated anything recently"}, "remove": {"message": "<PERSON><PERSON><PERSON>"}, "default": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "dateUpdated": {"message": "Uuendatud", "description": "ex. Date this item was updated"}, "dateCreated": {"message": "<PERSON><PERSON><PERSON>", "description": "ex. Date this item was created"}, "datePasswordUpdated": {"message": "Parool on uuendatud", "description": "ex. Date this password was updated"}, "neverLockWarning": {"message": "Oled kindel, et soovid kasutada valikut \"<PERSON><PERSON> kunagi\"? Sellega talletatakse sinu hoidla krüpteerimise võtit seadme mälus. Peaksid olema väga hoolas ja kindel, et seade on ohutu ja selles ei ole pahavara."}, "noOrganizationsList": {"message": "Sa ei kuulu ühessegi organisatsiooni. Organisatsioonid võimaldavad sul kirjeid turvaliselt teiste kasutajatega jagada."}, "noCollectionsInList": {"message": "Puuduvad kollektsioonid, mida kuvada."}, "ownership": {"message": "Omanik"}, "whoOwnsThisItem": {"message": "Kes on selle kirje omanik?"}, "strong": {"message": "<PERSON><PERSON><PERSON>", "description": "ex. A strong password. Scale: Weak -> Good -> Strong"}, "good": {"message": "<PERSON>a", "description": "ex. A good password. Scale: Weak -> Good -> Strong"}, "weak": {"message": "Nõrk", "description": "ex. A weak password. Scale: Weak -> Good -> Strong"}, "weakMasterPassword": {"message": "Nõrk ülemparool"}, "weakMasterPasswordDesc": {"message": "<PERSON><PERSON><PERSON> on nõrk. Oma Bitwardeni konto paremaks kaitsmiseks peaksid kasutama tugevat parooli. <PERSON><PERSON> kindel, et soovid seda parooli ülemparoolina kasutada?"}, "pin": {"message": "PIN", "description": "PIN code. Ex. The short code (often numeric) that you use to unlock a device."}, "unlockWithPin": {"message": "Ava PIN-iga"}, "setYourPinTitle": {"message": "Set PIN"}, "setYourPinButton": {"message": "Set PIN"}, "setYourPinCode": {"message": "Määra Bitwardeni lahtilukustamiseks PIN kood. Rakendusest täielikult välja logides nullitakse ka PIN koodi seaded."}, "setPinCode": {"message": "You can use this PIN to unlock Bitwarden. Your PIN will be reset if you ever fully log out of the application."}, "pinRequired": {"message": "Nõutakse PIN koodi."}, "invalidPin": {"message": "Vale PIN kood."}, "tooManyInvalidPinEntryAttemptsLoggingOut": {"message": "Liiga palju ebaõnnestunud katseid. Login välja."}, "unlockWithBiometrics": {"message": "Ava biomeetriaga"}, "unlockWithMasterPassword": {"message": "Unlock with master password"}, "awaitDesktop": {"message": "Kinnituse ootamine tö<PERSON>laua rakend<PERSON>lt"}, "awaitDesktopDesc": {"message": "Kinnitamiseks kasuta biomeetrilist lahtilukustamist Bitwardeni töölaua rakenduses."}, "lockWithMasterPassOnRestart": {"message": "<PERSON><PERSON><PERSON>, kui brauser taaskäivitatakse"}, "lockWithMasterPassOnRestart1": {"message": "Require master password on browser restart"}, "selectOneCollection": {"message": "Pead valima vähemalt ühe kogumiku."}, "cloneItem": {"message": "Klooni kirje"}, "clone": {"message": "Kloon"}, "passwordGenerator": {"message": "<PERSON><PERSON><PERSON> genereer<PERSON>"}, "usernameGenerator": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> genereerija"}, "useThisEmail": {"message": "Use this email"}, "useThisPassword": {"message": "<PERSON><PERSON><PERSON> seda parooli"}, "useThisPassphrase": {"message": "Use this passphrase"}, "useThisUsername": {"message": "<PERSON><PERSON><PERSON> seda kasuta<PERSON>"}, "securePasswordGenerated": {"message": "Turvaline parool loodud! Ära unusta uuendata seda ka veebisaidil."}, "useGeneratorHelpTextPartOne": {"message": "Kasuta generaatorit", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "useGeneratorHelpTextPartTwo": {"message": "et luua tugev ja ainulaadne parool", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "vaultCustomization": {"message": "Vault customization"}, "vaultTimeoutAction": {"message": "<PERSON><PERSON><PERSON> te<PERSON>"}, "vaultTimeoutAction1": {"message": "Timeout action"}, "lock": {"message": "Lukusta", "description": "Verb form: to make secure or inaccessible by"}, "trash": {"message": "Prügikast", "description": "Noun: a special folder to hold deleted items"}, "searchTrash": {"message": "<PERSON>tsi prügikastist"}, "permanentlyDeleteItem": {"message": "Kustuta kirje j<PERSON>ädavalt"}, "permanentlyDeleteItemConfirmation": {"message": "Oled kindel, et soovid selle kirje jääda<PERSON>t kustutada?"}, "permanentlyDeletedItem": {"message": "<PERSON><PERSON><PERSON> on jäädavalt kustutatud"}, "restoreItem": {"message": "<PERSON><PERSON><PERSON> k<PERSON>je"}, "restoredItem": {"message": "<PERSON><PERSON><PERSON> on taastatud"}, "alreadyHaveAccount": {"message": "On juba konto?"}, "vaultTimeoutLogOutConfirmation": {"message": "Väljalogimine eemaldab hoidlale ligipääsu ning nõuab pärast ajal<PERSON>pu perioodi uuesti autentimist. <PERSON>d kindel, et soovid seda valikut kasutada?"}, "vaultTimeoutLogOutConfirmationTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> tegevuse kinnitamine"}, "autoFillAndSave": {"message": "Täida ja salvesta"}, "fillAndSave": {"message": "Täida ja salvesta"}, "autoFillSuccessAndSavedUri": {"message": "<PERSON><PERSON><PERSON> ja URI salvestati"}, "autoFillSuccess": {"message": "<PERSON><PERSON><PERSON>"}, "insecurePageWarning": {"message": "Hoiatus: See on ebaturvaline HTTP lehekülg. Teised osapooled võivad sinu sisestatud infot potentsiaalselt näha ja muuta. Algselt oli see kirje salvestatud turvalise (HTTPS) lehe jaoks."}, "insecurePageWarningFillPrompt": {"message": "<PERSON><PERSON> kirje automaattäita?"}, "autofillIframeWarning": {"message": "See vorm on majutatud teistsugusel domeenil kui sinu salvestatud URI. Vajuta OK, et automaattäita või Tühista, et täitmine peatada."}, "autofillIframeWarningTip": {"message": "<PERSON><PERSON><PERSON>, et antud teavitust edaspidi ei kuvataks, sa<PERSON><PERSON> see URI $HOSTNAME$ Bitwardeni kirjesse.", "placeholders": {"hostname": {"content": "$1", "example": "www.example.com"}}}, "setMasterPassword": {"message": "Määra <PERSON>"}, "currentMasterPass": {"message": "Praegune ülemparool"}, "newMasterPass": {"message": "<PERSON><PERSON> ü<PERSON>"}, "confirmNewMasterPass": {"message": "<PERSON><PERSON><PERSON> ü<PERSON>par<PERSON>"}, "masterPasswordPolicyInEffect": {"message": "Üks või enam organisatsiooni eeskirja nõuavad, et ülemparool vastaks nendele nõudmistele:"}, "policyInEffectMinComplexity": {"message": "Minimaalne keerulisuse skoor peab olema $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "policyInEffectMinLength": {"message": "Minimaalne pikkus peab olema $LENGTH$", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "policyInEffectUppercase": {"message": "Sisaldab üht või enamat suurtähte"}, "policyInEffectLowercase": {"message": "Sisaldab üht või enamat väiketähte"}, "policyInEffectNumbers": {"message": "Sisaldab üht või rohkem numbreid"}, "policyInEffectSpecial": {"message": "Sisaldab üht või enamat järgnevatest märkidest: $CHARS$", "placeholders": {"chars": {"content": "$1", "example": "!@#$%^&*"}}}, "masterPasswordPolicyRequirementsNotMet": {"message": "Uus ülemparool ei vasta eeskirjades väljatoodud tingimustele."}, "receiveMarketingEmailsV2": {"message": "<PERSON><PERSON>, uudiseid ja pakku<PERSON> Bitwardenilt oma <PERSON>."}, "unsubscribe": {"message": "<PERSON><PERSON><PERSON><PERSON> tellimus"}, "atAnyTime": {"message": "iga hetk."}, "byContinuingYouAgreeToThe": {"message": "Jätkates nõustud sa"}, "and": {"message": "ja"}, "acceptPolicies": {"message": "Märkeruudu mark<PERSON><PERSON><PERSON><PERSON> nõustud järgnevaga:"}, "acceptPoliciesRequired": {"message": "Ka<PERSON>tustingimuste ja Privaatsuspoliitikaga pole nõustutud."}, "termsOfService": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "privacyPolicy": {"message": "Privaatsuspoliitika"}, "yourNewPasswordCannotBeTheSameAsYourCurrentPassword": {"message": "Your new password cannot be the same as your current password."}, "hintEqualsPassword": {"message": "<PERSON><PERSON><PERSON> vihje ei saa olla sama mis parool ise."}, "ok": {"message": "Ok"}, "errorRefreshingAccessToken": {"message": "Juurdepääsukoodi Värskendamine Ebaõnnestus"}, "errorRefreshingAccessTokenDesc": {"message": "Ei leidnud värskendamise koodi või API võtit. Palun proovi logida välja ja uuesti sisse."}, "desktopSyncVerificationTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> sü<PERSON>ise kinnitamine"}, "desktopIntegrationVerificationText": {"message": "<PERSON><PERSON><PERSON>, et töölaua rakendus kuvab järgnevat sõrmejälge: "}, "desktopIntegrationDisabledTitle": {"message": "Brauseri integratsioon ei ole sisse lülitatud"}, "desktopIntegrationDisabledDesc": {"message": "Brauseri integratsioon ei ole Bitwardeni töölaua rakenduses sisse lülitatud. <PERSON><PERSON><PERSON> lülit<PERSON> see töölaua rakenduse seadetes sisse."}, "startDesktopTitle": {"message": "Käivita Bitwardeni töö<PERSON>a rakendus"}, "startDesktopDesc": {"message": "Enne selle funktsiooni sisselülitamist peab käivitama Bitwardeni töölaua rakenduse."}, "errorEnableBiometricTitle": {"message": "Biomeetria sisselülitamine nurjus"}, "errorEnableBiometricDesc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> rakendus tü<PERSON> tege<PERSON>"}, "nativeMessagingInvalidEncryptionDesc": {"message": "Töölaua rakendusel ei õnnestunud turvalist ühenduskanalit luua. <PERSON><PERSON>n proovi uuesti"}, "nativeMessagingInvalidEncryptionTitle": {"message": "Su<PERSON><PERSON> töölaua rakendusega katkes"}, "nativeMessagingWrongUserDesc": {"message": "Töölaua rakenduses on sisse logitud teise kasutajaga. <PERSON><PERSON><PERSON>, et oled mõlemas rakenduses sisse loginud ühe ja sama kontoga."}, "nativeMessagingWrongUserTitle": {"message": "Kontod ei <PERSON>hti"}, "nativeMessagingWrongUserKeyTitle": {"message": "Biometric key missmatch"}, "nativeMessagingWrongUserKeyDesc": {"message": "Biometric unlock failed. The biometric secret key failed to unlock the vault. Please try to set up biometrics again."}, "biometricsNotEnabledTitle": {"message": "Biomeetria ei ole sisse lülitatud"}, "biometricsNotEnabledDesc": {"message": "Biomeetria kasutamiseks brauseris peab esmalt Bitwardeni töölaua rakenduse seadetes biomeetria lubama."}, "biometricsNotSupportedTitle": {"message": "Biomeetriat ei toetata"}, "biometricsNotSupportedDesc": {"message": "Brauseri biomeetria ei ole selles seadmes toetatud"}, "biometricsNotUnlockedTitle": {"message": "User locked or logged out"}, "biometricsNotUnlockedDesc": {"message": "Please unlock this user in the desktop application and try again."}, "biometricsNotAvailableTitle": {"message": "Biometric unlock unavailable"}, "biometricsNotAvailableDesc": {"message": "Biometric unlock is currently unavailable. Please try again later."}, "biometricsFailedTitle": {"message": "Biomeetria nurjus"}, "biometricsFailedDesc": {"message": "Biomeetriaga kinnitamine ebaõnnestus. Kasuta ülemparooli või logi välja. Kui probleem püsib, võta ühendust Bitwardeni toega."}, "nativeMessaginPermissionErrorTitle": {"message": "<PERSON><PERSON> puudub"}, "nativeMessaginPermissionErrorDesc": {"message": "<PERSON><PERSON><PERSON><PERSON> luba, et suhelda Bitwardeni töölaua rakendusega. Selle tõttu ei saa brauseri lisas biomeetriat kasutada. Palun proovi uuesti."}, "nativeMessaginPermissionSidebarTitle": {"message": "Loa taotlemisel ilmnes viga"}, "nativeMessaginPermissionSidebarDesc": {"message": "Seda tegevust ei saa k<PERSON><PERSON><PERSON><PERSON><PERSON> sooritada. Proovi seda sooritada hüpikakna vaates."}, "personalOwnershipSubmitError": {"message": "Ettevõtte poliitika tõttu ei saa sa andmeid oma personaalsesse Hoidlasse salvestada. Vali <PERSON> organisatsioon ja vali mõni saada<PERSON>olevatest Kogumikest."}, "personalOwnershipPolicyInEffect": {"message": "Organisatsiooni poliitika on seadnud omaniku valikutele piirangu."}, "personalOwnershipPolicyInEffectImports": {"message": "An organization policy has blocked importing items into your individual vault."}, "domainsTitle": {"message": "Domains", "description": "A category title describing the concept of web domains"}, "blockedDomains": {"message": "Blocked domains"}, "learnMoreAboutBlockedDomains": {"message": "Learn more about blocked domains"}, "excludedDomains": {"message": "Väljajäetud domeenid"}, "excludedDomainsDesc": {"message": "Nendel domeenidel Bitwarden paroolide salvestamise valikut ei paku. Muudatuste jõustamiseks pead lehekülge värskendama."}, "excludedDomainsDescAlt": {"message": "Bitwarden will not ask to save login details for these domains for all logged in accounts. You must refresh the page for changes to take effect."}, "blockedDomainsDesc": {"message": "Autofill and other related features will not be offered for these websites. You must refresh the page for changes to take effect."}, "autofillBlockedNoticeV2": {"message": "Autofill is blocked for this website."}, "autofillBlockedNoticeGuidance": {"message": "Change this in settings"}, "change": {"message": "Change"}, "changePassword": {"message": "Change password", "description": "Change password button for browser at risk notification on login."}, "changeButtonTitle": {"message": "Change password - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "atRiskPassword": {"message": "At-risk password"}, "atRiskPasswords": {"message": "At-risk passwords"}, "atRiskPasswordDescSingleOrg": {"message": "$ORGANIZATION$ is requesting you change one password because it is at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}}, "atRiskPasswordsDescSingleOrgPlural": {"message": "$ORGANIZATION$ is requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}, "count": {"content": "$2", "example": "2"}}}, "atRiskPasswordsDescMultiOrgPlural": {"message": "Your organizations are requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "atRiskChangePrompt": {"message": "Your password for this site is at-risk. $ORGANIZATION$ has requested that you change it.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}, "description": "Notification body when a login triggers an at-risk password change request and the change password domain is known."}, "atRiskNavigatePrompt": {"message": "$ORGANIZATION$ wants you to change this password because it is at-risk. Navigate to your account settings to change the password.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}, "description": "Notification body when a login triggers an at-risk password change request and no change password domain is provided."}, "reviewAndChangeAtRiskPassword": {"message": "Review and change one at-risk password"}, "reviewAndChangeAtRiskPasswordsPlural": {"message": "Review and change $COUNT$ at-risk passwords", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "changeAtRiskPasswordsFaster": {"message": "Change at-risk passwords faster"}, "changeAtRiskPasswordsFasterDesc": {"message": "Update your settings so you can quickly autofill your passwords and generate new ones"}, "reviewAtRiskLogins": {"message": "Review at-risk logins"}, "reviewAtRiskPasswords": {"message": "Review at-risk passwords"}, "reviewAtRiskLoginsSlideDesc": {"message": "Your organization passwords are at-risk because they are weak, reused, and/or exposed.", "description": "Description of the review at-risk login slide on the at-risk password page carousel"}, "reviewAtRiskLoginSlideImgAltPeriod": {"message": "Illustration of a list of logins that are at-risk."}, "generatePasswordSlideDesc": {"message": "Quickly generate a strong, unique password with the Bitwarden autofill menu on the at-risk site.", "description": "Description of the generate password slide on the at-risk password page carousel"}, "generatePasswordSlideImgAltPeriod": {"message": "Illustration of the Bitwarden autofill menu displaying a generated password."}, "updateInBitwarden": {"message": "Update in Bitwarden"}, "updateInBitwardenSlideDesc": {"message": "Bitwarden will then prompt you to update the password in the password manager.", "description": "Description of the update in Bitwarden slide on the at-risk password page carousel"}, "updateInBitwardenSlideImgAltPeriod": {"message": "Illustration of a Bitwarden’s notification prompting the user to update the login."}, "turnOnAutofill": {"message": "Turn on autofill"}, "turnedOnAutofill": {"message": "Turned on autofill"}, "dismiss": {"message": "<PERSON><PERSON><PERSON>"}, "websiteItemLabel": {"message": "Website $number$ (URI)", "placeholders": {"number": {"content": "$1", "example": "3"}}}, "excludedDomainsInvalidDomain": {"message": "$DOMAIN$ ei ole õige domeen.", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "blockedDomainsSavedSuccess": {"message": "Blocked domain changes saved"}, "excludedDomainsSavedSuccess": {"message": "Excluded domain changes saved"}, "limitSendViews": {"message": "Limit views"}, "limitSendViewsHint": {"message": "No one can view this Send after the limit is reached.", "description": "Displayed under the limit views field on Send"}, "limitSendViewsCount": {"message": "$ACCESSCOUNT$ views left", "description": "Displayed under the limit views field on Send", "placeholders": {"accessCount": {"content": "$1", "example": "2"}}}, "send": {"message": "Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDetails": {"message": "Send details", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendTypeText": {"message": "Tekst"}, "sendTypeTextToShare": {"message": "Text to share"}, "sendTypeFile": {"message": "Fail"}, "allSends": {"message": "<PERSON><PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "maxAccessCountReached": {"message": "Max access count reached", "description": "This text will be displayed after a Send has been accessed the maximum amount of times."}, "hideTextByDefault": {"message": "Hide text by default"}, "expired": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "passwordProtected": {"message": "Parooliga kaitstud"}, "copyLink": {"message": "Copy link"}, "copySendLink": {"message": "Kopeeri Sendi link", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "removePassword": {"message": "<PERSON><PERSON><PERSON>"}, "delete": {"message": "Kustuta"}, "removedPassword": {"message": "<PERSON><PERSON><PERSON><PERSON>i"}, "deletedSend": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLink": {"message": "Sendi link", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "disabled": {"message": "Keelatud"}, "removePasswordConfirmation": {"message": "<PERSON><PERSON> kindlasti selle parooli eemaldada?"}, "deleteSend": {"message": "Kustuta Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendConfirmation": {"message": "<PERSON><PERSON> t<PERSON> selle Sendi kustutada?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendPermanentConfirmation": {"message": "Are you sure you want to permanently delete this Send?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editSend": {"message": "<PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deletionDate": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "deletionDateDescV2": {"message": "The Send will be permanently deleted on this date.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "expirationDate": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "oneDay": {"message": "1 päev"}, "days": {"message": "$DAYS$ päeva", "placeholders": {"days": {"content": "$1", "example": "2"}}}, "custom": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sendPasswordDescV3": {"message": "Add an optional password for recipients to access this Send.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createSend": {"message": "Lo<PERSON> uus Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "newPassword": {"message": "<PERSON><PERSON>"}, "sendDisabled": {"message": "Send on väljalülitatud", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDisabledWarning": {"message": "Ettevõtte poliitika kohaselt saad ainult olemasolevat Sendi kustutada.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSend": {"message": "Send on loodud", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSendSuccessfully": {"message": "Send created successfully!", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHoursSingle": {"message": "The Send will be available to anyone with the link for the next 1 hour.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHours": {"message": "The Send will be available to anyone with the link for the next $HOURS$ hours.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"hours": {"content": "$1", "example": "5"}}}, "sendExpiresInDaysSingle": {"message": "The Send will be available to anyone with the link for the next 1 day.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInDays": {"message": "The Send will be available to anyone with the link for the next $DAYS$ days.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"days": {"content": "$1", "example": "5"}}}, "sendLinkCopied": {"message": "Send link copied", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editedSend": {"message": "Muudetud", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogText": {"message": "Pop out extension?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogDesc": {"message": "To create a file Send, you need to pop out the extension to a new window.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLinuxChromiumFileWarning": {"message": "Faili valimiseks ava rakendus külgribal (kui see on võimalik) või kasuta h<PERSON>, klikkides sellel bänneril."}, "sendFirefoxFileWarning": {"message": "Faili valimiseks läbi Firefoxi ava Bitwardeni rakendus Firefoxi külgribal või kasuta hüpi<PERSON>kent (klikkides sellel bänneril)."}, "sendSafariFileWarning": {"message": "Faili valimiseks läbi Safari kasuta Bitwardeni hüpikakent (klikkides sellel bänneril)."}, "popOut": {"message": "Pop out"}, "sendFileCalloutHeader": {"message": "<PERSON><PERSON>"}, "expirationDateIsInvalid": {"message": "Valitud aegumiskuupäev ei ole õ<PERSON>."}, "deletionDateIsInvalid": {"message": "Valitud kustutamise kuup<PERSON>ev ei ole õ<PERSON>."}, "expirationDateAndTimeRequired": {"message": "<PERSON><PERSON><PERSON><PERSON> on aegumiskuupäev ja kellaaeg."}, "deletionDateAndTimeRequired": {"message": "<PERSON><PERSON><PERSON><PERSON> on kustuta<PERSON> kuup<PERSON>ev ja kellaaeg."}, "dateParsingError": {"message": "Kustutamis- ja aegumiskuupäevade salvestamisel ilmnes tõrge."}, "hideYourEmail": {"message": "Hide your email address from viewers."}, "passwordPrompt": {"message": "Nõutav on ülemparool"}, "passwordConfirmation": {"message": "Ülemparooli kinnitamine"}, "passwordConfirmationDesc": {"message": "See tegevus on kaitstud. Jätkamiseks sisesta oma ü<PERSON>parool."}, "emailVerificationRequired": {"message": "<PERSON><PERSON><PERSON> on e-posti kinnitamine"}, "emailVerifiedV2": {"message": "Email verified"}, "emailVerificationRequiredDesc": {"message": "<PERSON><PERSON> ka<PERSON>tam<PERSON>ks pead kinnitama oma e-posti aadressi. <PERSON>ad seda teha ve<PERSON>."}, "updatedMasterPassword": {"message": "<PERSON><PERSON><PERSON>"}, "updateMasterPassword": {"message": "Ülemparooli uuendamine"}, "updateMasterPasswordWarning": {"message": "Organisatsiooni administraator muutis hiljuti sinu ülemparooli. Hoidlale ligi pääsemiseks pead seda nüüd uuendama. Jätkates logitakse sind käimasolevast sessioonist v<PERSON><PERSON><PERSON>, mis<PERSON><PERSON><PERSON> nõutakse uuesti sisselogimist. Teistes seadmetes olevad aktiivsed sessioonid jäävad aktiivseks kuni üheks tunniks."}, "updateWeakMasterPasswordWarning": {"message": "Sinu ülemparool ei vasta ühele või rohkemale organisatsiooni poolt seatud poliitikale. Hoidlale ligipääsemiseks pead oma ülemaprooli uuendama. Jätkamisel logitakse sind praegusest sessioonist v<PERSON><PERSON><PERSON>, mistõttu pead uuesti sisse logima. Teistes seadmetes olevad aktiivsed sessioonid aeguvad umbes ühe tunni jooksul."}, "tdeDisabledMasterPasswordRequired": {"message": "Your organization has disabled trusted device encryption. Please set a master password to access your vault."}, "resetPasswordPolicyAutoEnroll": {"message": "Automaatne liitumine"}, "resetPasswordAutoEnrollInviteWarning": {"message": "Selle organisatsiooni poliitika kohaselt liidetakse sind automaatselt ülemparooli lähtestamise funktsiooniga. Liitumisel saavad organisatsiooni administraatorid sinu ülemparooli muuta."}, "selectFolder": {"message": "<PERSON>i ka<PERSON>..."}, "noFoldersFound": {"message": "No folders found", "description": "Used as a message within the notification bar when no folders are found"}, "orgPermissionsUpdatedMustSetPassword": {"message": "Your organization permissions were updated, requiring you to set a master password.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "orgRequiresYouToSetPassword": {"message": "Your organization requires you to set a master password.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "cardMetrics": {"message": "out of $TOTAL$", "placeholders": {"total": {"content": "$1", "example": "5"}}}, "verificationRequired": {"message": "Verification required", "description": "Default title for the user verification dialog."}, "hours": {"message": "Tundi"}, "minutes": {"message": "<PERSON><PERSON><PERSON>"}, "vaultTimeoutPolicyAffectingOptions": {"message": "Enterprise policy requirements have been applied to your timeout options"}, "vaultTimeoutPolicyInEffect": {"message": "Organisatsiooni poliitikad mõjutavad sinu hoidla ajalõppu. Maksimaalne lubatud hoidla ajalõpp on $HOURS$ tund(i) ja $MINUTES$ minut(it)", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyInEffect1": {"message": "$HOURS$ hour(s) and $MINUTES$ minute(s) maximum.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyMaximumError": {"message": "Timeout exceeds the restriction set by your organization: $HOURS$ hour(s) and $MINUTES$ minute(s) maximum", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyWithActionInEffect": {"message": "Organisatsiooni poliitikad mõjutavad sinu hoidla ajalõppu. Maksimaalne lubatud hoidla ajalõpp on $HOURS$ tund(i) ja $MINUTES$ minut(it). Sinu hoidla ajalõpu tegevus on $ACTION$.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}, "action": {"content": "$3", "example": "Lock"}}}, "vaultTimeoutActionPolicyInEffect": {"message": "Organisatsiooni poliitika on sinu hoidla ajalõpu tegevuse seadistanud $ACTION$ peale.", "placeholders": {"action": {"content": "$1", "example": "Lock"}}}, "vaultTimeoutTooLarge": {"message": "Valitud ho<PERSON> a<PERSON> ei ole organisatsiooni poolt määratud reeglitega k<PERSON>kõlas."}, "vaultExportDisabled": {"message": "Hoidla eksportimine on väljalülitatud"}, "personalVaultExportPolicyInEffect": {"message": "Üks või enam organisatsiooni poliitikat ei võimalda sul oma personaalset hoidlat eksportida."}, "copyCustomFieldNameInvalidElement": {"message": "Korrektset välja nime ei õ<PERSON>ud tuvastada. Proovi HTML koodi inspekteerimist."}, "copyCustomFieldNameNotUnique": {"message": "Unikaalset identifikaatorit ei leitud."}, "removeMasterPasswordForOrganizationUserKeyConnector": {"message": "A master password is no longer required for members of the following organization. Please confirm the domain below with your organization administrator."}, "organizationName": {"message": "Organization name"}, "keyConnectorDomain": {"message": "Key Connector domain"}, "leaveOrganization": {"message": "Lahku organisatsioonist"}, "removeMasterPassword": {"message": "<PERSON><PERSON><PERSON>"}, "removedMasterPassword": {"message": "Ülemparool on eemaldatud."}, "leaveOrganizationConfirmation": {"message": "Kas oled kindel, et soovid sellest organisatsioonist lahkuda?"}, "leftOrganization": {"message": "Oled organisatsioonist la<PERSON>."}, "toggleCharacterCount": {"message": "<PERSON><PERSON><PERSON> kirja<PERSON><PERSON> hulka"}, "sessionTimeout": {"message": "Sessioon on aegunud. Palun mine tagasi ja proovi uuesti sisse logida."}, "exportingPersonalVaultTitle": {"message": "Personaalse hoidla eksportimine"}, "exportingIndividualVaultDescription": {"message": "Ainult e-postiga $EMAIL$ seonduvad kirjed eksporditakse. Organisatsiooni kirjeid ei kaasata. Samuti ei kaasata organisatsiooniga seonduvaid manuseid.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingIndividualVaultWithAttachmentsDescription": {"message": "Only the individual vault items including attachments associated with $EMAIL$ will be exported. Organization vault items will not be included", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingOrganizationVaultTitle": {"message": "Exporting organization vault"}, "exportingOrganizationVaultDesc": {"message": "Only the organization vault associated with $ORGANIZATION$ will be exported. Items in individual vaults or other organizations will not be included.", "placeholders": {"organization": {"content": "$1", "example": "ACME Moving Co."}}}, "error": {"message": "Viga"}, "decryptionError": {"message": "Decryption error"}, "couldNotDecryptVaultItemsBelow": {"message": "<PERSON><PERSON><PERSON> could not decrypt the vault item(s) listed below."}, "contactCSToAvoidDataLossPart1": {"message": "Contact customer success", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "contactCSToAvoidDataLossPart2": {"message": "to avoid additional data loss.", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "generateUsername": {"message": "<PERSON><PERSON><PERSON>"}, "generateEmail": {"message": "Generate email"}, "spinboxBoundariesHint": {"message": "Value must be between $MIN$ and $MAX$.", "description": "Explains spin box minimum and maximum values to the user", "placeholders": {"min": {"content": "$1", "example": "8"}, "max": {"content": "$2", "example": "128"}}}, "passwordLengthRecommendationHint": {"message": " Use $RECOMMENDED$ characters or more to generate a strong password.", "description": "Appended to `spinboxBoundariesHint` to recommend a length to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "14"}}}, "passphraseNumWordsRecommendationHint": {"message": " Use $RECOMMENDED$ words or more to generate a strong passphrase.", "description": "Appended to `spinboxBoundariesHint` to recommend a number of words to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "6"}}}, "plusAddressedEmail": {"message": "Plussiga e-posti aadress", "description": "Username generator option that appends a random sub-address to the username. For example: <EMAIL>"}, "plusAddressedEmailDesc": {"message": "Kasuta e-posti teenuspakkuja alamadressimise võimalusi."}, "catchallEmail": {"message": "Kogumisaadress"}, "catchallEmailDesc": {"message": "<PERSON><PERSON><PERSON>hist kogumisaadressi."}, "random": {"message": "<PERSON><PERSON><PERSON>"}, "randomWord": {"message": "Juhuslik sõna"}, "websiteName": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimi"}, "service": {"message": "<PERSON><PERSON>"}, "forwardedEmail": {"message": "<PERSON><PERSON><PERSON> alias"}, "forwardedEmailDesc": {"message": "<PERSON><PERSON><PERSON> e-posti alias, ka<PERSON><PERSON><PERSON> selleks välist <PERSON><PERSON><PERSON>."}, "forwarderDomainName": {"message": "Email domain", "description": "Labels the domain name email forwarder service option"}, "forwarderDomainNameHint": {"message": "Choose a domain that is supported by the selected service", "description": "Guidance provided for email forwarding services that support multiple email domains."}, "forwarderError": {"message": "$SERVICENAME$ error: $ERRORMESSAGE$", "description": "Reports an error returned by a forwarding service to the user.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Invalid characters in domain name."}}}, "forwarderGeneratedBy": {"message": "Generated by <PERSON><PERSON><PERSON>.", "description": "Displayed with the address on the forwarding service's configuration screen."}, "forwarderGeneratedByWithWebsite": {"message": "Website: $WEBSITE$. Generated by Bitwarden.", "description": "Displayed with the address on the forwarding service's configuration screen.", "placeholders": {"WEBSITE": {"content": "$1", "example": "www.example.com"}}}, "forwaderInvalidToken": {"message": "Invalid $SERVICENAME$ API token", "description": "Displayed when the user's API token is empty or rejected by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidTokenWithMessage": {"message": "Invalid $SERVICENAME$ API token: $ERRORMESSAGE$", "description": "Displayed when the user's API token is rejected by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwaderInvalidOperation": {"message": "$SERVICENAME$ refused your request. Please contact your service provider for assistance.", "description": "Displayed when the user is forbidden from using the API by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidOperationWithMessage": {"message": "$SERVICENAME$ refused your request: $ERRORMESSAGE$", "description": "Displayed when the user is forbidden from using the API by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwarderNoAccountId": {"message": "Unable to obtain $SERVICENAME$ masked email account ID.", "description": "Displayed when the forwarding service fails to return an account ID.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoDomain": {"message": "Invalid $SERVICENAME$ domain.", "description": "Displayed when the domain is empty or domain authorization failed at the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoUrl": {"message": "Invalid $SERVICENAME$ url.", "description": "Displayed when the url of the forwarding service wasn't supplied.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownError": {"message": "Unknown $SERVICENAME$ error occurred.", "description": "Displayed when the forwarding service failed due to an unknown error.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownForwarder": {"message": "Unknown forwarder: '$SERVICENAME$'.", "description": "Displayed when the forwarding service is not supported.", "placeholders": {"servicename": {"content": "$1", "example": "JustTrust.us"}}}, "hostname": {"message": "<PERSON>i nimi", "description": "Part of a URL."}, "apiAccessToken": {"message": "API ligipääsu märk"}, "apiKey": {"message": "API võti"}, "ssoKeyConnectorError": {"message": "Key Connectori viga: ve<PERSON><PERSON>, et Key Connector on saadaval ja töötab korrektselt."}, "premiumSubcriptionRequired": {"message": "V<PERSON>lik on Premium versioon"}, "organizationIsDisabled": {"message": "Organisatsiooni ligipääs on keelatud."}, "disabledOrganizationFilterError": {"message": "Organisatsiooni alla kuuluvatele kirjetele ei ole ligipääsu. Kontakteeru oma organisatsiooni omanikuga."}, "loggingInTo": {"message": "Sisselogimine läbi $DOMAIN$", "placeholders": {"domain": {"content": "$1", "example": "example.com"}}}, "serverVersion": {"message": "<PERSON><PERSON> versioon"}, "selfHostedServer": {"message": "enda majutatud"}, "thirdParty": {"message": "<PERSON><PERSON><PERSON>"}, "thirdPartyServerMessage": {"message": "Connected to third-party server implementation, $SERVERNAME$. Please verify bugs using the official server, or report them to the third-party server.", "placeholders": {"servername": {"content": "$1", "example": "ThirdPartyServerName"}}}, "lastSeenOn": {"message": "viimati nähtud: $DATE$", "placeholders": {"date": {"content": "$1", "example": "Jun 15, 2015"}}}, "loginWithMasterPassword": {"message": "Logi sisse ülemparooliga"}, "newAroundHere": {"message": "Oled siin uus?"}, "rememberEmail": {"message": "Mäleta e-posti aadressi"}, "loginWithDevice": {"message": "<PERSON><PERSON> sisse seadme kaudu"}, "fingerprintPhraseHeader": {"message": "Unikaalne sõna<PERSON>da"}, "fingerprintMatchInfo": {"message": "<PERSON><PERSON><PERSON>, et hoidla on lahti lukustatud ja unikaalne sõnajada ühtib teiste seadmetega."}, "resendNotification": {"message": "<PERSON><PERSON> märguan<PERSON>"}, "viewAllLogInOptions": {"message": "View all log in options"}, "notificationSentDevice": {"message": "<PERSON>u seadmesse sa<PERSON>ti tea<PERSON>tus."}, "notificationSentDevicePart1": {"message": "Unlock Bitwarden on your device or on the"}, "notificationSentDeviceAnchor": {"message": "web app"}, "notificationSentDevicePart2": {"message": "Make sure the Fingerprint phrase matches the one below before approving."}, "aNotificationWasSentToYourDevice": {"message": "A notification was sent to your device"}, "youWillBeNotifiedOnceTheRequestIsApproved": {"message": "You will be notified once the request is approved"}, "needAnotherOptionV1": {"message": "Need another option?"}, "loginInitiated": {"message": "Sisselogimine on käivitatud"}, "logInRequestSent": {"message": "Request sent"}, "exposedMasterPassword": {"message": "Ülemparool on haavatav"}, "exposedMasterPasswordDesc": {"message": "Ülemparool on varasemalt lekkinud. <PERSON><PERSON><PERSON> konto kaitsmiseks unikaalset parooli. <PERSON><PERSON> kindel, et soovid kasutada varem lekkinud parooli?"}, "weakAndExposedMasterPassword": {"message": "Nõrk ja haavatav ülemparool"}, "weakAndBreachedMasterPasswordDesc": {"message": "Tuvastati nõrk ning andmelekkes lekkinud ülemparool. Kasuta konto paremaks turvamiseks tugevamat parooli. <PERSON><PERSON> kindel, et soovid nõrga parooliga jätkata?"}, "checkForBreaches": {"message": "<PERSON><PERSON>i seda parooli teadaolevatest andmeleketest"}, "important": {"message": "Tähtis:"}, "masterPasswordHint": {"message": "Ülemparooli ei saa taastada, kui sa selle unustama peaksid!"}, "characterMinimum": {"message": "Minimaalselt $LENGTH$ tähemärki", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "autofillPageLoadPolicyActivated": {"message": "Sinu organisatsioon ei võimalda lehe laadimisel automaatset kontoandmete täitmist kasutada."}, "howToAutofill": {"message": "Kuidas automaatselt täita"}, "autofillSelectInfoWithCommand": {"message": "Select an item from this screen, use the shortcut $COMMAND$, or explore other options in settings.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillSelectInfoWithoutCommand": {"message": "Select an item from this screen, or explore other options in settings."}, "gotIt": {"message": "<PERSON>lge"}, "autofillSettings": {"message": "Automaattäite seaded"}, "autofillKeyboardShortcutSectionTitle": {"message": "Autofill shortcut"}, "autofillKeyboardShortcutUpdateLabel": {"message": "Change shortcut"}, "autofillKeyboardManagerShortcutsLabel": {"message": "Manage shortcuts"}, "autofillShortcut": {"message": "Automaattäite klaviatuuri otseteed"}, "autofillLoginShortcutNotSet": {"message": "The autofill login shortcut is not set. Change this in the browser's settings."}, "autofillLoginShortcutText": {"message": "The autofill login shortcut is $COMMAND$. Manage all shortcuts in the browser's settings.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillShortcutTextSafari": {"message": "Vaike automaattäite otsetee on: $COMMAND$.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "opensInANewWindow": {"message": "<PERSON><PERSON><PERSON> u<PERSON> a<PERSON>"}, "rememberThisDeviceToMakeFutureLoginsSeamless": {"message": "Remember this device to make future logins seamless"}, "deviceApprovalRequired": {"message": "Nõutav on seadme kinnitamine. Vali kinnitamise meetod alt:"}, "deviceApprovalRequiredV2": {"message": "Device approval required"}, "selectAnApprovalOptionBelow": {"message": "Select an approval option below"}, "rememberThisDevice": {"message": "<PERSON><PERSON><PERSON><PERSON> seda seadet"}, "uncheckIfPublicDevice": {"message": "<PERSON><PERSON><PERSON>, kui oled aval<PERSON>us seadmes"}, "approveFromYourOtherDevice": {"message": "<PERSON><PERSON><PERSON> teises seadmes"}, "requestAdminApproval": {"message": "<PERSON><PERSON><PERSON> admini kinnitust"}, "ssoIdentifierRequired": {"message": "Nõutav on organisatsiooni SSO identifikaator."}, "creatingAccountOn": {"message": "Creating account on"}, "checkYourEmail": {"message": "Check your email"}, "followTheLinkInTheEmailSentTo": {"message": "Follow the link in the email sent to"}, "andContinueCreatingYourAccount": {"message": "and continue creating your account."}, "noEmail": {"message": "No email?"}, "goBack": {"message": "Go back"}, "toEditYourEmailAddress": {"message": "to edit your email address."}, "eu": {"message": "EL", "description": "European Union"}, "accessDenied": {"message": "Ligipääs keel<PERSON>. Sul pole lubatud seda lehekülge vaadata."}, "general": {"message": "<PERSON><PERSON><PERSON>"}, "display": {"message": "Kuvamine"}, "accountSuccessfullyCreated": {"message": "Konto edukalt loodud!"}, "adminApprovalRequested": {"message": "<PERSON><PERSON><PERSON> on saadetud"}, "adminApprovalRequestSentToAdmins": {"message": "Kinnituspäring saadeti adminile."}, "troubleLoggingIn": {"message": "<PERSON><PERSON> si<PERSON>l on probleeme?"}, "loginApproved": {"message": "<PERSON><PERSON><PERSON><PERSON>ine on kinnitatud"}, "userEmailMissing": {"message": "Kasu<PERSON>ja e-post on puudulik"}, "activeUserEmailNotFoundLoggingYouOut": {"message": "Active user email not found. Logging you out."}, "deviceTrusted": {"message": "Seade on usaldusväärne"}, "trustOrganization": {"message": "Trust organization"}, "trust": {"message": "Trust"}, "doNotTrust": {"message": "Do not trust"}, "organizationNotTrusted": {"message": "Organization is not trusted"}, "emergencyAccessTrustWarning": {"message": "For the security of your account, only confirm if you have granted emergency access to this user and their fingerprint matches what is displayed in their account"}, "orgTrustWarning": {"message": "For the security of your account, only proceed if you are a member of this organization, have account recovery enabled, and the fingerprint displayed below matches the organization's fingerprint."}, "orgTrustWarning1": {"message": "This organization has an Enterprise policy that will enroll you in account recovery. Enrollment will allow organization administrators to change your password. Only proceed if you recognize this organization and the fingerprint phrase displayed below matches the organization's fingerprint."}, "trustUser": {"message": "Trust user"}, "sendsTitleNoItems": {"message": "Send sensitive information safely", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendsBodyNoItems": {"message": "Share files and data securely with anyone, on any platform. Your information will remain end-to-end encrypted while limiting exposure.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "inputRequired": {"message": "<PERSON><PERSON><PERSON> on nõutav."}, "required": {"message": "n<PERSON><PERSON><PERSON>"}, "search": {"message": "<PERSON><PERSON><PERSON>"}, "inputMinLength": {"message": "Sisend peab olema vähemalt $COUNT$ tähemärki pikk.", "placeholders": {"count": {"content": "$1", "example": "8"}}}, "inputMaxLength": {"message": "Sisend ei tohi olla üle $COUNT$ tähemärgi pikkune.", "placeholders": {"count": {"content": "$1", "example": "20"}}}, "inputForbiddenCharacters": {"message": "Järgnevad kirjamärgid pole lubatud: $CHARACTERS$", "placeholders": {"characters": {"content": "$1", "example": "@, #, $, %"}}}, "inputMinValue": {"message": "Sisend peab olema vähemalt $MIN$.", "placeholders": {"min": {"content": "$1", "example": "8"}}}, "inputMaxValue": {"message": "Sisend ei tohi ületada $MAX$.", "placeholders": {"max": {"content": "$1", "example": "100"}}}, "multipleInputEmails": {"message": "Üks või rohkem e-posti on kehtetud"}, "inputTrimValidator": {"message": "Sisend ei tohi koosneda ainult tühikutest.", "description": "Notification to inform the user that a form's input can't contain only whitespace."}, "inputEmail": {"message": "<PERSON>send pole e-posti aadress."}, "fieldsNeedAttention": {"message": "$COUNT$ vä<PERSON><PERSON> nõuab tähelepanu.", "placeholders": {"count": {"content": "$1", "example": "4"}}}, "singleFieldNeedsAttention": {"message": "1 field needs your attention."}, "multipleFieldsNeedAttention": {"message": "$COUNT$ fields need your attention.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "selectPlaceholder": {"message": "-- Vali --"}, "multiSelectPlaceholder": {"message": "-- Filtreeritav tüüp --"}, "multiSelectLoading": {"message": "Valikute hankimine..."}, "multiSelectNotFound": {"message": "Ühtki kirjet ei leitud"}, "multiSelectClearAll": {"message": "Tühjenda kõ<PERSON>"}, "plusNMore": {"message": "+ $QUANTITY$ veel", "placeholders": {"quantity": {"content": "$1", "example": "5"}}}, "submenu": {"message": "Alammenüü"}, "toggleCollapse": {"message": "<PERSON><PERSON><PERSON>", "description": "Toggling an expand/collapse state."}, "aliasDomain": {"message": "<PERSON><PERSON>"}, "passwordRepromptDisabledAutofillOnPageLoad": {"message": "Items with master password re-prompt cannot be autofilled on page load. Autofill on page load turned off.", "description": "Toast message for describing that master password re-prompt cannot be autofilled on page load."}, "autofillOnPageLoadSetToDefault": {"message": "Autofill on page load set to use default setting.", "description": "Toast message for informing the user that autofill on page load has been set to the default setting."}, "turnOffMasterPasswordPromptToEditField": {"message": "Turn off master password re-prompt to edit this field", "description": "Message appearing below the autofill on load message when master password reprompt is set for a vault item."}, "toggleSideNavigation": {"message": "Toggle side navigation"}, "skipToContent": {"message": "Skip to content"}, "bitwardenOverlayButton": {"message": "Bitwarden autofill menu button", "description": "Page title for the iframe containing the overlay button"}, "toggleBitwardenVaultOverlay": {"message": "Toggle Bitwarden autofill menu", "description": "Screen reader and tool tip label for the overlay button"}, "bitwardenVault": {"message": "Bitwarden autofill menu", "description": "Page title in overlay"}, "unlockYourAccountToViewMatchingLogins": {"message": "Unlock your account to view matching logins", "description": "Text to display in overlay when the account is locked."}, "unlockYourAccountToViewAutofillSuggestions": {"message": "Unlock your account to view autofill suggestions", "description": "Text to display in overlay when the account is locked."}, "unlockAccount": {"message": "Unlock account", "description": "Button text to display in overlay when the account is locked."}, "unlockAccountAria": {"message": "Unlock your account, opens in a new window", "description": "Screen reader text (aria-label) for unlock account button in overlay"}, "totpCodeAria": {"message": "Time-based One-Time Password Verification Code", "description": "Aria label for the totp code displayed in the inline menu for autofill"}, "totpSecondsSpanAria": {"message": "Time remaining before current TOTP expires", "description": "Aria label for the totp seconds displayed in the inline menu for autofill"}, "fillCredentialsFor": {"message": "Fill credentials for", "description": "Screen reader text for when overlay item is in focused"}, "partialUsername": {"message": "Partial username", "description": "Screen reader text for when a login item is focused where a partial username is displayed. SR will announce this phrase before reading the text of the partial username"}, "noItemsToShow": {"message": "No items to show", "description": "Text to show in overlay if there are no matching items"}, "newItem": {"message": "New item", "description": "Button text to display in overlay when there are no matching items"}, "addNewVaultItem": {"message": "Add new vault item", "description": "Screen reader text (aria-label) for new item button in overlay"}, "newLogin": {"message": "New login", "description": "Button text to display within inline menu when there are no matching items on a login field"}, "addNewLoginItemAria": {"message": "Add new vault login item, opens in a new window", "description": "Screen reader text (aria-label) for new login button within inline menu"}, "newCard": {"message": "New card", "description": "Button text to display within inline menu when there are no matching items on a credit card field"}, "addNewCardItemAria": {"message": "Add new vault card item, opens in a new window", "description": "Screen reader text (aria-label) for new card button within inline menu"}, "newIdentity": {"message": "New identity", "description": "Button text to display within inline menu when there are no matching items on an identity field"}, "addNewIdentityItemAria": {"message": "Add new vault identity item, opens in a new window", "description": "Screen reader text (aria-label) for new identity button within inline menu"}, "bitwardenOverlayMenuAvailable": {"message": "Bitwarden autofill menu available. Press the down arrow key to select.", "description": "Screen reader text for announcing when the overlay opens on the page"}, "turnOn": {"message": "Turn on"}, "ignore": {"message": "Ignore"}, "importData": {"message": "Import data", "description": "Used for the header of the import dialog, the import button and within the file-password-prompt"}, "importError": {"message": "Import error"}, "importErrorDesc": {"message": "There was a problem with the data you tried to import. Please resolve the errors listed below in your source file and try again."}, "resolveTheErrorsBelowAndTryAgain": {"message": "Resolve the errors below and try again."}, "description": {"message": "Description"}, "importSuccess": {"message": "Data successfully imported"}, "importSuccessNumberOfItems": {"message": "A total of $AMOUNT$ items were imported.", "placeholders": {"amount": {"content": "$1", "example": "2"}}}, "tryAgain": {"message": "Try again"}, "verificationRequiredForActionSetPinToContinue": {"message": "Verification required for this action. Set a PIN to continue."}, "setPin": {"message": "Set PIN"}, "verifyWithBiometrics": {"message": "Verify with biometrics"}, "awaitingConfirmation": {"message": "Awaiting confirmation"}, "couldNotCompleteBiometrics": {"message": "Could not complete biometrics."}, "needADifferentMethod": {"message": "Need a different method?"}, "useMasterPassword": {"message": "Use master password"}, "usePin": {"message": "Use PIN"}, "useBiometrics": {"message": "Use biometrics"}, "enterVerificationCodeSentToEmail": {"message": "Enter the verification code that was sent to your email."}, "resendCode": {"message": "Resend code"}, "total": {"message": "Total"}, "importWarning": {"message": "You are importing data to $ORGANIZATION$. Your data may be shared with members of this organization. Do you want to proceed?", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "duoHealthCheckResultsInNullAuthUrlError": {"message": "Error connecting with the Duo service. Use a different two-step login method or contact Duo for assistance."}, "duoRequiredForAccount": {"message": "Duo two-step login is required for your account."}, "popoutExtension": {"message": "Popout extension"}, "launchDuo": {"message": "Launch Duo"}, "importFormatError": {"message": "Data is not formatted correctly. Please check your import file and try again."}, "importNothingError": {"message": "Nothing was imported."}, "importEncKeyError": {"message": "Error decrypting the exported file. Your encryption key does not match the encryption key used export the data."}, "invalidFilePassword": {"message": "Invalid file password, please use the password you entered when you created the export file."}, "destination": {"message": "Destination"}, "learnAboutImportOptions": {"message": "Learn about your import options"}, "selectImportFolder": {"message": "Select a folder"}, "selectImportCollection": {"message": "Select a collection"}, "importTargetHint": {"message": "Select this option if you want the imported file contents moved to a $DESTINATION$", "description": "Located as a hint under the import target. Will be appended by either folder or collection, depending if the user is importing into an individual or an organizational vault.", "placeholders": {"destination": {"content": "$1", "example": "folder or collection"}}}, "importUnassignedItemsError": {"message": "File contains unassigned items."}, "selectFormat": {"message": "Select the format of the import file"}, "selectImportFile": {"message": "Select the import file"}, "chooseFile": {"message": "Choose <PERSON>"}, "noFileChosen": {"message": "No file chosen"}, "orCopyPasteFileContents": {"message": "or copy/paste the import file contents"}, "instructionsFor": {"message": "$NAME$ Instructions", "description": "The title for the import tool instructions.", "placeholders": {"name": {"content": "$1", "example": "LastPass (csv)"}}}, "confirmVaultImport": {"message": "Confirm vault import"}, "confirmVaultImportDesc": {"message": "This file is password-protected. Please enter the file password to import data."}, "confirmFilePassword": {"message": "Confirm file password"}, "exportSuccess": {"message": "Vault data exported"}, "typePasskey": {"message": "Passkey"}, "accessing": {"message": "Accessing"}, "loggedInExclamation": {"message": "Logged in!"}, "passkeyNotCopied": {"message": "Pääsukoodi ei kopeerita"}, "passkeyNotCopiedAlert": {"message": "Pääsukoodi ei kopeerita kloonitud kirjele. <PERSON>d kindel, et soovid jätkata?"}, "passkeyFeatureIsNotImplementedForAccountsWithoutMasterPassword": {"message": "Verification required by the initiating site. This feature is not yet implemented for accounts without master password."}, "logInWithPasskeyQuestion": {"message": "Log in with passkey?"}, "passkeyAlreadyExists": {"message": "A passkey already exists for this application."}, "noPasskeysFoundForThisApplication": {"message": "No passkeys found for this application."}, "noMatchingPasskeyLogin": {"message": "You do not have a matching login for this site."}, "noMatchingLoginsForSite": {"message": "No matching logins for this site"}, "searchSavePasskeyNewLogin": {"message": "Search or save passkey as new login"}, "confirm": {"message": "Confirm"}, "savePasskey": {"message": "Save passkey"}, "savePasskeyNewLogin": {"message": "Save passkey as new login"}, "chooseCipherForPasskeySave": {"message": "Choose a login to save this passkey to"}, "chooseCipherForPasskeyAuth": {"message": "Choose a passkey to log in with"}, "passkeyItem": {"message": "<PERSON><PERSON> Item"}, "overwritePasskey": {"message": "Overwrite passkey?"}, "overwritePasskeyAlert": {"message": "This item already contains a passkey. Are you sure you want to overwrite the current passkey?"}, "featureNotSupported": {"message": "Feature not yet supported"}, "yourPasskeyIsLocked": {"message": "Authentication required to use passkey. Verify your identity to continue."}, "multifactorAuthenticationCancelled": {"message": "Multifactor authentication cancelled"}, "noLastPassDataFound": {"message": "No LastPass data found"}, "incorrectUsernameOrPassword": {"message": "Incorrect username or password"}, "incorrectPassword": {"message": "Incorrect password"}, "incorrectCode": {"message": "Incorrect code"}, "incorrectPin": {"message": "Incorrect PIN"}, "multifactorAuthenticationFailed": {"message": "Multifactor authentication failed"}, "includeSharedFolders": {"message": "Include shared folders"}, "lastPassEmail": {"message": "LastPass Email"}, "importingYourAccount": {"message": "Importing your account..."}, "lastPassMFARequired": {"message": "LastPass multifactor authentication required"}, "lastPassMFADesc": {"message": "Enter your one-time passcode from your authentication app"}, "lastPassOOBDesc": {"message": "Approve the login request in your authentication app or enter a one-time passcode."}, "passcode": {"message": "Passcode"}, "lastPassMasterPassword": {"message": "LastPass master password"}, "lastPassAuthRequired": {"message": "LastPass authentication required"}, "awaitingSSO": {"message": "Awaiting SSO authentication"}, "awaitingSSODesc": {"message": "Please continue to log in using your company credentials."}, "seeDetailedInstructions": {"message": "See detailed instructions on our help site at", "description": "This is followed a by a hyperlink to the help website."}, "importDirectlyFromLastPass": {"message": "Import directly from LastPass"}, "importFromCSV": {"message": "Import from CSV"}, "lastPassTryAgainCheckEmail": {"message": "Try again or look for an email from LastPass to verify it's you."}, "collection": {"message": "Collection"}, "lastPassYubikeyDesc": {"message": "Insert the YubiKey associated with your LastPass account into your computer's USB port, then touch its button."}, "switchAccount": {"message": "Switch account"}, "switchAccounts": {"message": "Switch accounts"}, "switchToAccount": {"message": "Switch to account"}, "activeAccount": {"message": "Active account"}, "bitwardenAccount": {"message": "Bitwarden account"}, "availableAccounts": {"message": "Available accounts"}, "accountLimitReached": {"message": "Account limit reached. Log out of an account to add another."}, "active": {"message": "active"}, "locked": {"message": "locked"}, "unlocked": {"message": "unlocked"}, "server": {"message": "server"}, "hostedAt": {"message": "hosted at"}, "useDeviceOrHardwareKey": {"message": "Use your device or hardware key"}, "justOnce": {"message": "Just once"}, "alwaysForThisSite": {"message": "Always for this site"}, "domainAddedToExcludedDomains": {"message": "$DOMAIN$ added to excluded domains.", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "commonImportFormats": {"message": "Common formats", "description": "Label indicating the most common import formats"}, "confirmContinueToBrowserSettingsTitle": {"message": "Continue to browser settings?", "description": "Title for dialog which asks if the user wants to proceed to a relevant browser settings page"}, "confirmContinueToHelpCenter": {"message": "Continue to Help Center?", "description": "Title for dialog which asks if the user wants to proceed to a relevant Help Center page"}, "confirmContinueToHelpCenterPasswordManagementContent": {"message": "Change your browser's autofill and password management settings.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser password management settings"}, "confirmContinueToHelpCenterKeyboardShortcutsContent": {"message": "You can view and set extension shortcuts in your browser's settings.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser keyboard shortcut settings"}, "confirmContinueToBrowserPasswordManagementSettingsContent": {"message": "Change your browser's autofill and password management settings.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's password management settings page"}, "confirmContinueToBrowserKeyboardShortcutSettingsContent": {"message": "You can view and set extension shortcuts in your browser's settings.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's keyboard shortcut settings page"}, "overrideDefaultBrowserAutofillTitle": {"message": "Make <PERSON><PERSON><PERSON> your default password manager?", "description": "Dialog title facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutofillDescription": {"message": "Ignoring this option may cause conflicts between Bitwarden autofill suggestions and your browser's.", "description": "Dialog message facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutoFillSettings": {"message": "Make Bitwarden your default password manager", "description": "Label for the setting that allows overriding the default browser autofill settings"}, "privacyPermissionAdditionNotGrantedTitle": {"message": "Unable to set <PERSON><PERSON><PERSON> as the default password manager", "description": "Title for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "privacyPermissionAdditionNotGrantedDescription": {"message": "You must grant browser privacy permissions to Bitwarden to set it as the default password manager.", "description": "Description for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "makeDefault": {"message": "Make default", "description": "Button text for the setting that allows overriding the default browser autofill settings"}, "saveCipherAttemptSuccess": {"message": "Credentials saved successfully!", "description": "Notification message for when saving credentials has succeeded."}, "passwordSaved": {"message": "Password saved!", "description": "Notification message for when saving credentials has succeeded."}, "updateCipherAttemptSuccess": {"message": "Credentials updated successfully!", "description": "Notification message for when updating credentials has succeeded."}, "passwordUpdated": {"message": "Password updated!", "description": "Notification message for when updating credentials has succeeded."}, "saveCipherAttemptFailed": {"message": "Error saving credentials. Check console for details.", "description": "Notification message for when saving credentials has failed."}, "success": {"message": "Success"}, "removePasskey": {"message": "<PERSON><PERSON><PERSON>"}, "passkeyRemoved": {"message": "Pää<PERSON><PERSON><PERSON><PERSON> on eemaldatud"}, "autofillSuggestions": {"message": "Autofill suggestions"}, "itemSuggestions": {"message": "Suggested items"}, "autofillSuggestionsTip": {"message": "Save a login item for this site to autofill"}, "yourVaultIsEmpty": {"message": "Your vault is empty"}, "noItemsMatchSearch": {"message": "No items match your search"}, "clearFiltersOrTryAnother": {"message": "Clear filters or try another search term"}, "copyInfoTitle": {"message": "Copy info - $ITEMNAME$", "description": "Title for a button that opens a menu with options to copy information from an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "copyNoteTitle": {"message": "Copy Note - $ITEMNAME$", "description": "Title for a button copies a note to the clipboard.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Note Item"}}}, "moreOptionsLabel": {"message": "More options, $ITEMNAME$", "description": "Aria label for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "moreOptionsTitle": {"message": "More options - $ITEMNAME$", "description": "Title for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitle": {"message": "View item - $ITEMNAME$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitleWithField": {"message": "View item - $ITEMNAME$ - $FIELD$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "autofillTitle": {"message": "Autofill - $ITEMNAME$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "autofillTitleWithField": {"message": "Autofill - $ITEMNAME$ - $FIELD$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "copyFieldValue": {"message": "Copy $FIELD$, $VALUE$", "description": "Title for a button that copies a field value to the clipboard.", "placeholders": {"field": {"content": "$1", "example": "Username"}, "value": {"content": "$2", "example": "Foo"}}}, "noValuesToCopy": {"message": "No values to copy"}, "assignToCollections": {"message": "Assign to collections"}, "copyEmail": {"message": "Copy email"}, "copyPhone": {"message": "Copy phone"}, "copyAddress": {"message": "Copy address"}, "adminConsole": {"message": "<PERSON><PERSON>"}, "accountSecurity": {"message": "Account security"}, "notifications": {"message": "Notifications"}, "appearance": {"message": "Appearance"}, "errorAssigningTargetCollection": {"message": "Error assigning target collection."}, "errorAssigningTargetFolder": {"message": "Error assigning target folder."}, "viewItemsIn": {"message": "View items in $NAME$", "description": "Button to view the contents of a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "backTo": {"message": "Back to $NAME$", "description": "Navigate back to a previous folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "new": {"message": "New"}, "removeItem": {"message": "Remove $NAME$", "description": "Remove a selected option, such as a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "itemsWithNoFolder": {"message": "Items with no folder"}, "itemDetails": {"message": "Item details"}, "itemName": {"message": "Item name"}, "organizationIsDeactivated": {"message": "Organization is deactivated"}, "owner": {"message": "Owner"}, "selfOwnershipLabel": {"message": "You", "description": "Used as a label to indicate that the user is the owner of an item."}, "contactYourOrgAdmin": {"message": "Items in deactivated organizations cannot be accessed. Contact your organization owner for assistance."}, "additionalInformation": {"message": "Additional information"}, "itemHistory": {"message": "Item history"}, "lastEdited": {"message": "Last edited"}, "ownerYou": {"message": "Owner: You"}, "linked": {"message": "Linked"}, "copySuccessful": {"message": "Copy Successful"}, "upload": {"message": "Upload"}, "addAttachment": {"message": "Add attachment"}, "maxFileSizeSansPunctuation": {"message": "Maximum file size is 500 MB"}, "deleteAttachmentName": {"message": "Delete attachment $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadAttachmentName": {"message": "Download $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadBitwarden": {"message": "Download Bitwarden"}, "downloadBitwardenOnAllDevices": {"message": "Download Bitwarden on all devices"}, "getTheMobileApp": {"message": "Get the mobile app"}, "getTheMobileAppDesc": {"message": "Access your passwords on the go with the Bitwarden mobile app."}, "getTheDesktopApp": {"message": "Get the desktop app"}, "getTheDesktopAppDesc": {"message": "Access your vault without a browser, then set up unlock with biometrics to expedite unlocking in both the desktop app and browser extension."}, "downloadFromBitwardenNow": {"message": "Download from bitwarden.com now"}, "getItOnGooglePlay": {"message": "Get it on Google Play"}, "downloadOnTheAppStore": {"message": "Download on the App Store"}, "permanentlyDeleteAttachmentConfirmation": {"message": "Are you sure you want to permanently delete this attachment?"}, "premium": {"message": "Premium"}, "freeOrgsCannotUseAttachments": {"message": "Free organizations cannot use attachments"}, "filters": {"message": "Filters"}, "filterVault": {"message": "Filter vault"}, "filterApplied": {"message": "One filter applied"}, "filterAppliedPlural": {"message": "$COUNT$ filters applied", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "personalDetails": {"message": "Personal details"}, "identification": {"message": "Identification"}, "contactInfo": {"message": "Contact info"}, "downloadAttachment": {"message": "Download - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Your File"}}}, "cardNumberEndsWith": {"message": "card number ends with", "description": "Used within the inline menu to provide an aria description when users are attempting to fill a card cipher."}, "loginCredentials": {"message": "Login credentials"}, "authenticatorKey": {"message": "Authenticator key"}, "autofillOptions": {"message": "Autofill options"}, "websiteUri": {"message": "Website (URI)"}, "websiteUriCount": {"message": "Website (URI) $COUNT$", "description": "Label for an input field that contains a website URI. The input field is part of a list of fields, and the count indicates the position of the field in the list.", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "websiteAdded": {"message": "Website added"}, "addWebsite": {"message": "Add website"}, "deleteWebsite": {"message": "Delete website"}, "defaultLabel": {"message": "Default ($VALUE$)", "description": "A label that indicates the default value for a field with the current default value in parentheses.", "placeholders": {"value": {"content": "$1", "example": "Base domain"}}}, "showMatchDetection": {"message": "Show match detection $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "hideMatchDetection": {"message": "Hide match detection $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "autoFillOnPageLoad": {"message": "Autofill on page load?"}, "cardExpiredTitle": {"message": "Expired card"}, "cardExpiredMessage": {"message": "If you've renewed it, update the card's information"}, "cardDetails": {"message": "Card details"}, "cardBrandDetails": {"message": "$BRAND$ details", "placeholders": {"brand": {"content": "$1", "example": "Visa"}}}, "enableAnimations": {"message": "Enable animations"}, "showAnimations": {"message": "Show animations"}, "addAccount": {"message": "Add account"}, "loading": {"message": "Loading"}, "data": {"message": "Data"}, "passkeys": {"message": "Passkeys", "description": "A section header for a list of passkeys."}, "passwords": {"message": "Passwords", "description": "A section header for a list of passwords."}, "logInWithPasskeyAriaLabel": {"message": "Log in with passkey", "description": "ARIA label for the inline menu button that logs in with a passkey."}, "assign": {"message": "Assign"}, "bulkCollectionAssignmentDialogDescriptionSingular": {"message": "Only organization members with access to these collections will be able to see the item."}, "bulkCollectionAssignmentDialogDescriptionPlural": {"message": "Only organization members with access to these collections will be able to see the items."}, "bulkCollectionAssignmentWarning": {"message": "You have selected $TOTAL_COUNT$ items. You cannot update $READONLY_COUNT$ of the items because you do not have edit permissions.", "placeholders": {"total_count": {"content": "$1", "example": "10"}, "readonly_count": {"content": "$2"}}}, "addField": {"message": "Add field"}, "add": {"message": "Add"}, "fieldType": {"message": "Field type"}, "fieldLabel": {"message": "Field label"}, "textHelpText": {"message": "Use text fields for data like security questions"}, "hiddenHelpText": {"message": "Use hidden fields for sensitive data like a password"}, "checkBoxHelpText": {"message": "Use checkboxes if you'd like to autofill a form's checkbox, like a remember email"}, "linkedHelpText": {"message": "Use a linked field when you are experiencing autofill issues for a specific website."}, "linkedLabelHelpText": {"message": "Enter the the field's html id, name, aria-label, or placeholder."}, "editField": {"message": "Edit field"}, "editFieldLabel": {"message": "Edit $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "deleteCustomField": {"message": "Delete $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "fieldAdded": {"message": "$LABEL$ added", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderToggleButton": {"message": "Reorder $LABEL$. Use arrow key to move item up or down.", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderWebsiteUriButton": {"message": "Reorder website URI. Use arrow key to move item up or down."}, "reorderFieldUp": {"message": "$LABEL$ moved up, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "selectCollectionsToAssign": {"message": "Select collections to assign"}, "personalItemTransferWarningSingular": {"message": "1 item will be permanently transferred to the selected organization. You will no longer own this item."}, "personalItemsTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to the selected organization. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}}}, "personalItemWithOrgTransferWarningSingular": {"message": "1 item will be permanently transferred to $ORG$. You will no longer own this item.", "placeholders": {"org": {"content": "$1", "example": "Organization name"}}}, "personalItemsWithOrgTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to $ORG$. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}, "org": {"content": "$2", "example": "Organization name"}}}, "successfullyAssignedCollections": {"message": "Successfully assigned collections"}, "nothingSelected": {"message": "You have not selected anything."}, "itemsMovedToOrg": {"message": "Items moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "itemMovedToOrg": {"message": "Item moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "reorderFieldDown": {"message": "$LABEL$ moved down, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "itemLocation": {"message": "Item Location"}, "fileSend": {"message": "File Send"}, "fileSends": {"message": "File Sends"}, "textSend": {"message": "Text Send"}, "textSends": {"message": "Text Sends"}, "accountActions": {"message": "Account actions"}, "showNumberOfAutofillSuggestions": {"message": "Show number of login autofill suggestions on extension icon"}, "showQuickCopyActions": {"message": "Show quick copy actions on Vault"}, "systemDefault": {"message": "System default"}, "enterprisePolicyRequirementsApplied": {"message": "Enterprise policy requirements have been applied to this setting"}, "sshPrivateKey": {"message": "Private key"}, "sshPublicKey": {"message": "Public key"}, "sshFingerprint": {"message": "Fingerprint"}, "sshKeyAlgorithm": {"message": "Key type"}, "sshKeyAlgorithmED25519": {"message": "ED25519"}, "sshKeyAlgorithmRSA2048": {"message": "RSA 2048-Bit"}, "sshKeyAlgorithmRSA3072": {"message": "RSA 3072-Bit"}, "sshKeyAlgorithmRSA4096": {"message": "RSA 4096-Bit"}, "retry": {"message": "Retry"}, "vaultCustomTimeoutMinimum": {"message": "Minimum custom timeout is 1 minute."}, "additionalContentAvailable": {"message": "Additional content is available"}, "fileSavedToDevice": {"message": "File saved to device. Manage from your device downloads."}, "showCharacterCount": {"message": "Show character count"}, "hideCharacterCount": {"message": "Hide character count"}, "itemsInTrash": {"message": "Items in trash"}, "noItemsInTrash": {"message": "No items in trash"}, "noItemsInTrashDesc": {"message": "Items you delete will appear here and be permanently deleted after 30 days"}, "trashWarning": {"message": "Items that have been in trash more than 30 days will automatically be deleted"}, "restore": {"message": "Rest<PERSON>"}, "deleteForever": {"message": "Delete forever"}, "noEditPermissions": {"message": "You don't have permission to edit this item"}, "biometricsStatusHelptextUnlockNeeded": {"message": "Biometric unlock is unavailable because PIN or password unlock is required first."}, "biometricsStatusHelptextHardwareUnavailable": {"message": "Biometric unlock is currently unavailable."}, "biometricsStatusHelptextAutoSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextManualSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextDesktopDisconnected": {"message": "Biometric unlock is unavailable because the Bitwarden desktop app is closed."}, "biometricsStatusHelptextNotEnabledInDesktop": {"message": "Biometric unlock is unavailable because it is not enabled for $EMAIL$ in the Bitwarden desktop app.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "biometricsStatusHelptextUnavailableReasonUnknown": {"message": "Biometric unlock is currently unavailable for an unknown reason."}, "unlockVault": {"message": "Unlock your vault in seconds"}, "unlockVaultDesc": {"message": "You can customize your unlock and timeout settings to more quickly access your vault."}, "unlockPinSet": {"message": "Unlock PIN set"}, "authenticating": {"message": "Authenticating"}, "fillGeneratedPassword": {"message": "Fill generated password", "description": "Heading for the password generator within the inline menu"}, "passwordRegenerated": {"message": "Password regenerated", "description": "Notification message for when a password has been regenerated"}, "saveToBitwarden": {"message": "Save to Bitwarden", "description": "Confirmation message for saving a login to Bitwarden"}, "spaceCharacterDescriptor": {"message": "Space", "description": "Represents the space key in screen reader content as a readable word"}, "tildeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ~ key in screen reader content as a readable word"}, "backtickCharacterDescriptor": {"message": "Backtick", "description": "Represents the ` key in screen reader content as a readable word"}, "exclamationCharacterDescriptor": {"message": "Exclamation mark", "description": "Represents the ! key in screen reader content as a readable word"}, "atSignCharacterDescriptor": {"message": "At sign", "description": "Represents the @ key in screen reader content as a readable word"}, "hashSignCharacterDescriptor": {"message": "Hash sign", "description": "Represents the # key in screen reader content as a readable word"}, "dollarSignCharacterDescriptor": {"message": "Dollar sign", "description": "Represents the $ key in screen reader content as a readable word"}, "percentSignCharacterDescriptor": {"message": "Percent sign", "description": "Represents the % key in screen reader content as a readable word"}, "caretCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ^ key in screen reader content as a readable word"}, "ampersandCharacterDescriptor": {"message": "Ampersand", "description": "Represents the & key in screen reader content as a readable word"}, "asteriskCharacterDescriptor": {"message": "Asterisk", "description": "Represents the * key in screen reader content as a readable word"}, "parenLeftCharacterDescriptor": {"message": "Left parenthesis", "description": "Represents the ( key in screen reader content as a readable word"}, "parenRightCharacterDescriptor": {"message": "Right parenthesis", "description": "Represents the ) key in screen reader content as a readable word"}, "hyphenCharacterDescriptor": {"message": "Underscore", "description": "Represents the _ key in screen reader content as a readable word"}, "underscoreCharacterDescriptor": {"message": "Hyphen", "description": "Represents the - key in screen reader content as a readable word"}, "plusCharacterDescriptor": {"message": "Plus", "description": "Represents the + key in screen reader content as a readable word"}, "equalsCharacterDescriptor": {"message": "Equals", "description": "Represents the = key in screen reader content as a readable word"}, "braceLeftCharacterDescriptor": {"message": "Left brace", "description": "Represents the { key in screen reader content as a readable word"}, "braceRightCharacterDescriptor": {"message": "Right brace", "description": "Represents the } key in screen reader content as a readable word"}, "bracketLeftCharacterDescriptor": {"message": "Left bracket", "description": "Represents the [ key in screen reader content as a readable word"}, "bracketRightCharacterDescriptor": {"message": "Right bracket", "description": "Represents the ] key in screen reader content as a readable word"}, "pipeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the | key in screen reader content as a readable word"}, "backSlashCharacterDescriptor": {"message": "Back slash", "description": "Represents the back slash key in screen reader content as a readable word"}, "colonCharacterDescriptor": {"message": "Colon", "description": "Represents the : key in screen reader content as a readable word"}, "semicolonCharacterDescriptor": {"message": "Semicolon", "description": "Represents the ; key in screen reader content as a readable word"}, "doubleQuoteCharacterDescriptor": {"message": "Double quote", "description": "Represents the double quote key in screen reader content as a readable word"}, "singleQuoteCharacterDescriptor": {"message": "Single quote", "description": "Represents the ' key in screen reader content as a readable word"}, "lessThanCharacterDescriptor": {"message": "Less than", "description": "Represents the < key in screen reader content as a readable word"}, "greaterThanCharacterDescriptor": {"message": "Greater than", "description": "Represents the > key in screen reader content as a readable word"}, "commaCharacterDescriptor": {"message": "Comma", "description": "Represents the , key in screen reader content as a readable word"}, "periodCharacterDescriptor": {"message": "Period", "description": "Represents the . key in screen reader content as a readable word"}, "questionCharacterDescriptor": {"message": "Question mark", "description": "Represents the ? key in screen reader content as a readable word"}, "forwardSlashCharacterDescriptor": {"message": "Forward slash", "description": "Represents the / key in screen reader content as a readable word"}, "lowercaseAriaLabel": {"message": "Lowercase"}, "uppercaseAriaLabel": {"message": "Uppercase"}, "generatedPassword": {"message": "Generated password"}, "compactMode": {"message": "Compact mode"}, "beta": {"message": "Beta"}, "extensionWidth": {"message": "Extension width"}, "wide": {"message": "Wide"}, "extraWide": {"message": "Extra wide"}, "sshKeyWrongPassword": {"message": "The password you entered is incorrect."}, "importSshKey": {"message": "Import"}, "confirmSshKeyPassword": {"message": "Confirm password"}, "enterSshKeyPasswordDesc": {"message": "Enter the password for the SSH key."}, "enterSshKeyPassword": {"message": "Enter password"}, "invalidSshKey": {"message": "The SSH key is invalid"}, "sshKeyTypeUnsupported": {"message": "The SSH key type is not supported"}, "importSshKeyFromClipboard": {"message": "Import key from clipboard"}, "sshKeyImported": {"message": "SSH key imported successfully"}, "cannotRemoveViewOnlyCollections": {"message": "You cannot remove collections with View only permissions: $COLLECTIONS$", "placeholders": {"collections": {"content": "$1", "example": "Work, Personal"}}}, "updateDesktopAppOrDisableFingerprintDialogTitle": {"message": "Please update your desktop application"}, "updateDesktopAppOrDisableFingerprintDialogMessage": {"message": "To use biometric unlock, please update your desktop application, or disable fingerprint unlock in the desktop settings."}, "changeAtRiskPassword": {"message": "Change at-risk password"}, "settingsVaultOptions": {"message": "Vault options"}, "emptyVaultDescription": {"message": "The vault protects more than just your passwords. Store secure logins, IDs, cards and notes securely here."}, "introCarouselLabel": {"message": "Welcome to Bitwarden"}, "securityPrioritized": {"message": "Security, prioritized"}, "securityPrioritizedBody": {"message": "Save logins, cards, and identities to your secure vault. Bitwarden uses zero-knowledge, end-to-end encryption to protect what’s important to you."}, "quickLogin": {"message": "Quick and easy login"}, "quickLoginBody": {"message": "Set up biometric unlock and autofill to log into your accounts without typing a single letter."}, "secureUser": {"message": "Level up your logins"}, "secureUserBody": {"message": "Use the generator to create and save strong, unique passwords for all your accounts."}, "secureDevices": {"message": "Your data, when and where you need it"}, "secureDevicesBody": {"message": "Save unlimited passwords across unlimited devices with Bitwarden mobile, browser, and desktop apps."}, "nudgeBadgeAria": {"message": "1 notification"}, "emptyVaultNudgeTitle": {"message": "Import existing passwords"}, "emptyVaultNudgeBody": {"message": "Use the importer to quickly transfer logins to Bitwarden without manually adding them."}, "emptyVaultNudgeButton": {"message": "Import now"}, "hasItemsVaultNudgeTitle": {"message": "Welcome to your vault!"}, "hasItemsVaultNudgeBodyOne": {"message": "Autofill items for the current page"}, "hasItemsVaultNudgeBodyTwo": {"message": "Favorite items for easy access"}, "hasItemsVaultNudgeBodyThree": {"message": "Search your vault for something else"}, "newLoginNudgeTitle": {"message": "Save time with autofill"}, "newLoginNudgeBodyOne": {"message": "Include a", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyBold": {"message": "Website", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyTwo": {"message": "so this login appears as an autofill suggestion.", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newCardNudgeTitle": {"message": "Seamless online checkout"}, "newCardNudgeBody": {"message": "With cards, easily autofill payment forms securely and accurately."}, "newIdentityNudgeTitle": {"message": "Simplify creating accounts"}, "newIdentityNudgeBody": {"message": "With identities, quickly autofill long registration or contact forms."}, "newNoteNudgeTitle": {"message": "Keep your sensitive data safe"}, "newNoteNudgeBody": {"message": "With notes, securely store sensitive data like banking or insurance details."}, "newSshNudgeTitle": {"message": "Developer-friendly SSH access"}, "newSshNudgeBodyOne": {"message": "Store your keys and connect with the SSH agent for fast, encrypted authentication.", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "newSshNudgeBodyTwo": {"message": "Learn more about SSH agent", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "generatorNudgeTitle": {"message": "Quickly create passwords"}, "generatorNudgeBodyOne": {"message": "Easily create strong and unique passwords by clicking on", "description": "Two part message", "example": "Easily create strong and unique passwords by clicking on {icon} to help you keep your logins secure."}, "generatorNudgeBodyTwo": {"message": "to help you keep your logins secure.", "description": "Two part message", "example": "Easily create strong and unique passwords by clicking on {icon} to help you keep your logins secure."}, "generatorNudgeBodyAria": {"message": "Easily create strong and unique passwords by clicking on the Generate password button to help you keep your logins secure.", "description": "Aria label for the body content of the generator nudge"}, "noPermissionsViewPage": {"message": "You do not have permissions to view this page. Try logging in with a different account."}}
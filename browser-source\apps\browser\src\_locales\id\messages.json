{"appName": {"message": "Bitwarden"}, "appLogoLabel": {"message": "Logo Bitwarden"}, "extName": {"message": "<PERSON><PERSON><PERSON> Bitwarden", "description": "Extension name, MUST be less than 40 characters (Safari restriction)"}, "extDesc": {"message": "Bitwarden memudahkan <PERSON>a <PERSON> kata sandi, kata kun<PERSON>, dan informasi sensitif dimanapun <PERSON><PERSON> berada", "description": "Extension description, MUST be less than 112 characters (Safari restriction)"}, "loginOrCreateNewAccount": {"message": "<PERSON><PERSON><PERSON> atau buat akun baru untuk mengakses brankas Anda."}, "inviteAccepted": {"message": "Undangan diterima"}, "createAccount": {"message": "Buat A<PERSON>n"}, "newToBitwarden": {"message": "Baru menggunakan Bitwarden?"}, "logInWithPasskey": {"message": "<PERSON><PERSON><PERSON> dengan kunci sandi"}, "useSingleSignOn": {"message": "<PERSON><PERSON><PERSON> masuk tunggal"}, "welcomeBack": {"message": "Selamat datang kembali"}, "setAStrongPassword": {"message": "Atur sebuah kata sandi yang kuat"}, "finishCreatingYourAccountBySettingAPassword": {"message": "Selesaikan membuat akun Anda dengan mengatur sebuah kata sandi"}, "enterpriseSingleSignOn": {"message": "SSO <PERSON>"}, "cancel": {"message": "<PERSON><PERSON>"}, "close": {"message": "<PERSON><PERSON><PERSON>"}, "submit": {"message": "<PERSON><PERSON>"}, "emailAddress": {"message": "<PERSON><PERSON><PERSON>"}, "masterPass": {"message": "<PERSON><PERSON>"}, "masterPassDesc": {"message": "Kata sandi utama adalah kata sandi yang Anda gunakan untuk mengakses brankas Anda. Sangat penting bahwa Anda tidak lupa kata sandi utama Anda. Tidak ada cara untuk memulihkan kata sandi jika Anda melupakannya."}, "masterPassHintDesc": {"message": "Petunjuk kata sandi utama dapat membantu Anda mengingat kata sandi Anda jika Anda melupakannya."}, "masterPassHintText": {"message": "Ji<PERSON> Anda lupa kata sandi <PERSON>, petunjuk kata sandi dapat dikirim ke surel Anda. Maksimal $CURRENT$/$MAXIMUM$ karakter.", "placeholders": {"current": {"content": "$1", "example": "0"}, "maximum": {"content": "$2", "example": "50"}}}, "reTypeMasterPass": {"message": "<PERSON><PERSON><PERSON> ulang <PERSON> Sand<PERSON> U<PERSON>a"}, "masterPassHint": {"message": "Petunjuk Kat<PERSON> (opsional)"}, "passwordStrengthScore": {"message": "Skor kekuatan kata sandi $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "joinOrganization": {"message": "Bergabung ke organisasi"}, "joinOrganizationName": {"message": "Bergabung ke $ORGANIZATIONNAME$", "placeholders": {"organizationName": {"content": "$1", "example": "My Org Name"}}}, "finishJoiningThisOrganizationBySettingAMasterPassword": {"message": "Se<PERSON>aikan penggabungan ke organisasi ini dengan mengatur sebuah kata sandi utama."}, "tab": {"message": "Tab"}, "vault": {"message": "<PERSON><PERSON><PERSON>"}, "myVault": {"message": "<PERSON><PERSON><PERSON>"}, "allVaults": {"message": "<PERSON><PERSON><PERSON> branka<PERSON>"}, "tools": {"message": "Alat"}, "settings": {"message": "<PERSON><PERSON><PERSON>"}, "currentTab": {"message": "Tab <PERSON>"}, "copyPassword": {"message": "<PERSON><PERSON>"}, "copyPassphrase": {"message": "<PERSON>in frasa sandi"}, "copyNote": {"message": "<PERSON>in <PERSON>"}, "copyUri": {"message": "<PERSON><PERSON>"}, "copyUsername": {"message": "<PERSON><PERSON>"}, "copyNumber": {"message": "<PERSON><PERSON>"}, "copySecurityCode": {"message": "<PERSON><PERSON>"}, "copyName": {"message": "<PERSON><PERSON> nama"}, "copyCompany": {"message": "<PERSON><PERSON>"}, "copySSN": {"message": "<PERSON><PERSON> nomor <PERSON>"}, "copyPassportNumber": {"message": "Salin nomor paspor"}, "copyLicenseNumber": {"message": "Salin nomor lisensi"}, "copyPrivateKey": {"message": "<PERSON><PERSON> kunci pribadi"}, "copyPublicKey": {"message": "<PERSON>in kunci publik"}, "copyFingerprint": {"message": "<PERSON><PERSON> sidik jari"}, "copyCustomField": {"message": "Salin $FIELD$", "placeholders": {"field": {"content": "$1", "example": "Custom field label"}}}, "copyWebsite": {"message": "Salin situs web"}, "copyNotes": {"message": "Salin catatan"}, "copy": {"message": "<PERSON><PERSON>", "description": "Copy to clipboard"}, "fill": {"message": "<PERSON><PERSON><PERSON>", "description": "This string is used on the vault page to indicate autofilling. Horizontal space is limited in the interface here so try and keep translations as concise as possible."}, "autoFill": {"message": "<PERSON><PERSON> otomatis"}, "autoFillLogin": {"message": "Autofill masuk"}, "autoFillCard": {"message": "Autofill kartu"}, "autoFillIdentity": {"message": "Autofill identitas"}, "fillVerificationCode": {"message": "Isikan kode verifikasi"}, "fillVerificationCodeAria": {"message": "Isikan Ko<PERSON> Verifikas<PERSON>", "description": "Aria label for the heading displayed the inline menu for totp code autofill"}, "generatePasswordCopied": {"message": "Membua<PERSON> (tersalin)"}, "copyElementIdentifier": {"message": "<PERSON><PERSON>"}, "noMatchingLogins": {"message": "Tidak ada info masuk yang cocok."}, "noCards": {"message": "Tanpa kartu"}, "noIdentities": {"message": "Tanpa identitas"}, "addLoginMenu": {"message": "Tambahkan Info Masuk"}, "addCardMenu": {"message": "Tambahkan kartu"}, "addIdentityMenu": {"message": "Tambahkan identitas"}, "unlockVaultMenu": {"message": "<PERSON><PERSON> brankas And<PERSON>"}, "loginToVaultMenu": {"message": "Ma<PERSON>k ke brankas Anda"}, "autoFillInfo": {"message": "Tidak ada info masuk yang tersedia untuk mengisi sexara otomatis tab peramban saat ini."}, "addLogin": {"message": "Tambah Info Masuk"}, "addItem": {"message": "Tambah Item"}, "accountEmail": {"message": "<PERSON><PERSON><PERSON> surel"}, "requestHint": {"message": "<PERSON><PERSON>"}, "requestPasswordHint": {"message": "Minta petunjuk kata sandi"}, "enterYourAccountEmailAddressAndYourPasswordHintWillBeSentToYou": {"message": "<PERSON><PERSON><PERSON><PERSON> alamat surel akun Anda dan petunjuk kata sandi Anda akan dikirimkan kepada Anda"}, "getMasterPasswordHint": {"message": "Dapatkan petunjuk sandi utama"}, "continue": {"message": "Lanjutkan"}, "sendVerificationCode": {"message": "<PERSON><PERSON> kode verifikasi ke email Anda"}, "sendCode": {"message": "<PERSON><PERSON>"}, "codeSent": {"message": "<PERSON>de sudah di<PERSON>rim"}, "verificationCode": {"message": "<PERSON><PERSON>"}, "confirmIdentity": {"message": "Konfirmasi identitas Anda untuk melanjutkan."}, "changeMasterPassword": {"message": "Ubah Kata Sandi Utama"}, "continueToWebApp": {"message": "Lanjut ke aplikasi web?"}, "continueToWebAppDesc": {"message": "Temukan fitur Bitwarden lebih melalui aplikasi web."}, "continueToHelpCenter": {"message": "Lanjutkan ke Pusat Bantuan?"}, "continueToHelpCenterDesc": {"message": "P<PERSON>jar<PERSON> lebih lan<PERSON>t penggunaan Bitwarden di Pusat Bantuan."}, "continueToBrowserExtensionStore": {"message": "<PERSON>n<PERSON>t ke pasar ekstensi peramban?"}, "continueToBrowserExtensionStoreDesc": {"message": "Bantu orang lain menemukan apakah Bitwarden sesuai untuk mereka. Kunjungi toko ekstensi peramban Anda dan tinggalkan sebuah ulasan sekarang."}, "changeMasterPasswordOnWebConfirmation": {"message": "<PERSON>a dapat merubah sandi utama di aplikasi Bitwarden web."}, "fingerprintPhrase": {"message": "Frasa Sid<PERSON>", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "yourAccountsFingerprint": {"message": "Frasa sidik jari akun <PERSON>a", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "twoStepLogin": {"message": "Info masuk dua langkah"}, "logOut": {"message": "<PERSON><PERSON><PERSON>"}, "aboutBitwarden": {"message": "Tentang Bitwarden"}, "about": {"message": "Tentang"}, "moreFromBitwarden": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "continueToBitwardenDotCom": {"message": "Lanjutkan ke bitwarden.com?"}, "bitwardenForBusiness": {"message": "Bitwarden untuk Bisnis"}, "bitwardenAuthenticator": {"message": "Pengotentikasi Bitwarden"}, "continueToAuthenticatorPageDesc": {"message": "Pengotentikasi Bitwarden membolehkan Anda untuk menyimpan kunci pengotentikasi dan menghasilkan kode TOTP untuk alur verifikasi dua-langkah. Pelajari lebih lanjut di situs web bitwarden.com"}, "bitwardenSecretsManager": {"message": "<PERSON><PERSON><PERSON>"}, "continueToSecretsManagerPageDesc": {"message": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, dan bagikan secara aman rahasia pengembang dengan Pen<PERSON>ola Rahasia Bitwarden. Pelajari lebih lanjut di situs web bitwarden.com."}, "passwordlessDotDev": {"message": "Passwordless.dev"}, "continueToPasswordlessDotDevPageDesc": {"message": "<PERSON><PERSON>t pengalaman masuk yang lancar dan aman dari kata sandi tradisional dengan Passwordless.dev. <PERSON><PERSON><PERSON><PERSON> lebih lanjut di situs web bitwarden.com."}, "freeBitwardenFamilies": {"message": "Bitwarden <PERSON><PERSON>"}, "freeBitwardenFamiliesPageDesc": {"message": "<PERSON><PERSON> be<PERSON><PERSON> untuk Bitwarden Keluarga Gratis. Tukarkan penawaran ini hari ini di aplikasi web."}, "version": {"message": "<PERSON><PERSON><PERSON>"}, "save": {"message": "Simpan"}, "move": {"message": "Pindah"}, "addFolder": {"message": "Tambah Folder"}, "name": {"message": "<PERSON><PERSON>"}, "editFolder": {"message": "Sunting Folder"}, "editFolderWithName": {"message": "Sunting folder: $FOLDERNAME$", "placeholders": {"foldername": {"content": "$1", "example": "Social"}}}, "newFolder": {"message": "Folder baru"}, "folderName": {"message": "Nama folder"}, "folderHintText": {"message": "Sarangkan sebuah folder dengan menambahkan nama folder induk diikuti dengan sebuah \"/\". Contoh: Sosial/Forum"}, "noFoldersAdded": {"message": "Tidak ada folder yang ditambahkan"}, "createFoldersToOrganize": {"message": "Buat folder untuk mengorganisasi benda-benda di brankas Anda"}, "deleteFolderPermanently": {"message": "<PERSON><PERSON><PERSON><PERSON> Anda yakin akan menghapus folder ini selamanya?"}, "deleteFolder": {"message": "Hapus Folder"}, "folders": {"message": "Folder"}, "noFolders": {"message": "Tidak ada folder yang dapat dicantumkan."}, "helpFeedback": {"message": "Bantuan & Umpan Balik"}, "helpCenter": {"message": "Pusat Bantuan Bitwarden"}, "communityForums": {"message": "Telusuri forum Bitwarden"}, "contactSupport": {"message": "Kontak dukungan Bitwarden"}, "sync": {"message": "Sink<PERSON><PERSON><PERSON>"}, "syncVaultNow": {"message": "Sinkronkan Brankas Se<PERSON>ng"}, "lastSync": {"message": "Sink<PERSON><PERSON><PERSON>:"}, "passGen": {"message": "Pembuat Kata Sandi"}, "generator": {"message": "Pembuat Sandi", "description": "Short for 'credential generator'."}, "passGenInfo": {"message": "<PERSON><PERSON>a otomatis membuat sandi yang kuat dan unik untuk info masuk Anda."}, "bitWebVaultApp": {"message": "Aplikasi web Bitwarden"}, "importItems": {"message": "<PERSON><PERSON><PERSON>"}, "select": {"message": "<PERSON><PERSON><PERSON>"}, "generatePassword": {"message": "Buat Kata Sandi"}, "generatePassphrase": {"message": "Buat frasa sandi"}, "passwordGenerated": {"message": "Kata sandi dibuat"}, "passphraseGenerated": {"message": "Frasa sandi dibuat"}, "usernameGenerated": {"message": "Nama pengguna dibuat"}, "emailGenerated": {"message": "Surel dibuat"}, "regeneratePassword": {"message": "Buat Ulang Kata Sandi"}, "options": {"message": "<PERSON><PERSON><PERSON>"}, "length": {"message": "Panjang"}, "include": {"message": "Sertakan", "description": "Card header for password generator include block"}, "uppercaseDescription": {"message": "Sertakan karakter huruf kapital", "description": "Tooltip for the password generator uppercase character checkbox"}, "uppercaseLabel": {"message": "A-Z", "description": "Label for the password generator uppercase character checkbox"}, "lowercaseDescription": {"message": "Sertakan karakter huruf kecil", "description": "Full description for the password generator lowercase character checkbox"}, "lowercaseLabel": {"message": "a-z", "description": "Label for the password generator lowercase character checkbox"}, "numbersDescription": {"message": "Sertakan angka", "description": "Full description for the password generator numbers checkbox"}, "numbersLabel": {"message": "0-9", "description": "Label for the password generator numbers checkbox"}, "specialCharactersDescription": {"message": "Sertakan karakter k<PERSON>us", "description": "Full description for the password generator special characters checkbox"}, "numWords": {"message": "<PERSON><PERSON><PERSON>"}, "wordSeparator": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "capitalize": {"message": "<PERSON><PERSON><PERSON>", "description": "Make the first letter of a work uppercase."}, "includeNumber": {"message": "Ser<PERSON><PERSON>"}, "minNumbers": {"message": "Angka Minimum"}, "minSpecial": {"message": "Spesial Minimum"}, "avoidAmbiguous": {"message": "<PERSON><PERSON><PERSON> karakter ambigu", "description": "Label for the avoid ambiguous characters checkbox."}, "generatorPolicyInEffect": {"message": "Persyaratan kebijakan perusahaan telah diterapkan ke pilihan penghasil <PERSON>.", "description": "Indicates that a policy limits the credential generator screen."}, "searchVault": {"message": "<PERSON>i brankas"}, "edit": {"message": "Edit"}, "view": {"message": "Tampilan"}, "noItemsInList": {"message": "Tidak ada item yang dapat dicantumkan."}, "itemInformation": {"message": "Informasi Item"}, "username": {"message": "<PERSON><PERSON>"}, "password": {"message": "<PERSON><PERSON>"}, "totp": {"message": "<PERSON><PERSON>"}, "passphrase": {"message": "<PERSON><PERSON>"}, "favorite": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unfavorite": {"message": "Batalkan favorit"}, "itemAddedToFavorites": {"message": "<PERSON>a telah ditambahkan ke kesukaan"}, "itemRemovedFromFavorites": {"message": "<PERSON>a telah di<PERSON> dari kesukaan"}, "notes": {"message": "Catatan"}, "privateNote": {"message": "Catatan pribadi"}, "note": {"message": "Catatan"}, "editItem": {"message": "<PERSON>ting Item"}, "folder": {"message": "Folder"}, "deleteItem": {"message": "Ha<PERSON>"}, "viewItem": {"message": "<PERSON><PERSON>"}, "launch": {"message": "Luncurkan"}, "launchWebsite": {"message": "Buka situs web"}, "launchWebsiteName": {"message": "Buka situs web $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret item"}}}, "website": {"message": "Situs Web"}, "toggleVisibility": {"message": "Ubah Visibilitas"}, "manage": {"message": "<PERSON><PERSON><PERSON>"}, "other": {"message": "<PERSON><PERSON><PERSON>"}, "unlockMethods": {"message": "<PERSON><PERSON><PERSON>"}, "unlockMethodNeededToChangeTimeoutActionDesc": {"message": "Mengatur metode pembukaan kunci untuk mengubah tindakan batas waktu brankas Anda."}, "unlockMethodNeeded": {"message": "Mengatur metode penguncian di Pengaturan"}, "sessionTimeoutHeader": {"message": "<PERSON>as waktu sesi"}, "vaultTimeoutHeader": {"message": "Batas waktu brankas"}, "otherOptions": {"message": "Op<PERSON> la<PERSON>ya"}, "rateExtension": {"message": "<PERSON><PERSON>"}, "browserNotSupportClipboard": {"message": "<PERSON><PERSON><PERSON> Anda tidak mendukung menyalin clipboard dengan mudah. <PERSON><PERSON> secara manual."}, "verifyYourIdentity": {"message": "Verifikasikan identitas Anda"}, "weDontRecognizeThisDevice": {"message": "<PERSON>mi tidak mengenali perangkat ini. Masukkan kode yang dikirim ke surel Anda untuk memverifikasi identitas Anda."}, "continueLoggingIn": {"message": "Lanjutkan log masuk"}, "yourVaultIsLocked": {"message": "Brankas Anda terkun<PERSON>. Verifikasi kata sandi utama Anda untuk melanjutkan."}, "yourVaultIsLockedV2": {"message": "Brankas Anda te<PERSON>un<PERSON>"}, "yourAccountIsLocked": {"message": "<PERSON><PERSON><PERSON>"}, "or": {"message": "atau"}, "unlock": {"message": "<PERSON><PERSON>"}, "loggedInAsOn": {"message": "Telah masuk sebagai $EMAIL$ di $HOSTNAME$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "hostname": {"content": "$2", "example": "bitwarden.com"}}}, "invalidMasterPassword": {"message": "<PERSON><PERSON> utama tidak valid"}, "vaultTimeout": {"message": "Batas <PERSON>"}, "vaultTimeout1": {"message": "<PERSON><PERSON> waktu"}, "lockNow": {"message": "<PERSON><PERSON><PERSON>"}, "lockAll": {"message": "<PERSON><PERSON><PERSON> semua"}, "immediately": {"message": "<PERSON><PERSON><PERSON>"}, "tenSeconds": {"message": "10 detik"}, "twentySeconds": {"message": "20 detik"}, "thirtySeconds": {"message": "30 detik"}, "oneMinute": {"message": "1 menit"}, "twoMinutes": {"message": "2 menit"}, "fiveMinutes": {"message": "5 menit"}, "fifteenMinutes": {"message": "15 menit"}, "thirtyMinutes": {"message": "30 menit"}, "oneHour": {"message": "1 jam"}, "fourHours": {"message": "4 jam"}, "onLocked": {"message": "Saat Komputer Terkunci"}, "onRestart": {"message": "Saat <PERSON><PERSON>"}, "never": {"message": "<PERSON><PERSON>"}, "security": {"message": "<PERSON><PERSON><PERSON>"}, "confirmMasterPassword": {"message": "Konfirmasi kata sandi utama"}, "masterPassword": {"message": "<PERSON>a sandi utama"}, "masterPassImportant": {"message": "Kata sandi utama Anda tidak dapat dipulihkan jika Anda melupakannya!"}, "masterPassHintLabel": {"message": "Petunjuk kata sandi utama"}, "errorOccurred": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "emailRequired": {"message": "<PERSON><PERSON><PERSON> email diperlukan."}, "invalidEmail": {"message": "Alamat email tidak valid."}, "masterPasswordRequired": {"message": "<PERSON>a sandi utama dip<PERSON>lukan."}, "confirmMasterPasswordRequired": {"message": "<PERSON><PERSON> u<PERSON>a dip<PERSON>."}, "masterPasswordMinlength": {"message": "Kata sandi utama harus $VALUE$ karakter.", "description": "The Master Password must be at least a specific number of characters long.", "placeholders": {"value": {"content": "$1", "example": "8"}}}, "masterPassDoesntMatch": {"message": "Konfirmasi sandi utama tidak cocok."}, "newAccountCreated": {"message": "Akun baru Anda telah dibuat! Sekarang Anda bisa masuk."}, "newAccountCreated2": {"message": "Akun baru Anda telah dibuat!"}, "youHaveBeenLoggedIn": {"message": "Anda telah masuk!"}, "youSuccessfullyLoggedIn": {"message": "<PERSON><PERSON> be<PERSON><PERSON><PERSON> ma<PERSON>k"}, "youMayCloseThisWindow": {"message": "<PERSON>a dapat menutup jendela ini"}, "masterPassSent": {"message": "<PERSON><PERSON> telah men<PERSON> Anda email dengan petunjuk sandi utama And<PERSON>."}, "verificationCodeRequired": {"message": "<PERSON><PERSON> veri<PERSON><PERSON>."}, "webauthnCancelOrTimeout": {"message": "Otentikasi di<PERSON>an atau terlalu lama. Mohon coba kembali."}, "invalidVerificationCode": {"message": "Kode verifikasi tidak valid"}, "valueCopied": {"message": "$VALUE$ disalin", "description": "Value has been copied to the clipboard.", "placeholders": {"value": {"content": "$1", "example": "Password"}}}, "autofillError": {"message": "Tidak dapat mengisi otomatis item yang dipilih pada laman ini. Salin dan tempel informasinya sebagai gantinya."}, "totpCaptureError": {"message": "Tidak dapat memindai kode QR dari laman ini"}, "totpCaptureSuccess": {"message": "<PERSON><PERSON><PERSON> di<PERSON>"}, "totpCapture": {"message": "Pindai kode QR autentikator dari laman ini"}, "totpHelperTitle": {"message": "Buat verifi<PERSON>i dua-lang<PERSON>h lancar"}, "totpHelper": {"message": "Bitwarden dapat menyimpan dan mengisikan kode verifikasi dua-langkah. Salin dan tempel kunci ke kolom ini."}, "totpHelperWithCapture": {"message": "Bitwarden dapat menyimpan dan mengisikan kode verifikasi dua-langkah. Pilih ikon kamera untuk mengambil tangkapan layar dari kode QR otentikasi dari situs web ini, atau salin dan tempel kunci ke kolom ini."}, "learnMoreAboutAuthenticators": {"message": "<PERSON><PERSON><PERSON><PERSON> lebih lanjut tentang pengotentikasi"}, "copyTOTP": {"message": "<PERSON><PERSON> (TOTP)"}, "loggedOut": {"message": "<PERSON><PERSON><PERSON>"}, "loggedOutDesc": {"message": "<PERSON>a telah keluar dari akun <PERSON>."}, "loginExpired": {"message": "<PERSON><PERSON> masuk <PERSON>a telah be<PERSON>."}, "logIn": {"message": "<PERSON><PERSON><PERSON>"}, "logInToBitwarden": {"message": "Masuk ke Bitwarden"}, "enterTheCodeSentToYourEmail": {"message": "<PERSON><PERSON><PERSON>n kode yang dikirim ke surel Anda"}, "enterTheCodeFromYourAuthenticatorApp": {"message": "Ma<PERSON><PERSON><PERSON> kode dari aplikasi autentikator Anda"}, "pressYourYubiKeyToAuthenticate": {"message": "<PERSON><PERSON><PERSON>a untuk mengautentikasi"}, "duoTwoFactorRequiredPageSubtitle": {"message": "Log masuk dua langkah berganda diperlukan bagi akun Anda. <PERSON><PERSON><PERSON> langkah di bawah untuk menyelesaikan log masuk."}, "followTheStepsBelowToFinishLoggingIn": {"message": "<PERSON><PERSON><PERSON> langkah-langkah di bawah untuk menyelesaikan log masuk."}, "followTheStepsBelowToFinishLoggingInWithSecurityKey": {"message": "<PERSON><PERSON><PERSON> langkah-langkah berikut untuk menyelesaikan kegiatan masuk dengan kunci keamanan Anda."}, "restartRegistration": {"message": "<PERSON><PERSON> ul<PERSON> penda<PERSON>"}, "expiredLink": {"message": "<PERSON>tan telah kadal<PERSON>"}, "pleaseRestartRegistrationOrTryLoggingIn": {"message": "<PERSON><PERSON> mulai ulang pendaftaran atau coba masuk."}, "youMayAlreadyHaveAnAccount": {"message": "<PERSON>a mungkin telah memiliki sebuah akun"}, "logOutConfirmation": {"message": "Anda yakin ingin keluar?"}, "yes": {"message": "Ya"}, "no": {"message": "Tidak"}, "location": {"message": "<PERSON><PERSON>"}, "unexpectedError": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han yang tak diduga."}, "nameRequired": {"message": "<PERSON><PERSON>."}, "addedFolder": {"message": "Tambah Folder"}, "twoStepLoginConfirmation": {"message": "Info masuk dua langkah membuat akun Anda lebih aman dengan mengharuskan Anda memverifikasi info masuk Anda dengan peranti lain seperti kode keamanan, aplikasi autentikasi, SMK, panggilan telepon, atau email. Info masuk dua langkah dapat diaktifkan di brankas web bitwarden.com. Anda ingin mengunjungi situs web sekarang?"}, "twoStepLoginConfirmationContent": {"message": "B<PERSON>t akun <PERSON>a lebih aman dengan mengatur masuk dua-langkah pada aplikasi web Bitwarden."}, "twoStepLoginConfirmationTitle": {"message": "Lanjutkan ke aplikasi web?"}, "editedFolder": {"message": "Folder yang disunting"}, "deleteFolderConfirmation": {"message": "Anda yakin Anda ingin menghapus folder ini?"}, "deletedFolder": {"message": "Folder dihapus"}, "gettingStartedTutorial": {"message": "Tutorial Perkenalan"}, "gettingStartedTutorialVideo": {"message": "Lihat tutorial perkenalan kami untuk mempelajari bagaimana mendapatkan hasil maksimal dari ekstensi peramban."}, "syncingComplete": {"message": "Sinkron<PERSON><PERSON> se<PERSON>ai"}, "syncingFailed": {"message": "<PERSON>l menyink<PERSON>"}, "passwordCopied": {"message": "<PERSON><PERSON> disalin"}, "uri": {"message": "URI"}, "uriPosition": {"message": "URI $POSITION$", "description": "A listing of URIs. Ex: URI 1, URI 2, URI 3, etc.", "placeholders": {"position": {"content": "$1", "example": "2"}}}, "newUri": {"message": "URl Baru"}, "addDomain": {"message": "Tambah domain", "description": "'Domain' here refers to an internet domain name (e.g. 'bitwarden.com') and the message in whole described the act of putting a domain value into the context."}, "addedItem": {"message": "<PERSON><PERSON> yang <PERSON>"}, "editedItem": {"message": "<PERSON><PERSON> yang <PERSON>"}, "deleteItemConfirmation": {"message": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus item ini?"}, "deletedItem": {"message": "<PERSON><PERSON> yang <PERSON>"}, "overwritePassword": {"message": "<PERSON><PERSON>"}, "overwritePasswordConfirmation": {"message": "Anda yakin ingin menimpa sandi saat ini?"}, "overwriteUsername": {"message": "<PERSON><PERSON><PERSON> nama pengguna"}, "overwriteUsernameConfirmation": {"message": "Anda yakin ingin menimpa nama pengguna saat ini?"}, "searchFolder": {"message": "Cari folder"}, "searchCollection": {"message": "<PERSON><PERSON> k<PERSON>i"}, "searchType": {"message": "<PERSON><PERSON>"}, "noneFolder": {"message": "Tidak Ada Folder", "description": "This is the folder for uncategorized items"}, "enableAddLoginNotification": {"message": "<PERSON> untuk penambahan login"}, "vaultSaveOptionsTitle": {"message": "Simpan ke pilihan brankas"}, "addLoginNotificationDesc": {"message": "\"Notifikasi Penambahan Info Masuk\" secara otomatis akan meminta Anda untuk menyimpan info masuk baru ke brankas Anda saat Anda masuk untuk pertama kalinya."}, "addLoginNotificationDescAlt": {"message": "Tanyakan untuk menambah sebuah benda jika benda itu tidak ditemukan di brankas Anda. Diterapkan ke seluruh akun yang telah masuk."}, "showCardsInVaultViewV2": {"message": "Selalu tampilan kartu sebagai saran isi otomatis pada tampilan Brankas"}, "showCardsCurrentTab": {"message": "Tamplikan kartu pada halaman <PERSON>"}, "showCardsCurrentTabDesc": {"message": "Buat tampilan daftar benda dari kartu pada halaman Tab untuk isi otomatis yang mudah."}, "showIdentitiesInVaultViewV2": {"message": "Selalu tampilan identitas sebagai saran isi otomatis pada tampilan Brankas"}, "showIdentitiesCurrentTab": {"message": "Tampilkan identitas pada halaman Tab"}, "showIdentitiesCurrentTabDesc": {"message": "Buat tampilan daftar benda dari identitas pada halaman Tab untuk isi otomatis yang mudah."}, "clickToAutofillOnVault": {"message": "<PERSON>lik butir untuk mengisi otomatis pada tampilan Brankas"}, "clickToAutofill": {"message": "<PERSON><PERSON> butir dalam saran isi otomatis untuk mengisi"}, "clearClipboard": {"message": "Hapus Papan <PERSON>", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "clearClipboardDesc": {"message": "<PERSON><PERSON><PERSON> otomatis mengh<PERSON>us konten yang disalin dari papan klip <PERSON>.", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "notificationAddDesc": {"message": "<PERSON><PERSON><PERSON><PERSON> Bitwarden mengingat sandi ini untuk Anda?"}, "notificationAddSave": {"message": "<PERSON><PERSON>, Simpan <PERSON>"}, "notificationViewAria": {"message": "Lihat $ITEMNAME$, buka di jendela baru", "placeholders": {"itemName": {"content": "$1"}}, "description": "Aria label for the view button in notification bar confirmation message"}, "notificationNewItemAria": {"message": "New Item, opens in new window", "description": "Aria label for the new item button in notification bar confirmation message when error is prompted"}, "notificationEditTooltip": {"message": "Sunting sebelum menyimpan", "description": "Tooltip and Aria label for edit button on cipher item"}, "newNotification": {"message": "Pemberitahuan baru"}, "labelWithNotification": {"message": "$LABEL$: Pemberitahuan baru", "description": "Label for the notification with a new login suggestion.", "placeholders": {"label": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "notificationLoginSaveConfirmation": {"message": "saved to Bitwarden.", "description": "Shown to user after item is saved."}, "notificationLoginUpdatedConfirmation": {"message": "updated in Bitwarden.", "description": "Shown to user after item is updated."}, "selectItemAriaLabel": {"message": "Select $ITEMTYPE$, $ITEMNAME$", "description": "Used by screen readers. $1 is the item type (like vault or folder), $2 is the selected item name.", "placeholders": {"itemType": {"content": "$1"}, "itemName": {"content": "$2"}}}, "saveAsNewLoginAction": {"message": "Simpan sebagai log masuk baru", "description": "Button text for saving login details as a new entry."}, "updateLoginAction": {"message": "Perbarui log masuk", "description": "Button text for updating an existing login entry."}, "unlockToSave": {"message": "Unlock to save this login", "description": "User prompt to take action in order to save the login they just entered."}, "saveLogin": {"message": "Simpan log masuk", "description": "Prompt asking the user if they want to save their login details."}, "updateLogin": {"message": "Perbarui log masuk yang sudah ada", "description": "Prompt asking the user if they want to update an existing login entry."}, "loginSaveSuccess": {"message": "Log masuk disimpan", "description": "Message displayed when login details are successfully saved."}, "loginUpdateSuccess": {"message": "Log masuk diperbarui", "description": "Message displayed when login details are successfully updated."}, "loginUpdateTaskSuccess": {"message": "Bagus! Anda telah mengambil langkah-langkah untuk membuat Anda dan $ORGANIZATION$ lebih aman.", "placeholders": {"organization": {"content": "$1"}}, "description": "Shown to user after login is updated."}, "loginUpdateTaskSuccessAdditional": {"message": "Te<PERSON> kasih telah membuat $ORGANIZATION$ menjadi lebih aman. Anda memiliki $TASK_COUNT$ kata sandi lagi untuk diperbarui.", "placeholders": {"organization": {"content": "$1"}, "task_count": {"content": "$2"}}, "description": "Shown to user after login is updated."}, "nextSecurityTaskAction": {"message": "Ubah kata sandi se<PERSON>", "description": "Message prompting user to undertake completion of another security task."}, "saveFailure": {"message": "Kesalahan saat menyimpan", "description": "Error message shown when the system fails to save login details."}, "saveFailureDetails": {"message": "Oh tidak! Kami tidak bisa menyimpan ini. Cobalah memasukkan rincian secara manual.", "description": "Detailed error message shown when saving login details fails."}, "enableChangedPasswordNotification": {"message": "Tanyakan untuk memperbarui masuk yang sudah ada"}, "changedPasswordNotificationDesc": {"message": "Tanyakan untuk memperbarui kata sandi masuk ketika mendeteksi perubahan pada situs web."}, "changedPasswordNotificationDescAlt": {"message": "Tanyakan untuk memperbarui kata sandi masuk ketika mendeteksi perubahan pada situs web. Diterapkan ke semua akun yang telah masuk."}, "enableUsePasskeys": {"message": "Tanyakan untuk menyimpan dan menggunakan kunci sandi"}, "usePasskeysDesc": {"message": "Tanyakan untuk menyimpan kunci sandi baru atau masuk dengan kunci sandi yang tersimpan di brankas Anda. Diterapkan ke semua akun yang telah masuk."}, "notificationChangeDesc": {"message": "<PERSON><PERSON><PERSON><PERSON> Anda ingin memperbarui kata sandi ini di Bitwarden?"}, "notificationChangeSave": {"message": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>"}, "notificationUnlockDesc": {"message": "Buka brankan Bitwarden Anda untuk melengkapi permintaan isi otomatis."}, "notificationUnlock": {"message": "<PERSON><PERSON>"}, "additionalOptions": {"message": "<PERSON><PERSON><PERSON> tambahan"}, "enableContextMenuItem": {"message": "Tam<PERSON>lkan pilihan menu konteks"}, "contextMenuItemDesc": {"message": "Gunakan tombol sekunder untuk mengakses pembuat kata sandi dan mencocokkan login untuk situs web."}, "contextMenuItemDescAlt": {"message": "Gunakan tombol sekunder untuk mengakses pembuat kata sandi dan mencocokkan login untuk situs web. Diterapkan ke semua akun yang telah masuk."}, "defaultUriMatchDetection": {"message": "Deteksi Kecocokan URI Bawaan", "description": "Default URI match detection for autofill."}, "defaultUriMatchDetectionDesc": {"message": "<PERSON><PERSON><PERSON> cara bawaan penanganan pencocokan URI untuk masuk saat melakukan tindakan seperti isi-otomatis."}, "theme": {"message": "<PERSON><PERSON>"}, "themeDesc": {"message": "Ubah tema warna aplikasi."}, "themeDescAlt": {"message": "Ubah warna tema aplikasi. Diterapkan ke semua akun yang telah masuk."}, "dark": {"message": "<PERSON><PERSON><PERSON>", "description": "Dark color"}, "light": {"message": "Terang", "description": "Light color"}, "exportFrom": {"message": "Ekspor dari"}, "exportVault": {"message": "Ekspor Brankas"}, "fileFormat": {"message": "Format Berkas"}, "fileEncryptedExportWarningDesc": {"message": "Ekpor berkas ini akan akan dilindungi oleh kata sandi dan membutuhkan kata sandi berkas untuk mendekripsikannya."}, "filePassword": {"message": "<PERSON>a sandi berkas"}, "exportPasswordDescription": {"message": "Kata sandi ini akan digunakan untuk mengekspor dan mengimpor berkas ini"}, "accountRestrictedOptionDescription": {"message": "<PERSON>akan kunci enk<PERSON><PERSON> akun <PERSON>, di<PERSON><PERSON>an dari nama pengguna akun Anda dan kata sandi utama, untuk mengenkripsi ekspor dan membatasi impor menjadi hanya akun Bitwarden saat ini."}, "passwordProtectedOptionDescription": {"message": "Atur kata sandi berkas untuk mengenkripsi ekspor dan mengimpornya ke sebarang akun Bitwarden menggunakan kata sandi untuk dekripsi."}, "exportTypeHeading": {"message": "Jenis <PERSON>"}, "accountRestricted": {"message": "<PERSON><PERSON><PERSON>"}, "filePasswordAndConfirmFilePasswordDoNotMatch": {"message": "\"Kata sandi berkas\" dan \"<PERSON>n<PERSON><PERSON><PERSON> kata sandi berkas\" tidak cocok."}, "warning": {"message": "PERINGATAN", "description": "WARNING (should stay in capitalized letters if the language permits)"}, "warningCapitalized": {"message": "Peringatan", "description": "Warning (should maintain locale-relevant capitalization)"}, "confirmVaultExport": {"message": "Konfirmasi Ekspor Brankas"}, "exportWarningDesc": {"message": "Berkas ekspor ini berisi data brankas Anda dalam format tidak terenkripsi. <PERSON><PERSON> pernah menyimpan atau mengirim berkas ini melalui kanal tidak aman (seperti surel). Se<PERSON>a hapus setelah Anda se<PERSON>ai menggunakannya."}, "encExportKeyWarningDesc": {"message": "Ekspor ini mengenkripsi data Anda menggunakan kunci enkripsi akun Anda. Jika Anda pernah merotasi kunci enkripsi akun And<PERSON>, <PERSON><PERSON> harus mengekspor lagi karena Anda tidak akan dapat mendekripsi file ekspor ini."}, "encExportAccountWarningDesc": {"message": "Kunci enkripsi akun unik untuk setiap akun pengguna Bitwarden, jadi Anda tidak dapat mengimpor ekspor terenkripsi ke akun lain."}, "exportMasterPassword": {"message": "Masukkan sandi utama Anda untuk mengekspor data brankas Anda."}, "shared": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "bitwardenForBusinessPageDesc": {"message": "Bitwarden untuk Bisnis membolehkan Anda untuk membagikan benda-benda di brankas Anda dengan orang lain dengan menggunakan sebuah organisasi. Pelajari lebih lanjut pada situs web bitwarden.com."}, "moveToOrganization": {"message": "Pindah ke Organisasi"}, "movedItemToOrg": {"message": "$ITEMNAME$ pindah ke $ORGNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "orgname": {"content": "$2", "example": "Company Name"}}}, "moveToOrgDesc": {"message": "<PERSON><PERSON><PERSON><PERSON> sebuah organisasi yang Anda ingin memindahkan item ini. Memindahkan bearti memberikan kepemilikan kepada organisasi tersebut. Anda tidak akan lagi menjadi pemilik item ini."}, "learnMore": {"message": "<PERSON><PERSON><PERSON><PERSON> lebih lan<PERSON>t"}, "authenticatorKeyTotp": {"message": "<PERSON><PERSON><PERSON> (TOTP)"}, "verificationCodeTotp": {"message": "<PERSON><PERSON> (TOTP)"}, "copyVerificationCode": {"message": "<PERSON><PERSON>"}, "attachments": {"message": "Lam<PERSON>ran"}, "deleteAttachment": {"message": "<PERSON><PERSON>"}, "deleteAttachmentConfirmation": {"message": "Anda yakin ingin menghapus lampiran ini?"}, "deletedAttachment": {"message": "<PERSON><PERSON><PERSON>"}, "newAttachment": {"message": "Tambah Lampiran Baru"}, "noAttachments": {"message": "Tidak ada lampiran."}, "attachmentSaved": {"message": "<PERSON><PERSON><PERSON> telah disimpan."}, "file": {"message": "Berkas"}, "fileToShare": {"message": "<PERSON><PERSON><PERSON> untuk <PERSON>bag<PERSON>n"}, "selectFile": {"message": "<PERSON><PERSON><PERSON>."}, "maxFileSize": {"message": "Ukuran berkas maksimal adalah 500 MB."}, "featureUnavailable": {"message": "Fitur Tidak Tersedia"}, "legacyEncryptionUnsupported": {"message": "Legacy encryption is no longer supported. Please contact support to recover your account."}, "premiumMembership": {"message": "Keanggotaan Premium"}, "premiumManage": {"message": "<PERSON><PERSON><PERSON>"}, "premiumManageAlert": {"message": "Anda dapat mengelola keanggotaan Anda di brankas web bitwarden.com. Anda ingin mengunjungi situs web sekarang?"}, "premiumRefresh": {"message": "Segarkan Keanggotaan"}, "premiumNotCurrentMember": {"message": "Anda saat ini bukan anggota premium."}, "premiumSignUpAndGet": {"message": "Daftar untuk keanggotaan premium dan mendapatkan:"}, "ppremiumSignUpStorage": {"message": "1 GB penyimpanan berkas yang dienkripsi."}, "premiumSignUpEmergency": {"message": "<PERSON><PERSON><PERSON>."}, "premiumSignUpTwoStepOptions": {"message": "<PERSON><PERSON><PERSON> masuk dua-langkah yang dipatenkan seperti <PERSON> dan <PERSON>."}, "ppremiumSignUpReports": {"message": "<PERSON><PERSON><PERSON><PERSON> kata sandi, k<PERSON><PERSON><PERSON> a<PERSON>, dan laporan kebocoran data untuk tetap menjaga keamanan brankas Anda."}, "ppremiumSignUpTotp": {"message": "Pembuat kode verifikasi TOTP (2FA) untuk masuk di brankas anda."}, "ppremiumSignUpSupport": {"message": "Dukungan pelanggan prioritas."}, "ppremiumSignUpFuture": {"message": "Se<PERSON>a fitur-fitur premium masa depan. Akan segera tiba!"}, "premiumPurchase": {"message": "Beli Keanggotaan Premium"}, "premiumPurchaseAlertV2": {"message": "Anda dapat membeli Premium dari pilihan akun Anda pada aplikasi web Bitwarden."}, "premiumCurrentMember": {"message": "Anda adalah anggota premium!"}, "premiumCurrentMemberThanks": {"message": "<PERSON><PERSON> kasih telah mendukung Bitwarden."}, "premiumFeatures": {"message": "Tingkatkan ke Premium dan dapatkan:"}, "premiumPrice": {"message": "Semua itu hanya $PRICE$ /tahun!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "premiumPriceV2": {"message": "Semua itu hanya $PRICE$ tiap tahun!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "refreshComplete": {"message": "Penyegara<PERSON> se<PERSON>"}, "enableAutoTotpCopy": {"message": "Salin TOTP secara otomatis"}, "disableAutoTotpCopyDesc": {"message": "Jika info masuk Anda memiliki kunci autentikasi yang menyertainya, kode verifikasi TOTP akan disalin secara otomatis ke clipboard Anda setiap kali Anda mengisi info masuk secara otomatis."}, "enableAutoBiometricsPrompt": {"message": "Tanyakan untuk biometrik pada saat diluncurkan"}, "premiumRequired": {"message": "Membutuhkan Keanggotaan Premium"}, "premiumRequiredDesc": {"message": "Keanggotaan premium diperlukan untuk menggunakan fitur ini."}, "authenticationTimeout": {"message": "Batas waktu otentikasi"}, "authenticationSessionTimedOut": {"message": "<PERSON><PERSON> otentikasi telah berak<PERSON>. <PERSON><PERSON> mulai ulang proses masuk."}, "verificationCodeEmailSent": {"message": "Surel verifikasi telah dikirim ke $EMAIL$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "dontAskAgainOnThisDeviceFor30Days": {"message": "<PERSON>an tanyakan lagi pada perangkat ini untuk 30 hari"}, "selectAnotherMethod": {"message": "<PERSON><PERSON>h metode lain", "description": "Select another two-step login method"}, "useYourRecoveryCode": {"message": "<PERSON><PERSON><PERSON> kode pemulihan <PERSON>"}, "insertU2f": {"message": "Masukkan kunci keamanan ke port USB komputer Anda. <PERSON><PERSON> ada tomb<PERSON>, tekanlah."}, "openInNewTab": {"message": "<PERSON>uka dalam tab baru"}, "webAuthnAuthenticate": {"message": "Autentikasi dengan WebAuthn."}, "readSecurityKey": {"message": "Baca kunci keamanan"}, "awaitingSecurityKeyInteraction": {"message": "Menunggu interaksi kunci keamanan..."}, "loginUnavailable": {"message": "Info Masuk Tidak Tersedia"}, "noTwoStepProviders": {"message": "Akun ini mengaktifkan info masuk dua langkah, namun, tidak satupun dari penyedia dua langkah yang dikonfigurasi didukung oleh peramban web ini."}, "noTwoStepProviders2": {"message": "<PERSON><PERSON><PERSON> gunakan peramban web yang didukung (seperti Chrome) dan/atau tambahkan penyedia tambahan yang didukung di semua peramban web (seperti aplikasi autentikasi)."}, "twoStepOptions": {"message": "Opsi Info Masuk Du<PERSON> Lang<PERSON>"}, "selectTwoStepLoginMethod": {"message": "Pilih metode log masuk dua langkah"}, "recoveryCodeDesc": {"message": "Kehilangan akses ke semua penyedia dua faktor And<PERSON>? Gunakan kode pemulihan untuk menonaktifkan semua penyedia dua faktor dari akun <PERSON>."}, "recoveryCodeTitle": {"message": "<PERSON><PERSON>"}, "authenticatorAppTitle": {"message": "Aplikasi Otentikasi"}, "authenticatorAppDescV2": {"message": "Masukkan kode yang dihasilkan dari aplikasi pengotentikasi seperti Pengotentikasi Bitwarden.", "description": "'Bitwarden Authenticator' is a product name and should not be translated."}, "yubiKeyTitleV2": {"message": "<PERSON><PERSON><PERSON> OTP Yubico"}, "yubiKeyDesc": {"message": "<PERSON><PERSON><PERSON> untuk mengakses akun <PERSON>. <PERSON><PERSON><PERSON> dengan <PERSON> 4, 4 <PERSON><PERSON>, 4<PERSON>, dan per<PERSON>O."}, "duoDescV2": {"message": "Masukkan kode yang dihasilkan oleh Duo Security.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "duoOrganizationDesc": {"message": "Verifikasi dengan Duo Security untuk organisasi Anda menggunakan aplikasi Duo Mobile, SMS, panggilan telepon, atau kunci keamanan U2F.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "webAuthnTitle": {"message": "FIDO2 WebAuthn"}, "webAuthnDesc": {"message": "Gunakan kunci yang mendukung WebAUthn untuk mengakses akun anda."}, "emailTitle": {"message": "Email"}, "emailDescV2": {"message": "<PERSON><PERSON><PERSON>n kode yang dikirim ke surel Anda."}, "selfHostedEnvironment": {"message": "Lingkungan Penyedia Personal"}, "selfHostedBaseUrlHint": {"message": "Tentukan URL dasar dari pema<PERSON>an Bitwarden di server Anda. Contoh: https://bitwarden.company.com"}, "selfHostedCustomEnvHeader": {"message": "<PERSON>tuk pengaturan tingkat lan<PERSON>, <PERSON><PERSON> dapat menentukan URL dasar dari setiap layanan secara terpisah."}, "selfHostedEnvFormInvalid": {"message": "<PERSON>a harus menambahkan antara URL dasar server atau paling tidak satu lingkungan ubahsuai."}, "customEnvironment": {"message": "Lingkunga<PERSON>"}, "baseUrl": {"message": "URL Server"}, "selfHostBaseUrl": {"message": "URL server yang dihosting mandiri", "description": "Label for field requesting a self-hosted integration service URL"}, "apiUrl": {"message": "URL Server API"}, "webVaultUrl": {"message": "URL Server Brankas Web"}, "identityUrl": {"message": "URL Server Identitas"}, "notificationsUrl": {"message": "URL Server Notifikasi"}, "iconsUrl": {"message": "URL Server Ikon"}, "environmentSaved": {"message": "URL dari semua lingkungan telah disimpan."}, "showAutoFillMenuOnFormFields": {"message": "Tampilkan menu isi otomatis pada kolom formulir", "description": "Represents the message for allowing the user to enable the autofill overlay"}, "autofillSuggestionsSectionTitle": {"message": "Saran isi otomatis"}, "autofillSpotlightTitle": {"message": "Easily find autofill suggestions"}, "autofillSpotlightDesc": {"message": "Turn off your browser's autofill settings, so they don't conflict with Bitwarden."}, "turnOffBrowserAutofill": {"message": "Turn off $BROWSER$ autofill", "placeholders": {"browser": {"content": "$1", "example": "Chrome"}}}, "turnOffAutofill": {"message": "Turn off autofill"}, "showInlineMenuLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> saran isi otomatis pada kolom formulir"}, "showInlineMenuIdentitiesLabel": {"message": "Tampilkan identitas sebagai saran"}, "showInlineMenuCardsLabel": {"message": "Tam<PERSON>lkan kartu sebagai saran"}, "showInlineMenuOnIconSelectionLabel": {"message": "Tam<PERSON>lkan saran ketika ikon dipilih"}, "showInlineMenuOnFormFieldsDescAlt": {"message": "Diterapkan ke semua akun yang telah masuk."}, "turnOffBrowserBuiltInPasswordManagerSettings": {"message": "<PERSON><PERSON><PERSON> pengaturan pengelola kata sandi bawaan peramban Anda untuk menghindari benturan."}, "turnOffBrowserBuiltInPasswordManagerSettingsLink": {"message": "Sunting pengaturan peramban."}, "autofillOverlayVisibilityOff": {"message": "<PERSON><PERSON>", "description": "Overlay setting select option for disabling autofill overlay"}, "autofillOverlayVisibilityOnFieldFocus": {"message": "Ketika kolom dipilih (ketika difokuskan)", "description": "Overlay appearance select option for showing the field on focus of the input element"}, "autofillOverlayVisibilityOnButtonClick": {"message": "<PERSON><PERSON><PERSON> ikon isi otomatis dipilih", "description": "Overlay appearance select option for showing the field on click of the overlay icon"}, "enableAutoFillOnPageLoadSectionTitle": {"message": "<PERSON><PERSON> otomatis ketika halaman dimuat"}, "enableAutoFillOnPageLoad": {"message": "Akt<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, "enableAutoFillOnPageLoadDesc": {"message": "Jika formulir info masuk terdeteksi, secara otomatis melakukan pengisian otomatis ketika memuat laman web."}, "experimentalFeature": {"message": "Situs web yang disusupi atau tidak terpercaya dapat memanfaatkan isi otomatis ketika halaman dimuat."}, "learnMoreAboutAutofillOnPageLoadLinkText": {"message": "<PERSON><PERSON><PERSON><PERSON> lebih lanjut tentang risiko"}, "learnMoreAboutAutofill": {"message": "<PERSON><PERSON><PERSON><PERSON> lebih lanjut tentang isi otomatis"}, "defaultAutoFillOnPageLoad": {"message": "Konfigurasi autofill standard untuk item login."}, "defaultAutoFillOnPageLoadDesc": {"message": "Setelah mengaktifkan Auto-Fill waktu website terbuka, kamu dapat mengaktifkan atau meng-nonaktifkan feature ini untuk setiap item. Ini adalah konfigurasi standard untuk item yang tidak dikonfigurasi terpisah."}, "itemAutoFillOnPageLoad": {"message": "Auto-fill waktu website terbuka (Jika diaktifkan di Options)"}, "autoFillOnPageLoadUseDefault": {"message": "<PERSON><PERSON><PERSON> pen<PERSON>uran baku"}, "autoFillOnPageLoadYes": {"message": "Auto-Fill ketika website baru terbuka"}, "autoFillOnPageLoadNo": {"message": "Jangan Auto-Fill ketika website baru terbuka"}, "commandOpenPopup": {"message": "Buka popup brankas"}, "commandOpenSidebar": {"message": "<PERSON>uka brankas di bilah samping"}, "commandAutofillLoginDesc": {"message": "<PERSON>i otomatis login yang terakhir digunakan untuk situs web saat ini"}, "commandAutofillCardDesc": {"message": "<PERSON>i otomatis kartu yang terakhir digunakan untuk situs web saat ini"}, "commandAutofillIdentityDesc": {"message": "Isi otomatis identitas yang terakhir digunakan untuk situs web saat ini"}, "commandGeneratePasswordDesc": {"message": "Buat dan salin kata sandi acak baru ke papan klip."}, "commandLockVaultDesc": {"message": "<PERSON><PERSON><PERSON> brankas"}, "customFields": {"message": "<PERSON><PERSON><PERSON>"}, "copyValue": {"message": "<PERSON><PERSON>"}, "value": {"message": "<PERSON><PERSON>"}, "newCustomField": {"message": "<PERSON><PERSON><PERSON>"}, "dragToSort": {"message": "<PERSON>et untuk mengurutkan"}, "dragToReorder": {"message": "Seret untuk mengubah urutan"}, "cfTypeText": {"message": "Teks"}, "cfTypeHidden": {"message": "Tersemb<PERSON><PERSON>"}, "cfTypeBoolean": {"message": "Boolean"}, "cfTypeCheckbox": {"message": "Kotak centang"}, "cfTypeLinked": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "This describes a field that is 'linked' (tied) to another field."}, "linkedValue": {"message": "<PERSON><PERSON>", "description": "This describes a value that is 'linked' (tied) to another value."}, "popup2faCloseMessage": {"message": "Tindakan klik diluar jendela popup untuk memeriksa  kode verifikasi di dalam surel Anda akan menyebabkan popup ini ditutup. <PERSON><PERSON><PERSON><PERSON> Anda ingin membuka popup ini di jendela baru sehingga terus tetap terbuka?"}, "popupU2fCloseMessage": {"message": "Peramban ini tidak bisa memproses permintaan U2F di jendela popup ini. <PERSON><PERSON><PERSON><PERSON> Anda ingin membuka popup ini di jendela baru sehingga Anda dapat masuk menggunakan U2F?"}, "enableFavicon": {"message": "<PERSON><PERSON><PERSON>an ikon situs web"}, "faviconDesc": {"message": "<PERSON><PERSON><PERSON><PERSON> sebuah gambar yang dapat dikenali di setiap masuk."}, "faviconDescAlt": {"message": "<PERSON><PERSON><PERSON><PERSON> sebuah gambar yang dapat dikenali di sebelah tiap login. Diterapkan ke semua akun yang telah masuk."}, "enableBadgeCounter": {"message": "<PERSON><PERSON><PERSON><PERSON> hitungan di lencana"}, "badgeCounterDesc": {"message": "Tunjukkan seberapa banyak login yang Anda miliki untuk halaman web saat ini."}, "cardholderName": {"message": "<PERSON><PERSON>"}, "number": {"message": "Nomor"}, "brand": {"message": "<PERSON><PERSON>"}, "expirationMonth": {"message": "<PERSON><PERSON><PERSON>"}, "expirationYear": {"message": "<PERSON><PERSON>"}, "expiration": {"message": "<PERSON><PERSON>"}, "january": {"message": "<PERSON><PERSON><PERSON>"}, "february": {"message": "<PERSON><PERSON><PERSON>"}, "march": {"message": "<PERSON><PERSON>"}, "april": {"message": "April"}, "may": {"message": "<PERSON>"}, "june": {"message": "<PERSON><PERSON>"}, "july": {"message": "<PERSON><PERSON>"}, "august": {"message": "<PERSON><PERSON><PERSON>"}, "september": {"message": "September"}, "october": {"message": "Oktober"}, "november": {"message": "November"}, "december": {"message": "Desember"}, "securityCode": {"message": "<PERSON><PERSON>"}, "ex": {"message": "mis."}, "title": {"message": "Panggilan"}, "mr": {"message": "<PERSON><PERSON>"}, "mrs": {"message": "Nyonya"}, "ms": {"message": "<PERSON><PERSON>"}, "dr": {"message": "Dr"}, "mx": {"message": "Yth"}, "firstName": {"message": "<PERSON><PERSON>"}, "middleName": {"message": "<PERSON><PERSON>"}, "lastName": {"message": "<PERSON><PERSON>"}, "fullName": {"message": "<PERSON><PERSON>"}, "identityName": {"message": "Nama <PERSON>"}, "company": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ssn": {"message": "Nomor Jaminan <PERSON>"}, "passportNumber": {"message": "<PERSON><PERSON> Pa<PERSON>or"}, "licenseNumber": {"message": "<PERSON><PERSON>"}, "email": {"message": "Email"}, "phone": {"message": "Telepon"}, "address": {"message": "<PERSON><PERSON><PERSON>"}, "address1": {"message": "Alamat 1"}, "address2": {"message": "Alamat 2"}, "address3": {"message": "Alamat 3"}, "cityTown": {"message": "Kota / Kabupaten"}, "stateProvince": {"message": "Negara Bagian / Provinsi"}, "zipPostalCode": {"message": "Kode Pos"}, "country": {"message": "Negara"}, "type": {"message": "Tipe"}, "typeLogin": {"message": "Info <PERSON>"}, "typeLogins": {"message": "Info <PERSON>"}, "typeSecureNote": {"message": "Catatan Aman"}, "typeCard": {"message": "Kartu"}, "typeIdentity": {"message": "Identitas"}, "typeSshKey": {"message": "Kunci SSH"}, "newItemHeader": {"message": "$TYPE$ baru", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "editItemHeader": {"message": "Sunting $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "viewItemHeader": {"message": "Lihat $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "passwordHistory": {"message": "Riwayat Kata Sandi"}, "generatorHistory": {"message": "Ri<PERSON><PERSON> pen<PERSON>asil"}, "clearGeneratorHistoryTitle": {"message": "Bersihkan riwayat penghasil"}, "cleargGeneratorHistoryDescription": {"message": "<PERSON><PERSON> <PERSON>a melan<PERSON>, semua daftar akan dihapus selamanya dari riwayat penghasil. <PERSON><PERSON><PERSON><PERSON> Anda yakin ingin melanjutkan?"}, "back": {"message": "Kembali"}, "collections": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "nCollections": {"message": "$COUNT$ koleksi", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "favorites": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "popOutNewWindow": {"message": "<PERSON>uka di jendela baru"}, "refresh": {"message": "Segarkan"}, "cards": {"message": "Kartu"}, "identities": {"message": "Identitas"}, "logins": {"message": "Info <PERSON>"}, "secureNotes": {"message": "Catatan Aman"}, "sshKeys": {"message": "Kunci SSH"}, "clear": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "To clear something out. example: To clear browser history."}, "checkPassword": {"message": "<PERSON>iksa jika kata sandi telah terekspos."}, "passwordExposed": {"message": "Kata sandi ini telah terekspos $VALUE$ kali dalam insiden kebocoran data. Anda harus memperbaruinya.", "placeholders": {"value": {"content": "$1", "example": "2"}}}, "passwordSafe": {"message": "Kata sandi ini tidak ditemukan dalam insiden kebocoran data yang ada. Kata sandi tersebut seharusnya aman untuk digunakan."}, "baseDomain": {"message": "Domain basis", "description": "Domain name. Ex. website.com"}, "baseDomainOptionRecommended": {"message": "Domain utama (disarankan)", "description": "Domain name. Ex. website.com"}, "domainName": {"message": "Nama Domain", "description": "Domain name. Ex. website.com"}, "host": {"message": "<PERSON><PERSON>", "description": "A URL's host value. For example, the host of https://sub.domain.com:443 is 'sub.domain.com:443'."}, "exact": {"message": "Tepat"}, "startsWith": {"message": "<PERSON><PERSON>"}, "regEx": {"message": "Ekspresi umum", "description": "A programming term, also known as 'RegEx'."}, "matchDetection": {"message": "Deteksi Kecocokan", "description": "URI match detection for autofill."}, "defaultMatchDetection": {"message": "Deteksi kecocokan standar", "description": "Default URI match detection for autofill."}, "toggleOptions": {"message": "Ubah Opsi"}, "toggleCurrentUris": {"message": "Alihkan URI Saat Ini", "description": "Toggle the display of the URIs of the currently open tabs in the browser."}, "currentUri": {"message": "URI Saat Ini", "description": "The URI of one of the current open tabs in the browser."}, "organization": {"message": "Organisasi", "description": "An entity of multiple related people (ex. a team or business organization)."}, "types": {"message": "Tipe"}, "allItems": {"message": "<PERSON><PERSON><PERSON>"}, "noPasswordsInList": {"message": "Tidak ada sandi yang dapat dicantumkan."}, "clearHistory": {"message": "Bersihkan riwayat"}, "nothingToShow": {"message": "Tidak ada yang dapat ditampilkan"}, "nothingGeneratedRecently": {"message": "Anda belum mengh<PERSON>lkan apapun akhir-akhir ini"}, "remove": {"message": "Hapus"}, "default": {"message": "<PERSON><PERSON><PERSON>"}, "dateUpdated": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "ex. Date this item was updated"}, "dateCreated": {"message": "Dibuat", "description": "ex. Date this item was created"}, "datePasswordUpdated": {"message": "<PERSON><PERSON>", "description": "ex. Date this password was updated"}, "neverLockWarning": {"message": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menggunakan opsi \"<PERSON><PERSON>\"? Mengatur opsi penguncian ke \"<PERSON><PERSON>\" akan menyimpan kunci enkripsi brankas Anda di dalam perangkat. Jika Anda menggunakan opsi ini, <PERSON>a harus pastikan perangkat Anda dilindungi dengan baik."}, "noOrganizationsList": {"message": "Anda tidak tergabung dalam organisasi apapun. Organisasi memungkinkan Anda secara aman berbagi item dengan pengguna lainnya."}, "noCollectionsInList": {"message": "Tidak ada koleksi untuk ditampilkan."}, "ownership": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "whoOwnsThisItem": {"message": "Siapa pemilik item ini?"}, "strong": {"message": "Ku<PERSON>", "description": "ex. A strong password. Scale: Weak -> Good -> Strong"}, "good": {"message": "Baik", "description": "ex. A good password. Scale: Weak -> Good -> Strong"}, "weak": {"message": "Lemah", "description": "ex. A weak password. Scale: Weak -> Good -> Strong"}, "weakMasterPassword": {"message": "<PERSON><PERSON>"}, "weakMasterPasswordDesc": {"message": "Kata sandi utama yang Anda pilih itu lemah. Anda harus menggunakan kata sandi yang kuat (atau frasa sandi) untuk melindungi akun Bitwarden Anda. A<PERSON><PERSON>h Anda yakin ingin menggunakan kata sandi ini?"}, "pin": {"message": "PIN", "description": "PIN code. Ex. The short code (often numeric) that you use to unlock a device."}, "unlockWithPin": {"message": "<PERSON>uka kunci dengan PIN"}, "setYourPinTitle": {"message": "Atur PIN"}, "setYourPinButton": {"message": "Atur PIN"}, "setYourPinCode": {"message": "Setel kode PIN Anda untuk membuka kunci Bitwarden. Pengaturan PIN Anda akan diatur ulang jika Anda pernah keluar sepenuhnya dari aplikasi."}, "setPinCode": {"message": "You can use this PIN to unlock Bitwarden. Your PIN will be reset if you ever fully log out of the application."}, "pinRequired": {"message": "Membutuhkan kode PIN."}, "invalidPin": {"message": "Kode PIN tidak valid."}, "tooManyInvalidPinEntryAttemptsLoggingOut": {"message": "Terlalu banyak usaha memasukkan PIN yang gagal. Mengeluarkan dari sesi."}, "unlockWithBiometrics": {"message": "Buka kunci dengan biometrik"}, "unlockWithMasterPassword": {"message": "<PERSON>uka dengan kata sandi utama"}, "awaitDesktop": {"message": "Menunggu konfirmasi dari desktop"}, "awaitDesktopDesc": {"message": "Silakan konfirmasi menggunakan biometrik di aplikasi Bitwarden Desktop untuk mengaktifkan biometrik untuk peramban."}, "lockWithMasterPassOnRestart": {"message": "<PERSON>nci dengan kata sandi utama saat peramban dimulai ulang"}, "lockWithMasterPassOnRestart1": {"message": "Memerlukan kata sandi utama ketika mulai ulang peramban"}, "selectOneCollection": {"message": "Anda harus memilih setidaknya satu koleksi."}, "cloneItem": {"message": "Duplikat Item"}, "clone": {"message": "Duplikat"}, "passwordGenerator": {"message": "Pembuat kata sandi"}, "usernameGenerator": {"message": "Pembuat nama pengguna"}, "useThisEmail": {"message": "<PERSON><PERSON> surel ini"}, "useThisPassword": {"message": "<PERSON><PERSON>n kata sandi ini"}, "useThisPassphrase": {"message": "Use this passphrase"}, "useThisUsername": {"message": "<PERSON><PERSON>n nama pengguna ini"}, "securePasswordGenerated": {"message": "Kata sandi aman berhasil dibuat! Jangan lupa untuk memperbarui kata sandi Anda di situs web."}, "useGeneratorHelpTextPartOne": {"message": "<PERSON><PERSON><PERSON>", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "useGeneratorHelpTextPartTwo": {"message": "untuk membuat sebuah kata sandi unit yang kuat", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "vaultCustomization": {"message": "Penyesuaian brankas"}, "vaultTimeoutAction": {"message": "<PERSON>dakan Batas Waktu <PERSON>"}, "vaultTimeoutAction1": {"message": "<PERSON>as waktu tindakan"}, "lock": {"message": "<PERSON><PERSON><PERSON>", "description": "Verb form: to make secure or inaccessible by"}, "trash": {"message": "Sampah", "description": "Noun: a special folder to hold deleted items"}, "searchTrash": {"message": "Cari di sampah"}, "permanentlyDeleteItem": {"message": "<PERSON><PERSON>"}, "permanentlyDeleteItemConfirmation": {"message": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus item ini secara permanen?"}, "permanentlyDeletedItem": {"message": "<PERSON><PERSON>"}, "restoreItem": {"message": "P<PERSON><PERSON><PERSON>"}, "restoredItem": {"message": "<PERSON><PERSON>"}, "alreadyHaveAccount": {"message": "Sudah memiliki akun?"}, "vaultTimeoutLogOutConfirmation": {"message": "<PERSON><PERSON><PERSON> akan menghapus semua akses ke brankas Anda dan membutuhkan otentikasi daring setelah periode batas waktu tertentu. Apakah Anda yakin ingin menggunakan pengaturan ini?"}, "vaultTimeoutLogOutConfirmationTitle": {"message": "Kon<PERSON><PERSON><PERSON> Wak<PERSON>"}, "autoFillAndSave": {"message": "<PERSON><PERSON> dan <PERSON>"}, "fillAndSave": {"message": "<PERSON><PERSON>n dan simpan"}, "autoFillSuccessAndSavedUri": {"message": "<PERSON><PERSON> yang <PERSON> dan U<PERSON> Tersimpan"}, "autoFillSuccess": {"message": "<PERSON><PERSON>"}, "insecurePageWarning": {"message": "Peringatan: <PERSON><PERSON> adalah halaman HTTP yang tidak aman, dan setiap informasi yang Anda kirim dapat berpotensi terlihat dan diubah oleh orang lain. Login ini awalnya disimpan di halaman aman (HTTPS) "}, "insecurePageWarningFillPrompt": {"message": "Anda masih ingin mengisi login ini?"}, "autofillIframeWarning": {"message": "Formulir dihosting oleh domain yang berbeda dari URI login yang telah Anda simpan. Pilih OK untuk tetap mengisi otomatis, atau Batalkan untuk menghentikan."}, "autofillIframeWarningTip": {"message": "Untuk mencegah peringatan ini di masa depan, simpan URI ini, $HOSTNAME$, ke benda login Bitwarden untuk situs ini.", "placeholders": {"hostname": {"content": "$1", "example": "www.example.com"}}}, "setMasterPassword": {"message": "At<PERSON> Kat<PERSON>"}, "currentMasterPass": {"message": "Kata sandi utama saat ini"}, "newMasterPass": {"message": "Kata sandi utama baru"}, "confirmNewMasterPass": {"message": "Konfirmasi kata sandi utama baru"}, "masterPasswordPolicyInEffect": {"message": "Satu atau lebih kebijakan organisasi membutuhkan kata sandi utama Anda untuk memenuhi persyaratan berikut:"}, "policyInEffectMinComplexity": {"message": "Skor kompleksitas minimum $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "policyInEffectMinLength": {"message": "Panjang minimum $LENGTH$", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "policyInEffectUppercase": {"message": "<PERSON><PERSON>i satu atau lebih karakter huruf besar"}, "policyInEffectLowercase": {"message": "<PERSON><PERSON>i satu atau lebih karakter huruf kecil"}, "policyInEffectNumbers": {"message": "<PERSON><PERSON>i satu atau lebih angka"}, "policyInEffectSpecial": {"message": "Berisi satu atau lebih karakter khusus berikut $CHARS$", "placeholders": {"chars": {"content": "$1", "example": "!@#$%^&*"}}}, "masterPasswordPolicyRequirementsNotMet": {"message": "Kata sandi utama Anda yang baru tidak memenuhi persyaratan kebijakan."}, "receiveMarketingEmailsV2": {"message": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON>, <PERSON><PERSON><PERSON>, dan kesempatan pen<PERSON>tian dari <PERSON> di kotak masuk <PERSON>."}, "unsubscribe": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "atAnyTime": {"message": "kapanpun."}, "byContinuingYouAgreeToThe": {"message": "<PERSON><PERSON>, <PERSON><PERSON>"}, "and": {"message": "dan"}, "acceptPolicies": {"message": "<PERSON>gan mencentang kotak ini, <PERSON><PERSON> menye<PERSON><PERSON>i yang berikut:"}, "acceptPoliciesRequired": {"message": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON> belum disetujui."}, "termsOfService": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "privacyPolicy": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "yourNewPasswordCannotBeTheSameAsYourCurrentPassword": {"message": "Kata sandi baru Anda tidak boleh sama dengan kata sandi Anda yang sekarang."}, "hintEqualsPassword": {"message": "Petunjuk kata sandi Anda tidak boleh sama dengan kata sandi Anda."}, "ok": {"message": "<PERSON>e"}, "errorRefreshingAccessToken": {"message": "Galat Penyegaran Token Akses"}, "errorRefreshingAccessTokenDesc": {"message": "Tidak ada token penyegaran atau kunci API yang ditemukan. Harap coba keluar dan masuk kembali."}, "desktopSyncVerificationTitle": {"message": "Verifikasi sinkronisasi desktop"}, "desktopIntegrationVerificationText": {"message": "Harap verifikasi bahwa aplikasi desktop menampilkan sidik jari ini: "}, "desktopIntegrationDisabledTitle": {"message": "Integrasi peramban tidak diaktifkan"}, "desktopIntegrationDisabledDesc": {"message": "Integrasi peramban tidak diaktifkan di aplikasi Desktop Bitwarden. Silakan aktifkan di pengaturan di dalam aplikasi desktop."}, "startDesktopTitle": {"message": "Jalankan aplikasi Desktop Bitwarden"}, "startDesktopDesc": {"message": "Aplikasi Desktop Bitwarden harus dijalankan sebelum fungsi ini bisa digunakan."}, "errorEnableBiometricTitle": {"message": "Tidak bisa mengaktifkan biometrik"}, "errorEnableBiometricDesc": {"message": "Tindakan dibatalkan oleh aplikasi desktop"}, "nativeMessagingInvalidEncryptionDesc": {"message": "Aplikasi desktop membatalkan saluran komunikasi aman. <PERSON><PERSON><PERSON> coba lagi proses ini"}, "nativeMessagingInvalidEncryptionTitle": {"message": "Komunikasi desktop terputus"}, "nativeMessagingWrongUserDesc": {"message": "Aplikasi desktop masuk ke akun yang berbeda. Harap pastikan kedua aplikasi masuk ke akun yang sama."}, "nativeMessagingWrongUserTitle": {"message": "<PERSON>kun tidak cocok"}, "nativeMessagingWrongUserKeyTitle": {"message": "Kunci biometrik tidak cocok"}, "nativeMessagingWrongUserKeyDesc": {"message": "Gagal membuka dengan biometrik. Kunci rahasia biometrik gagal membuka brankas. Harap coba atur biometrik lagi."}, "biometricsNotEnabledTitle": {"message": "Biometrik tidak diaktifkan"}, "biometricsNotEnabledDesc": {"message": "Biometrik peramban mengharuskan biometrik desktop diaktifkan di pengaturan terlebih dahulu."}, "biometricsNotSupportedTitle": {"message": "Biometrik tidak didukung"}, "biometricsNotSupportedDesc": {"message": "Biometrik peramban tidak didukung di perangkat ini."}, "biometricsNotUnlockedTitle": {"message": "Pengguna terkunci atau telah keluar"}, "biometricsNotUnlockedDesc": {"message": "Harap buka kunci pengguna ini di aplikasi desktop dan coba kembali."}, "biometricsNotAvailableTitle": {"message": "Buka dengan biometrik tidak tersedia"}, "biometricsNotAvailableDesc": {"message": "Buka dengan biometrik tidak tersedia untuk saat ini. <PERSON>hon coba kembali nanti."}, "biometricsFailedTitle": {"message": "Biometrik <PERSON>"}, "biometricsFailedDesc": {"message": "Biometrik tidak dapat disel<PERSON>, pertimbangkan untuk menggunakan sebuah kata sandi utama atau keluar. Jika hal ini tetap berl<PERSON>, mohon hubungi dukungan Bitwarden."}, "nativeMessaginPermissionErrorTitle": {"message": "<PERSON>zin tidak diberikan"}, "nativeMessaginPermissionErrorDesc": {"message": "Tanpa adanya izin untuk berkomunikasi dengan Aplikasi Desktop Bitwarden kami tidak bisa menyediakan fitur biometrik di dalam ekstensi peramban. <PERSON>lakan coba lagi."}, "nativeMessaginPermissionSidebarTitle": {"message": "<PERSON><PERSON><PERSON> permin<PERSON> izin"}, "nativeMessaginPermissionSidebarDesc": {"message": "Tindakan ini tidak dapat dilakukan di sidebar, coba lagi tindakan di pop-up atau pop-out."}, "personalOwnershipSubmitError": {"message": "<PERSON><PERSON>, <PERSON><PERSON> di<PERSON>an item ke brankas personal And<PERSON>. Ubah opsi Kepemilikan ke organisasi dan pilih dari <PERSON> yang tersedia."}, "personalOwnershipPolicyInEffect": {"message": "Kebijakan organisasi memengaruhi opsi kepemilikan Anda."}, "personalOwnershipPolicyInEffectImports": {"message": "Sebuah kebijakan organisasi telah menghalangi mengimpor benda-benda ke brankas pribadi Anda."}, "domainsTitle": {"message": "Domain", "description": "A category title describing the concept of web domains"}, "blockedDomains": {"message": "Domain terblokir"}, "learnMoreAboutBlockedDomains": {"message": "Pelajari lebih lanjut tentang domain yang diblokir"}, "excludedDomains": {"message": "Domain yang Dikecualikan"}, "excludedDomainsDesc": {"message": "Bitwarden tidak akan meminta untuk menyimpan detail login untuk domain ini. Anda harus menyegarkan halaman agar perubahan diterapkan."}, "excludedDomainsDescAlt": {"message": "Bitwarden tidak akan meminta untuk menyimpan rincian login untuk domain tersebut. Anda harus menyegarkan halaman agar perubahan diterapkan."}, "blockedDomainsDesc": {"message": "<PERSON>i otomatis dan fitur terkait lain tidak akan ditawarkan bagi situs-situs web ini. Anda mesti menyegarkan halaman agar per<PERSON>han berdampak."}, "autofillBlockedNoticeV2": {"message": "Isi otomatis diblokir bagi situs web ini."}, "autofillBlockedNoticeGuidance": {"message": "Ubah ini di pengaturan"}, "change": {"message": "Ubah"}, "changePassword": {"message": "Change password", "description": "Change password button for browser at risk notification on login."}, "changeButtonTitle": {"message": "Ubah kata sandi - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "atRiskPassword": {"message": "At-risk password"}, "atRiskPasswords": {"message": "Kata sandi yang berrisiko"}, "atRiskPasswordDescSingleOrg": {"message": "$ORGANIZATION$ meminta Ada mengubah satu kata sandi karena itu berrisiko.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}}, "atRiskPasswordsDescSingleOrgPlural": {"message": "$ORGANIZATION$ meminta Anda mengubah $COUNT$ kata sandi karena mereka berrisiko.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}, "count": {"content": "$2", "example": "2"}}}, "atRiskPasswordsDescMultiOrgPlural": {"message": "Organisasi Anda meminta Anda mengubah $COUNT$ kata sandi karena mereka berrisiko.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "atRiskChangePrompt": {"message": "Your password for this site is at-risk. $ORGANIZATION$ has requested that you change it.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}, "description": "Notification body when a login triggers an at-risk password change request and the change password domain is known."}, "atRiskNavigatePrompt": {"message": "$ORGANIZATION$ wants you to change this password because it is at-risk. Navigate to your account settings to change the password.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}, "description": "Notification body when a login triggers an at-risk password change request and no change password domain is provided."}, "reviewAndChangeAtRiskPassword": {"message": "Tinjau dan ubah satu kata sandi berrisiko"}, "reviewAndChangeAtRiskPasswordsPlural": {"message": "Tinjau dan ubah $COUNT$ kata sandi berrisiko", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "changeAtRiskPasswordsFaster": {"message": "Ubah lebih cepat kata sandi yang berrisiko"}, "changeAtRiskPasswordsFasterDesc": {"message": "<PERSON><PERSON><PERSON> pengaturan <PERSON>a se<PERSON>ga Anda dapat dengan cepat mengisi otomatis kata sandi Anda dan menghasilkan kata sandi baru"}, "reviewAtRiskLogins": {"message": "Tinjau ulang info masuk yang berpotensi bahaya"}, "reviewAtRiskPasswords": {"message": "Tinjau kata sandi yang berrisiko"}, "reviewAtRiskLoginsSlideDesc": {"message": "<PERSON>a sandi organisasi Anda berrisiko karena mere<PERSON> le<PERSON>, dip<PERSON><PERSON> ulang, dan/atau terpapar.", "description": "Description of the review at-risk login slide on the at-risk password page carousel"}, "reviewAtRiskLoginSlideImgAltPeriod": {"message": "Gambaran daftar info masuk yang berpotensi bahaya."}, "generatePasswordSlideDesc": {"message": "Hasilkan kata sandi yang kuat dan unik dengan cepat dengan menu isi otomatis Bitwarden pada situs yang berpotensi bahaya.", "description": "Description of the generate password slide on the at-risk password page carousel"}, "generatePasswordSlideImgAltPeriod": {"message": "Gambaran dari menu isi otomatis Bitwarden yang menampilkan kata sandi yang dihasilkan."}, "updateInBitwarden": {"message": "<PERSON><PERSON><PERSON> di Bitwarden"}, "updateInBitwardenSlideDesc": {"message": "Bitwarden akan meminta Anda untuk memperbarui kata sandi di pengelola sandi.", "description": "Description of the update in Bitwarden slide on the at-risk password page carousel"}, "updateInBitwardenSlideImgAltPeriod": {"message": "Gambaran dari pemberitahuan Bitwarden yang meminta pengguna untuk memperbarui info masuk."}, "turnOnAutofill": {"message": "<PERSON><PERSON><PERSON><PERSON> isi otomatis"}, "turnedOnAutofill": {"message": "<PERSON>ah menyalakan isi otomatis"}, "dismiss": {"message": "<PERSON><PERSON><PERSON>"}, "websiteItemLabel": {"message": "Situs web $number$ (URI)", "placeholders": {"number": {"content": "$1", "example": "3"}}}, "excludedDomainsInvalidDomain": {"message": "$DOMAIN$ bukan domain yang valid", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "blockedDomainsSavedSuccess": {"message": "Perubahan domain yang diblokir disimpan"}, "excludedDomainsSavedSuccess": {"message": "Perubahan domain yang diabaikan telah disimpan"}, "limitSendViews": {"message": "Batasi tampilan"}, "limitSendViewsHint": {"message": "Tidak ada yang dapat melihat <PERSON>n ini setelah mencapai batasan.", "description": "Displayed under the limit views field on Send"}, "limitSendViewsCount": {"message": "$ACCESSCOUNT$ tampilan tersisa", "description": "Displayed under the limit views field on Send", "placeholders": {"accessCount": {"content": "$1", "example": "2"}}}, "send": {"message": "Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDetails": {"message": "<PERSON><PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendTypeText": {"message": "Teks"}, "sendTypeTextToShare": {"message": "Teks untuk dibagikan"}, "sendTypeFile": {"message": "Berkas"}, "allSends": {"message": "<PERSON><PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "maxAccessCountReached": {"message": "Max access count reached", "description": "This text will be displayed after a Send has been accessed the maximum amount of times."}, "hideTextByDefault": {"message": "Sembunyikan teks secara bawaan"}, "expired": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "passwordProtected": {"message": "<PERSON><PERSON><PERSON>i kata sandi"}, "copyLink": {"message": "<PERSON><PERSON>an"}, "copySendLink": {"message": "<PERSON><PERSON> tautan <PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "removePassword": {"message": "<PERSON><PERSON>"}, "delete": {"message": "Hapus"}, "removedPassword": {"message": "<PERSON><PERSON> yang <PERSON>"}, "deletedSend": {"message": "<PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLink": {"message": "<PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "disabled": {"message": "Dinonaktifkan"}, "removePasswordConfirmation": {"message": "Anda yakin ingin menghapus kata sandi?"}, "deleteSend": {"message": "Ha<PERSON> Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendConfirmation": {"message": "Anda yakin ingin menghapus Send ini?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendPermanentConfirmation": {"message": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus Send ini selamanya?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editSend": {"message": "Edit Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deletionDate": {"message": "Tanggal <PERSON>"}, "deletionDateDescV2": {"message": "Send akan dihapus selamanya pada tanggal ini.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "expirationDate": {"message": "Tanggal habis tempo"}, "oneDay": {"message": "1 hari"}, "days": {"message": "$DAYS$ hari", "placeholders": {"days": {"content": "$1", "example": "2"}}}, "custom": {"message": "Kustom"}, "sendPasswordDescV3": {"message": "Tambahkan kata sandi tidak wajib untuk penerima untuk mengakses Send ini.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createSend": {"message": "<PERSON><PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "newPassword": {"message": "Kata <PERSON> baru"}, "sendDisabled": {"message": "Send Dinonaktifkan", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDisabledWarning": {"message": "<PERSON><PERSON>, <PERSON><PERSON> hanya dapat men<PERSON><PERSON> Send yang sudah ada.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSend": {"message": "Send Dibuat ", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSendSuccessfully": {"message": "Send berhasil dibuat!", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHoursSingle": {"message": "Send akan tersedia ke setiap orang yang memiliki tautan untuk 1 jam ke depan.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHours": {"message": "Send akan tersedia ke setiap orang yang memiliki tautan untuk $HOURS$ jam ke depan.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"hours": {"content": "$1", "example": "5"}}}, "sendExpiresInDaysSingle": {"message": "Send akan tersedia ke setiap orang yang memiliki tautan untuk 1 hari ke depan.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInDays": {"message": "Send akan tersedia ke setiap orang yang memiliki tautan untuk $DAYS$ hari ke depan.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"days": {"content": "$1", "example": "5"}}}, "sendLinkCopied": {"message": "<PERSON><PERSON> disalin", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editedSend": {"message": "Send diedit", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogText": {"message": "Sembulkan ekstensi?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogDesc": {"message": "Untuk membuat sebuah berkas Send, <PERSON>a perlu menyembulkan ekstensi ke sebuah jendela baru.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLinuxChromiumFileWarning": {"message": "Untuk memilih file ini, buka <PERSON> di sidebar (jika memungkinkan) atau keluarkan menjadi window baru dengan menekan gambar ini."}, "sendFirefoxFileWarning": {"message": "Untuk memilih file menggunakan Firefox, buka ekstensi di sidebar atau keluar ke jendela baru dengan mengklik banner ini."}, "sendSafariFileWarning": {"message": "Untuk memilih file menggunakan Safari, keluar ke jendela baru dengan mengklik spanduk ini."}, "popOut": {"message": "Sembulkan"}, "sendFileCalloutHeader": {"message": "Sebelum kamu memulai"}, "expirationDateIsInvalid": {"message": "Tanggal kedaluwarsa yang diberikan tidak valid."}, "deletionDateIsInvalid": {"message": "Tanggal penghapusan yang diberikan tidak valid."}, "expirationDateAndTimeRequired": {"message": "<PERSON><PERSON><PERSON><PERSON> tanggal dan waktu kedalu<PERSON>."}, "deletionDateAndTimeRequired": {"message": "<PERSON>gal dan waktu penghapusan diperlukan."}, "dateParsingError": {"message": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat menyimpan tanggal penghapusan dan kedal<PERSON><PERSON><PERSON>."}, "hideYourEmail": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> alamat surel <PERSON>a dari penonton."}, "passwordPrompt": {"message": "Master password di<PERSON><PERSON><PERSON> kem<PERSON>i"}, "passwordConfirmation": {"message": "Konfirmasi sandi utama"}, "passwordConfirmationDesc": {"message": "Aksi ini terproteksi. Untuk melanjutkan, masukkan kembali sandi utama Anda untuk verifikasi identitas."}, "emailVerificationRequired": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "emailVerifiedV2": {"message": "<PERSON><PERSON> telah <PERSON>"}, "emailVerificationRequiredDesc": {"message": "Anda harus memverifikasi email Anda untuk menggunakan fitur ini. Anda dapat memverifikasi email Anda di brankas web."}, "updatedMasterPassword": {"message": "Kata Sand<PERSON> U<PERSON> Telah <PERSON>"}, "updateMasterPassword": {"message": "<PERSON><PERSON><PERSON>"}, "updateMasterPasswordWarning": {"message": "Kata Sandi Utama Anda baru-baru ini diubah oleh administrator organ<PERSON><PERSON>. Untuk mengaks<PERSON> brankas tersebut, <PERSON><PERSON> memperbarui Kata Sandi Utama And<PERSON>. <PERSON><PERSON>, <PERSON><PERSON> akan keluar dari sesi saat ini, yang mana mengharuskan Anda untuk login kembali. Sesi yang aktif di perangkat lain akan tetap aktif selama satu jam kedepan."}, "updateWeakMasterPasswordWarning": {"message": "Kata sandi utama Anda tidak memenuhi satu atau lebih dari kebijakan organisasi Anda. Untuk dapat mengakses branka<PERSON>, <PERSON><PERSON> harus memperbarui kata sandi utama Anda se<PERSON>. Melanjutkan akan mengeluarkan Anda dari sesi saat ini, me<PERSON><PERSON><PERSON> Anda untuk masuk kembali. Sesi aktif pada perangkat lainnya dapat tetap aktif hingga satu jam."}, "tdeDisabledMasterPasswordRequired": {"message": "Organisasi Anda telah mematikan enkripsi perangkat terpercaya. Mohon mengatur kata sandi utama untuk mengakses brankas Anda."}, "resetPasswordPolicyAutoEnroll": {"message": "Pendaftaran Otomatis"}, "resetPasswordAutoEnrollInviteWarning": {"message": "Organisasi ini memiliki kebijakan perusahaan yang secara otomatis mendaftarkan Anda dalam pengaturan ulang kata sandi. <PERSON><PERSON> menda<PERSON>, akan memungkinkan administrator organisasi untuk mengubah kata sandi utama Anda."}, "selectFolder": {"message": "<PERSON><PERSON><PERSON>..."}, "noFoldersFound": {"message": "Tidak ada folder yang di<PERSON>ukan", "description": "Used as a message within the notification bar when no folders are found"}, "orgPermissionsUpdatedMustSetPassword": {"message": "Perizinan organisasi <PERSON>a te<PERSON>, me<PERSON><PERSON>an <PERSON>a untuk mengatur kata sandi utama.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "orgRequiresYouToSetPassword": {"message": "Organisasi <PERSON>a me<PERSON>an Anda untuk mengatur sebuah kata sandi utama.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "cardMetrics": {"message": "dari $TOTAL$", "placeholders": {"total": {"content": "$1", "example": "5"}}}, "verificationRequired": {"message": "<PERSON><PERSON><PERSON><PERSON> veri<PERSON>", "description": "Default title for the user verification dialog."}, "hours": {"message": "Jam"}, "minutes": {"message": "Menit"}, "vaultTimeoutPolicyAffectingOptions": {"message": "Persyaratan kebijakan perusahaan telah diterapkan ke pilihan batas waktu Anda"}, "vaultTimeoutPolicyInEffect": {"message": "Ke<PERSON>jakan organisasi Anda memengaruhi waktu tunggu brankas Anda. Batas maksimal Waktu Tunggu Brankas yang diizinkan adalah $HOURS$ jam dan $MINUTES$ menit", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyInEffect1": {"message": "Maksimal $HOURS$ jam dan $MINUTES$ menit.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyMaximumError": {"message": "Batas waktu melebihi dari batasan yang telah ditetapkan oleh organisasi Anda: maksimal $HOURS$ jam dan $MINUTES$ menit", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyWithActionInEffect": {"message": "<PERSON><PERSON><PERSON><PERSON> organisasi Anda mempengaruhi batas waktu brankas Anda. Batas waktu maksimum yang dibolehkan adalah $HOURS$ jam dan $MINUTES$ menit. Batas waktu tindakan brankas Anda diatur ke $ACTION$.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}, "action": {"content": "$3", "example": "Lock"}}}, "vaultTimeoutActionPolicyInEffect": {"message": "<PERSON><PERSON><PERSON><PERSON> organisasi Anda telah mengatur batas waktu tindakan brankas Anda ke $ACTION$.", "placeholders": {"action": {"content": "$1", "example": "Lock"}}}, "vaultTimeoutTooLarge": {"message": "Batas waktu brankas Anda melebihi batasan yang telah ditetapkan oleh organisasi Anda."}, "vaultExportDisabled": {"message": "Ekspor Brankas Dinonaktifkan"}, "personalVaultExportPolicyInEffect": {"message": "Satu atau beberapa kebijakan organisasi mencegah Anda mengekspor brankas pribadi Anda."}, "copyCustomFieldNameInvalidElement": {"message": "Tidak dapat mengidentifikasi elemen yang valid. Coba inspeksi HTML saja."}, "copyCustomFieldNameNotUnique": {"message": "Tidak ada pengidentifikasi unik yang ditemukan."}, "removeMasterPasswordForOrganizationUserKeyConnector": {"message": "A master password is no longer required for members of the following organization. Please confirm the domain below with your organization administrator."}, "organizationName": {"message": "Organization name"}, "keyConnectorDomain": {"message": "Key Connector domain"}, "leaveOrganization": {"message": "Tinggalkan Organisasi"}, "removeMasterPassword": {"message": "<PERSON><PERSON>"}, "removedMasterPassword": {"message": "<PERSON><PERSON> utama di<PERSON>."}, "leaveOrganizationConfirmation": {"message": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin meninggalkan organisasi ini?"}, "leftOrganization": {"message": "<PERSON>a telah keluar dari organisa<PERSON>."}, "toggleCharacterCount": {"message": "Saklar hitung karakter"}, "sessionTimeout": {"message": "<PERSON><PERSON> <PERSON>a telah be<PERSON>. <PERSON><PERSON> kembali dan coba masuk lagi."}, "exportingPersonalVaultTitle": {"message": "Mengekspor brankas individu"}, "exportingIndividualVaultDescription": {"message": "<PERSON><PERSON> benda-benda brankas perorangan yang terkait dengan $EMAIL$ yang akan diekspor. Benda-benda brankas organisasi tidak akan disertakan. Hanya informasi benda brankas yang akan diekspor dan tidak menyertakan lampiran yang terkait.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingIndividualVaultWithAttachmentsDescription": {"message": "<PERSON><PERSON> benda-benda brankas satuan termasuk lampiran yang terhubung dengan $EMAIL$ yang akan diekspor. Benda brankas organisasi tidak akan disertakan", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingOrganizationVaultTitle": {"message": "Mengekspor brankas organisasi"}, "exportingOrganizationVaultDesc": {"message": "Hanya brankas organisasi yang terkait dengan $ORGANIZATION$ yang akan diekspor. Benda-benda di brankas perorangan atau organisasi lainnya tidak akan disertakan.", "placeholders": {"organization": {"content": "$1", "example": "ACME Moving Co."}}}, "error": {"message": "Galat"}, "decryptionError": {"message": "<PERSON><PERSON><PERSON>"}, "couldNotDecryptVaultItemsBelow": {"message": "Bitwarden tidak bisa mendekripsi butir brankas yang tercantum di bawah."}, "contactCSToAvoidDataLossPart1": {"message": "<PERSON><PERSON>ng<PERSON> layanan pelanggan", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "contactCSToAvoidDataLossPart2": {"message": "untuk menghindari lanjutan hilang data.", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "generateUsername": {"message": "Buat nama pengguna baru"}, "generateEmail": {"message": "Buat email"}, "spinboxBoundariesHint": {"message": "<PERSON><PERSON> harus ada di antara $MIN$ dan $MAX$.", "description": "Explains spin box minimum and maximum values to the user", "placeholders": {"min": {"content": "$1", "example": "8"}, "max": {"content": "$2", "example": "128"}}}, "passwordLengthRecommendationHint": {"message": " Gunakan $RECOMMENDED$ karakter atau lebih untuk menghasilkan kata sandi yang kuat.", "description": "Appended to `spinboxBoundariesHint` to recommend a length to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "14"}}}, "passphraseNumWordsRecommendationHint": {"message": " Gunakan $RECOMMENDED$ kata atau lebih untuk menghasilkan frasa sandi yang kuat.", "description": "Appended to `spinboxBoundariesHint` to recommend a number of words to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "6"}}}, "plusAddressedEmail": {"message": "<PERSON><PERSON> al<PERSON> plus", "description": "Username generator option that appends a random sub-address to the username. For example: <EMAIL>"}, "plusAddressedEmailDesc": {"message": "<PERSON><PERSON><PERSON> kema<PERSON> sub-addressing <PERSON><PERSON><PERSON> sure<PERSON>."}, "catchallEmail": {"message": "<PERSON><PERSON> tang<PERSON>-se<PERSON>a"}, "catchallEmailDesc": {"message": "Gunakan pengaturan kotak masuk tangkap-semua milik domain Anda."}, "random": {"message": "Acak"}, "randomWord": {"message": "Kata a<PERSON>k"}, "websiteName": {"message": "Nama situs web"}, "service": {"message": "<PERSON><PERSON><PERSON>"}, "forwardedEmail": {"message": "<PERSON><PERSON> surel yang di<PERSON>an"}, "forwardedEmailDesc": {"message": "<PERSON><PERSON><PERSON> alias sure<PERSON> dengan layanan penerusan ekstern<PERSON>."}, "forwarderDomainName": {"message": "Domain surel", "description": "Labels the domain name email forwarder service option"}, "forwarderDomainNameHint": {"message": "Pilih domain yang didukung oleh layanan terpilih", "description": "Guidance provided for email forwarding services that support multiple email domains."}, "forwarderError": {"message": "Galat $SERVICENAME$: $ERRORMESSAGE$", "description": "Reports an error returned by a forwarding service to the user.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Invalid characters in domain name."}}}, "forwarderGeneratedBy": {"message": "Dibuat oleh Bitwarden.", "description": "Displayed with the address on the forwarding service's configuration screen."}, "forwarderGeneratedByWithWebsite": {"message": "Situs web: $WEBSITE$. Dibuat oleh Bitwarden.", "description": "Displayed with the address on the forwarding service's configuration screen.", "placeholders": {"WEBSITE": {"content": "$1", "example": "www.example.com"}}}, "forwaderInvalidToken": {"message": "Token API $SERVICENAME$ tidak valid", "description": "Displayed when the user's API token is empty or rejected by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidTokenWithMessage": {"message": "Token API $SERVICENAME$ tidak valid: $ERRORMESSAGE$", "description": "Displayed when the user's API token is rejected by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwaderInvalidOperation": {"message": "$SERVICENAME$ menolak permintaan Anda. <PERSON>p hubungi penyedia layanan Anda untuk bantuan.", "description": "Displayed when the user is forbidden from using the API by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidOperationWithMessage": {"message": "$SERVICENAME$ menolak permintaan Anda: $ERRORMESSAGE$", "description": "Displayed when the user is forbidden from using the API by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwarderNoAccountId": {"message": "Gagal mendapatkan akun ID surel bertopeng dari $SERVICENAME$.", "description": "Displayed when the forwarding service fails to return an account ID.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoDomain": {"message": "Domain $SERVICENAME$ tidak valid.", "description": "Displayed when the domain is empty or domain authorization failed at the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoUrl": {"message": "URL $SERVICENAME$ tidak valid.", "description": "Displayed when the url of the forwarding service wasn't supplied.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownError": {"message": "Ter<PERSON>di galat yang tidak diketahui dari $SERVICENAME$.", "description": "Displayed when the forwarding service failed due to an unknown error.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownForwarder": {"message": "Penerus tidak diketahui: '$SERVICENAME$'.", "description": "Displayed when the forwarding service is not supported.", "placeholders": {"servicename": {"content": "$1", "example": "JustTrust.us"}}}, "hostname": {"message": "<PERSON><PERSON> host", "description": "Part of a URL."}, "apiAccessToken": {"message": "Token Akses API"}, "apiKey": {"message": "Kunci API"}, "ssoKeyConnectorError": {"message": "<PERSON>t kunci penyambung: pastikan kunci penyambung tersedia dan bekerja dengan benar."}, "premiumSubcriptionRequired": {"message": "Langganan premium diperlukan"}, "organizationIsDisabled": {"message": "Organisasi ditangguhkan."}, "disabledOrganizationFilterError": {"message": "Benda-benda di Organisasi yang ditangguhkan tidak dapat diakses. Hubungi pemilik Organisasi Anda untuk mendapatkan panduan."}, "loggingInTo": {"message": "Sedang masuk ke $DOMAIN$", "placeholders": {"domain": {"content": "$1", "example": "example.com"}}}, "serverVersion": {"message": "Versi server"}, "selfHostedServer": {"message": "dihosting mandiri"}, "thirdParty": {"message": "<PERSON><PERSON> ketiga"}, "thirdPartyServerMessage": {"message": "Tersambung ke penerapan server pihak ketiga, $SERVERNAME$. Mohon pastikan bug menggunakan server resmi, atau laporkan mereka ke server pihak ketiga.", "placeholders": {"servername": {"content": "$1", "example": "ThirdPartyServerName"}}}, "lastSeenOn": {"message": "terakhir terlihat pada: $DATE$", "placeholders": {"date": {"content": "$1", "example": "Jun 15, 2015"}}}, "loginWithMasterPassword": {"message": "<PERSON><PERSON><PERSON> dengan kata sandi utama"}, "newAroundHere": {"message": "Baru di sini?"}, "rememberEmail": {"message": "Ingat email"}, "loginWithDevice": {"message": "<PERSON><PERSON><PERSON> dengan per<PERSON>"}, "fingerprintPhraseHeader": {"message": "Frasa sidik jari"}, "fingerprintMatchInfo": {"message": "Pastikan brankas Anda terbuka dan frasa sidik jari cocok pada perangkat lainnya."}, "resendNotification": {"message": "<PERSON><PERSON> pember<PERSON>n"}, "viewAllLogInOptions": {"message": "<PERSON><PERSON> semua pilihan masuk"}, "notificationSentDevice": {"message": "Sebuah pemberitahuan dikirim ke perangkat Anda."}, "notificationSentDevicePart1": {"message": "<PERSON>uka kunci Bitwarden pada perangkat Anda atau pada"}, "notificationSentDeviceAnchor": {"message": "aplikasi web"}, "notificationSentDevicePart2": {"message": "Pastikan frasa Sidik Jari cocok dengan yang di bawah sebelum menyetujui."}, "aNotificationWasSentToYourDevice": {"message": "Se<PERSON>ah pemberitahuan telah dikirim ke perangkat Anda"}, "youWillBeNotifiedOnceTheRequestIsApproved": {"message": "<PERSON>a akan di<PERSON><PERSON>hu setelah permintaan disetu<PERSON>i"}, "needAnotherOptionV1": {"message": "<PERSON><PERSON> pilihan la<PERSON>ya?"}, "loginInitiated": {"message": "<PERSON><PERSON><PERSON> login"}, "logInRequestSent": {"message": "Permintaan terkirim"}, "exposedMasterPassword": {"message": "<PERSON><PERSON> yang <PERSON>"}, "exposedMasterPasswordDesc": {"message": "Kata sandi ditemukan di pelanggaran data. Gunakan kata sandi unik untuk melindungi akun Anda. Apakah Anda yakin ingin menggunakan kata sandi yang terpapar?"}, "weakAndExposedMasterPassword": {"message": "<PERSON>a <PERSON>a yang <PERSON> dan <PERSON>"}, "weakAndBreachedMasterPasswordDesc": {"message": "Kata sandi lemah dikenali dan ditemukan di pelanggaran data. Gunakan kata sandi unik untuk melindungi akun Anda. Apakah Anda yakin ingin menggunakan kata sandi ini?"}, "checkForBreaches": {"message": "Periksa pelanggaran data yang diketahui untuk kata sandi ini"}, "important": {"message": "Penting:"}, "masterPasswordHint": {"message": "Kata sandi utama Anda tidak dapat dipulihkan jika Anda melupakannya!"}, "characterMinimum": {"message": "Minimal $LENGTH$ karakter", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "autofillPageLoadPolicyActivated": {"message": "<PERSON><PERSON><PERSON><PERSON> organisasi Anda telah menyalakan isi otomatis ketika halaman dimuat."}, "howToAutofill": {"message": "Bagaimana cara mengisi otomatis"}, "autofillSelectInfoWithCommand": {"message": "<PERSON><PERSON>h sebuah benda dari layar ini, gunakan pintasan $COMMAND$, atau jelajahi pilihan lainnya di pengaturan.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillSelectInfoWithoutCommand": {"message": "<PERSON><PERSON><PERSON> sebuah benda dari layar ini, atau jelajahi pilihan la<PERSON>ya di pengaturan."}, "gotIt": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "autofillSettings": {"message": "Pengaturan isi otomatis"}, "autofillKeyboardShortcutSectionTitle": {"message": "Pintasan isi otomatis"}, "autofillKeyboardShortcutUpdateLabel": {"message": "Ubah pintasan"}, "autofillKeyboardManagerShortcutsLabel": {"message": "<PERSON><PERSON><PERSON>"}, "autofillShortcut": {"message": "Pintasan papan ketik isi otomatis"}, "autofillLoginShortcutNotSet": {"message": "Pintasan isi otomatis login belum diatur. Ubah ini di pengaturan peramban."}, "autofillLoginShortcutText": {"message": "Pintasan isi otomatis login adalah $COMMAND$. <PERSON><PERSON><PERSON> semua pintasan di pengaturan peramban.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillShortcutTextSafari": {"message": "Pintasan isi otomatis bawaan: $COMMAND$.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "opensInANewWindow": {"message": "<PERSON>uka di jendela baru"}, "rememberThisDeviceToMakeFutureLoginsSeamless": {"message": "Ingat perangkat ini untuk membuat login berikutnya lebih lancar"}, "deviceApprovalRequired": {"message": "<PERSON>setujuan perangkat diperlukan. <PERSON><PERSON><PERSON> sebuah pilihan persetujuan berikut:"}, "deviceApprovalRequiredV2": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> per<PERSON> diperlukan"}, "selectAnApprovalOptionBelow": {"message": "<PERSON><PERSON><PERSON> sebuah pilihan per<PERSON> berikut"}, "rememberThisDevice": {"message": "Ingat perangkat ini"}, "uncheckIfPublicDevice": {"message": "Batalkan centang jika menggunakan perangkat umum"}, "approveFromYourOtherDevice": {"message": "<PERSON><PERSON><PERSON><PERSON> dari perangkat lain milik <PERSON>"}, "requestAdminApproval": {"message": "<PERSON><PERSON> admin"}, "ssoIdentifierRequired": {"message": "Pengenal SSO organisasi <PERSON>."}, "creatingAccountOn": {"message": "Memb<PERSON>t akun pada"}, "checkYourEmail": {"message": "<PERSON><PERSON><PERSON>"}, "followTheLinkInTheEmailSentTo": {"message": "<PERSON><PERSON><PERSON> tautan pada surel yang telah dikirim"}, "andContinueCreatingYourAccount": {"message": "dan lanjutkan membuat akun <PERSON>."}, "noEmail": {"message": "Tidak punya surel?"}, "goBack": {"message": "Kembali"}, "toEditYourEmailAddress": {"message": "untuk menyunting alamat surel <PERSON>."}, "eu": {"message": "EU", "description": "European Union"}, "accessDenied": {"message": "<PERSON><PERSON><PERSON>. Anda tidak mempunyai izin untuk melihat halaman ini."}, "general": {"message": "<PERSON><PERSON>"}, "display": {"message": "Tampilan"}, "accountSuccessfullyCreated": {"message": "Akun berhasil dibuat!"}, "adminApprovalRequested": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> admin telah diminta"}, "adminApprovalRequestSentToAdmins": {"message": "Per<PERSON><PERSON><PERSON>a telah dikirim ke admin Anda."}, "troubleLoggingIn": {"message": "Kesulitan masuk?"}, "loginApproved": {"message": "<PERSON><PERSON>"}, "userEmailMissing": {"message": "<PERSON><PERSON> pengguna hilang"}, "activeUserEmailNotFoundLoggingYouOut": {"message": "<PERSON>l pengguna yang aktif tidak ditemukan. Mengeluarkan Anda."}, "deviceTrusted": {"message": "Perangkat dipercaya"}, "trustOrganization": {"message": "Percayai organisasi"}, "trust": {"message": "Percayai"}, "doNotTrust": {"message": "<PERSON><PERSON>"}, "organizationNotTrusted": {"message": "Organisasi tidak tepercaya"}, "emergencyAccessTrustWarning": {"message": "<PERSON><PERSON> keamanan akun <PERSON>, hanya konfirmasi jika Anda telah memberikan akses darurat ke pengguna ini dan sidik jari mereka cocok dengan apa yang ditampilkan pada akun mereka"}, "orgTrustWarning": {"message": "<PERSON><PERSON> kea<PERSON>n akun <PERSON>, hanya lanju<PERSON>kan apabila <PERSON>a adalah anggota dari organisasi ini, pem<PERSON>han akun telah aktif, dan sidik jari yang ditampilkan berikut cocok dengan sidik jari organisasi."}, "orgTrustWarning1": {"message": "Organisasi ini memiliki kebijakan perusahaan yang akan mendaftarkan Anda pada pemulihan akun. Pendaftaran ini akan membolehkan pengelola organisasi untuk mengubah kata sandi Anda. Hanya lanjutkan jika Anda mengenali organisasi ini dan frasa sidik jari yang ditampilkan berikut cocok dengan sidik jari organisasi."}, "trustUser": {"message": "Percayai pengguna"}, "sendsTitleNoItems": {"message": "Send sensitive information safely", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendsBodyNoItems": {"message": "Share files and data securely with anyone, on any platform. Your information will remain end-to-end encrypted while limiting exposure.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "inputRequired": {"message": "<PERSON><PERSON><PERSON> ini harus diisi."}, "required": {"message": "wajib di<PERSON>"}, "search": {"message": "<PERSON><PERSON>"}, "inputMinLength": {"message": "Masukan sekurang-kurangnya $COUNT$ karakter.", "placeholders": {"count": {"content": "$1", "example": "8"}}}, "inputMaxLength": {"message": "Masukan tidak boleh melebihi $COUNT$ karakter.", "placeholders": {"count": {"content": "$1", "example": "20"}}}, "inputForbiddenCharacters": {"message": "Karakter berikut tidak diperbolehkan: $CHARACTERS$", "placeholders": {"characters": {"content": "$1", "example": "@, #, $, %"}}}, "inputMinValue": {"message": "<PERSON><PERSON> masukan sekurang-kurangnya $MIN$.", "placeholders": {"min": {"content": "$1", "example": "8"}}}, "inputMaxValue": {"message": "<PERSON><PERSON> masukan tidak boleh melebihi $MAX$.", "placeholders": {"max": {"content": "$1", "example": "100"}}}, "multipleInputEmails": {"message": "1 atau lebih surel tidak valid"}, "inputTrimValidator": {"message": "Ma<PERSON>kan tidak boleh berisi hanya spasi kosong.", "description": "Notification to inform the user that a form's input can't contain only whitespace."}, "inputEmail": {"message": "<PERSON><PERSON><PERSON> bukan sebuah alamat surel."}, "fieldsNeedAttention": {"message": "$COUNT$ bidang di atas memerlukan perhatian Anda.", "placeholders": {"count": {"content": "$1", "example": "4"}}}, "singleFieldNeedsAttention": {"message": "1 bidang memerlukan perhatian Anda."}, "multipleFieldsNeedAttention": {"message": "$COUNT$ bidang memerlukan perhatian Anda.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "selectPlaceholder": {"message": "-- <PERSON><PERSON><PERSON> --"}, "multiSelectPlaceholder": {"message": "-- <PERSON><PERSON><PERSON> untuk menyaring --"}, "multiSelectLoading": {"message": "Mengam<PERSON> pilihan..."}, "multiSelectNotFound": {"message": "Tidak ada benda yang di<PERSON>ukan"}, "multiSelectClearAll": {"message": "<PERSON><PERSON><PERSON><PERSON> semua"}, "plusNMore": {"message": "+ $QUANTITY$ lagi", "placeholders": {"quantity": {"content": "$1", "example": "5"}}}, "submenu": {"message": "Submenu"}, "toggleCollapse": {"message": "Saklar lipat", "description": "Toggling an expand/collapse state."}, "aliasDomain": {"message": "Domain alias"}, "passwordRepromptDisabledAutofillOnPageLoad": {"message": "<PERSON><PERSON> dengan meminta ulang kata sandi utama tidak dapat diisikan otomatis ketika halaman dimuat. Isi otomatis ketika halaman dimuat dimatikan.", "description": "Toast message for describing that master password re-prompt cannot be autofilled on page load."}, "autofillOnPageLoadSetToDefault": {"message": "<PERSON><PERSON> otomatis ketika halaman dimuat telah diatur untuk menggunakan pengaturan bawaan.", "description": "Toast message for informing the user that autofill on page load has been set to the default setting."}, "turnOffMasterPasswordPromptToEditField": {"message": "Matikan minta ulang kata sandi utama untuk menyunting kolom ini", "description": "Message appearing below the autofill on load message when master password reprompt is set for a vault item."}, "toggleSideNavigation": {"message": "<PERSON><PERSON><PERSON> bilah isi navigasi"}, "skipToContent": {"message": "Lo<PERSON>at ke konten"}, "bitwardenOverlayButton": {"message": "Tombol menu isi otomatis Bitwarden", "description": "Page title for the iframe containing the overlay button"}, "toggleBitwardenVaultOverlay": {"message": "Saklar menu isi otomatis Bitwarden", "description": "Screen reader and tool tip label for the overlay button"}, "bitwardenVault": {"message": "<PERSON>u isi otomatis <PERSON>", "description": "Page title in overlay"}, "unlockYourAccountToViewMatchingLogins": {"message": "<PERSON><PERSON> akun Anda untuk melihat login yang cocok", "description": "Text to display in overlay when the account is locked."}, "unlockYourAccountToViewAutofillSuggestions": {"message": "<PERSON><PERSON> akun Anda untuk melihat saran isi otomatis", "description": "Text to display in overlay when the account is locked."}, "unlockAccount": {"message": "<PERSON><PERSON> akun", "description": "Button text to display in overlay when the account is locked."}, "unlockAccountAria": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, memb<PERSON><PERSON> di jendela baru", "description": "Screen reader text (aria-label) for unlock account button in overlay"}, "totpCodeAria": {"message": "Kode Verifikasi Kata Sandi Sekali-Waktu Berbasis Waktu", "description": "Aria label for the totp code displayed in the inline menu for autofill"}, "totpSecondsSpanAria": {"message": "Waktu tersisa sebelum TOTP sekarang kadaluwarsa", "description": "Aria label for the totp seconds displayed in the inline menu for autofill"}, "fillCredentialsFor": {"message": "<PERSON>i tanda pengenal untuk", "description": "Screen reader text for when overlay item is in focused"}, "partialUsername": {"message": "<PERSON><PERSON> pen<PERSON> se<PERSON>ian", "description": "Screen reader text for when a login item is focused where a partial username is displayed. SR will announce this phrase before reading the text of the partial username"}, "noItemsToShow": {"message": "Tidak ada benda untuk ditampilkan", "description": "Text to show in overlay if there are no matching items"}, "newItem": {"message": "<PERSON>a baru", "description": "Button text to display in overlay when there are no matching items"}, "addNewVaultItem": {"message": "Tambah benda brankas baru", "description": "Screen reader text (aria-label) for new item button in overlay"}, "newLogin": {"message": "Login baru", "description": "Button text to display within inline menu when there are no matching items on a login field"}, "addNewLoginItemAria": {"message": "Tambah baru untuk benda login, membukanya di jendela baru", "description": "Screen reader text (aria-label) for new login button within inline menu"}, "newCard": {"message": "Kartu baru", "description": "Button text to display within inline menu when there are no matching items on a credit card field"}, "addNewCardItemAria": {"message": "Tambah brankas baru untuk benda kartu, membukanya di jendela baru", "description": "Screen reader text (aria-label) for new card button within inline menu"}, "newIdentity": {"message": "Pengenal baru", "description": "Button text to display within inline menu when there are no matching items on an identity field"}, "addNewIdentityItemAria": {"message": "Tambah brankas baru untuk benda pengenal, membukanya di jendela baru", "description": "Screen reader text (aria-label) for new identity button within inline menu"}, "bitwardenOverlayMenuAvailable": {"message": "<PERSON>u isi otomatis Bitwarden tersedia. <PERSON><PERSON> tombol panah ke bawah untuk memilih.", "description": "Screen reader text for announcing when the overlay opens on the page"}, "turnOn": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ignore": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "importData": {"message": "Impor data", "description": "Used for the header of the import dialog, the import button and within the file-password-prompt"}, "importError": {"message": "Kesalahan impor"}, "importErrorDesc": {"message": "Ada masalah dengan data yang Anda coba impor. <PERSON><PERSON> se<PERSON>aikan kesalahan yang tercantum di bawah ini pada berkas sumber Anda dan coba lagi."}, "resolveTheErrorsBelowAndTryAgain": {"message": "<PERSON><PERSON><PERSON><PERSON> masalah berikut dan coba lagi."}, "description": {"message": "Keterangan"}, "importSuccess": {"message": "Data berhasil diimpor"}, "importSuccessNumberOfItems": {"message": "Sejumlah $AMOUNT$ benda telah diimpor.", "placeholders": {"amount": {"content": "$1", "example": "2"}}}, "tryAgain": {"message": "Coba lagi"}, "verificationRequiredForActionSetPinToContinue": {"message": "Diperlukan verifikasi untuk tindakan ini. Atur PIN untuk melanjutkan."}, "setPin": {"message": "Atur PIN"}, "verifyWithBiometrics": {"message": "Verifikasi dengan biometrik"}, "awaitingConfirmation": {"message": "<PERSON><PERSON><PERSON> konfi<PERSON><PERSON>"}, "couldNotCompleteBiometrics": {"message": "Tidak dapat melengkapi biometrik."}, "needADifferentMethod": {"message": "<PERSON><PERSON> cara lain?"}, "useMasterPassword": {"message": "<PERSON><PERSON><PERSON> kata sandi utama"}, "usePin": {"message": "Gunakan PIN"}, "useBiometrics": {"message": "Gunakan biometrik"}, "enterVerificationCodeSentToEmail": {"message": "Ma<PERSON>kkan kode verifikasi yang dikirim ke surel Anda."}, "resendCode": {"message": "<PERSON><PERSON> kode"}, "total": {"message": "<PERSON><PERSON><PERSON>"}, "importWarning": {"message": "Anda mengimpor data ke $ORGANIZATION$. Data Anda dapat dibagikan dengan anggota organisasi ini. Apakah Anda ingin melanjutkan?", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "duoHealthCheckResultsInNullAuthUrlError": {"message": "Gagal menyambungkan dengan layanan Duo. Gunakan cara masuk dua-langkah lainnya atau hubungi Duo untuk mendapatkan panduan."}, "duoRequiredForAccount": {"message": "Login dua-langkah Duo diperlukan untuk akun <PERSON>a."}, "popoutExtension": {"message": "Sembulkan ekstensi"}, "launchDuo": {"message": "Luncurkan Duo"}, "importFormatError": {"message": "Data tidak diformat dengan benar. Harap periksa berkas impor Anda dan coba lagi."}, "importNothingError": {"message": "Tidak ada yang diimpor."}, "importEncKeyError": {"message": "Tidak dapat mendekripsi berkas yang diekspor. Kunci enkripsi Anda tidak cocok dengan kunci enkripsi yang digunakan untuk mengekspor data tersebut."}, "invalidFilePassword": {"message": "Kata sandi berkas tidak valid, harap menggunakan kata sandi yang Anda masukkan saat Anda membuat berkas ekspor."}, "destination": {"message": "<PERSON><PERSON><PERSON>"}, "learnAboutImportOptions": {"message": "P<PERSON>jari tentang pilihan impor Anda"}, "selectImportFolder": {"message": "<PERSON><PERSON><PERSON> folder"}, "selectImportCollection": {"message": "<PERSON><PERSON><PERSON>"}, "importTargetHint": {"message": "<PERSON><PERSON>h pilihan ini jika Anda ingin isi dari berkas yang diimpor dipindah ke $DESTINATION$", "description": "Located as a hint under the import target. Will be appended by either folder or collection, depending if the user is importing into an individual or an organizational vault.", "placeholders": {"destination": {"content": "$1", "example": "folder or collection"}}}, "importUnassignedItemsError": {"message": "<PERSON><PERSON><PERSON> berisi benda-benda yang belum ditetapkan."}, "selectFormat": {"message": "Pilih format untuk berkas yang diimpor"}, "selectImportFile": {"message": "<PERSON><PERSON><PERSON> berkas yang akan diimpor"}, "chooseFile": {"message": "<PERSON><PERSON><PERSON>"}, "noFileChosen": {"message": "Tidak ada berkas yang dipilih"}, "orCopyPasteFileContents": {"message": "atau salin/tempel isi berkas yang diimpor"}, "instructionsFor": {"message": "Petunjuk $NAME$", "description": "The title for the import tool instructions.", "placeholders": {"name": {"content": "$1", "example": "LastPass (csv)"}}}, "confirmVaultImport": {"message": "Konfirmasi impor brankas"}, "confirmVaultImportDesc": {"message": "Berkas ini dilindungi oleh kata sandi. Masukkan kata sandi berkas untuk mengimpor data."}, "confirmFilePassword": {"message": "Konfirmasi kata sandi berkas"}, "exportSuccess": {"message": "Data brankas berhasil diekspor"}, "typePasskey": {"message": "<PERSON><PERSON><PERSON> sandi"}, "accessing": {"message": "Sedang mengakses"}, "loggedInExclamation": {"message": "Sudah masuk!"}, "passkeyNotCopied": {"message": "<PERSON>nci sandi tidak akan disalin"}, "passkeyNotCopiedAlert": {"message": "Kunci sandi tidak akan disalin ke benda yang digandakan. <PERSON><PERSON><PERSON>h Anda ingin melanjutkan menggandakan benda ini?"}, "passkeyFeatureIsNotImplementedForAccountsWithoutMasterPassword": {"message": "Verifikasi diperlukan oleh situs yang menyelenggarakan. Fitur ini belum diterapkan untuk akun tanpa kata sandi utama."}, "logInWithPasskeyQuestion": {"message": "Ma<PERSON>k dengan kunci sandi?"}, "passkeyAlreadyExists": {"message": "<PERSON>nci sandi sudah ada untuk aplikasi ini."}, "noPasskeysFoundForThisApplication": {"message": "Tidak ada kunci sandi yang ditemukan untuk aplikasi ini."}, "noMatchingPasskeyLogin": {"message": "Anda tidak memiliki login yang cocok untuk situs ini."}, "noMatchingLoginsForSite": {"message": "Tidak ada login yang cocok untuk situs ini"}, "searchSavePasskeyNewLogin": {"message": "Cari atau simpan kunci sandi sebagai login baru"}, "confirm": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "savePasskey": {"message": "Simpan kunci sandi"}, "savePasskeyNewLogin": {"message": "Simpan kunci sandi sebagai login baru"}, "chooseCipherForPasskeySave": {"message": "<PERSON><PERSON><PERSON> sebuah login untuk menyimpan kunci sandi ini"}, "chooseCipherForPasskeyAuth": {"message": "<PERSON><PERSON><PERSON> sebuah kunci sandi untuk masuk"}, "passkeyItem": {"message": "<PERSON><PERSON> kunci sandi"}, "overwritePasskey": {"message": "<PERSON><PERSON> kunci sandi?"}, "overwritePasskeyAlert": {"message": "Benda ini telah memiliki kunci sandi. <PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menimpa kunci sandi yang sekarang?"}, "featureNotSupported": {"message": "Kemampuan belum didukung"}, "yourPasskeyIsLocked": {"message": "Otentikasi diperlukan untuk menggunakan kunci sandi. Verifikasikan diri Anda untuk melanjutkan."}, "multifactorAuthenticationCancelled": {"message": "Otentikasi multifaktor dibatalkan"}, "noLastPassDataFound": {"message": "Tidak ada data LastPass yang di<PERSON>ukan"}, "incorrectUsernameOrPassword": {"message": "<PERSON>a pengguna atau kata sandi salah"}, "incorrectPassword": {"message": "Kata sandi salah"}, "incorrectCode": {"message": "<PERSON><PERSON> salah"}, "incorrectPin": {"message": "PIN salah"}, "multifactorAuthenticationFailed": {"message": "Otentikasi multifaktor gagal"}, "includeSharedFolders": {"message": "Sertakan folder yang dibagikan"}, "lastPassEmail": {"message": "<PERSON><PERSON>"}, "importingYourAccount": {"message": "<PERSON><PERSON><PERSON><PERSON> akun <PERSON>..."}, "lastPassMFARequired": {"message": "Otentikasi multifaktor LastPass diperlukan"}, "lastPassMFADesc": {"message": "Ma<PERSON>kka<PERSON> kode sandi sekali-waktu dari aplikasi otentikator Anda"}, "lastPassOOBDesc": {"message": "Setujui permintaan masuk di aplikasi otentikasi Anda atau masukkan kode sandi sekali-waktu."}, "passcode": {"message": "<PERSON><PERSON> sandi"}, "lastPassMasterPassword": {"message": "<PERSON>a sandi utama <PERSON>Pass"}, "lastPassAuthRequired": {"message": "Otentikasi LastPass diperlukan"}, "awaitingSSO": {"message": "Menunggu otentikasi SSO"}, "awaitingSSODesc": {"message": "Harap lanjutkan masuk menggunakan tanda pengenal perusa<PERSON>an <PERSON>."}, "seeDetailedInstructions": {"message": "<PERSON><PERSON> petunjuk lengkap pada situs bantuan kami di", "description": "This is followed a by a hyperlink to the help website."}, "importDirectlyFromLastPass": {"message": "<PERSON><PERSON><PERSON> langsung dari <PERSON>"}, "importFromCSV": {"message": "Impor dari CSV"}, "lastPassTryAgainCheckEmail": {"message": "Coba lagi atau cari surel dari LastPass untuk memverifikasi bahwa ini adalah <PERSON>a."}, "collection": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lastPassYubikeyDesc": {"message": "<PERSON><PERSON><PERSON><PERSON> yang ditautkan dengan akun LastPass Anda ke port USB komputer Anda, kemudian sentuh tombolnya."}, "switchAccount": {"message": "<PERSON><PERSON> akun"}, "switchAccounts": {"message": "<PERSON><PERSON> akun"}, "switchToAccount": {"message": "Ganti ke akun"}, "activeAccount": {"message": "Akun aktif"}, "bitwardenAccount": {"message": "<PERSON><PERSON><PERSON>"}, "availableAccounts": {"message": "<PERSON>kun yang tersedia"}, "accountLimitReached": {"message": "Batas akun tercapai. <PERSON>luar dari akun untuk menambahkan akun lain."}, "active": {"message": "aktif"}, "locked": {"message": "terkun<PERSON>"}, "unlocked": {"message": "terb<PERSON>"}, "server": {"message": "server"}, "hostedAt": {"message": "dihost di"}, "useDeviceOrHardwareKey": {"message": "<PERSON>akan perangkat Anda atau kunci perangkat keras"}, "justOnce": {"message": "<PERSON><PERSON>"}, "alwaysForThisSite": {"message": "Se<PERSON>u untuk situs ini"}, "domainAddedToExcludedDomains": {"message": "$DOMAIN$ ditambahkan ke domain yang dikecualikan.", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "commonImportFormats": {"message": "Format umum", "description": "Label indicating the most common import formats"}, "confirmContinueToBrowserSettingsTitle": {"message": "Lanjutkan ke pengaturan peramban?", "description": "Title for dialog which asks if the user wants to proceed to a relevant browser settings page"}, "confirmContinueToHelpCenter": {"message": "Lanjutkan ke Pusat Bantuan?", "description": "Title for dialog which asks if the user wants to proceed to a relevant Help Center page"}, "confirmContinueToHelpCenterPasswordManagementContent": {"message": "Ganti pengaturan isi otomatis dan pengelolaan kata sandi peramban <PERSON>.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser password management settings"}, "confirmContinueToHelpCenterKeyboardShortcutsContent": {"message": "Anda dapat melihat dan mengatur pintasan ekstensi di pengaturan peramban <PERSON>a.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser keyboard shortcut settings"}, "confirmContinueToBrowserPasswordManagementSettingsContent": {"message": "Ganti pengaturan isi otomatis dan pengelolaan kata sandi peramban <PERSON>.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's password management settings page"}, "confirmContinueToBrowserKeyboardShortcutSettingsContent": {"message": "Anda dapat melihat dan mengatur pintasan ekstensi di pengaturan peramban <PERSON>a.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's keyboard shortcut settings page"}, "overrideDefaultBrowserAutofillTitle": {"message": "Jadikan Bitwarden sebagai pengelola kata sandi bawaan <PERSON>?", "description": "Dialog title facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutofillDescription": {"message": "Mengabaikan pilihan ini dapat mengakibatkan perseteruan antara saran isi otomatis Bitwarden dengan peramban Anda.", "description": "Dialog message facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutoFillSettings": {"message": "Jadikan Bitwarden sebagai pengelola kata sandi bawaan <PERSON>a", "description": "Label for the setting that allows overriding the default browser autofill settings"}, "privacyPermissionAdditionNotGrantedTitle": {"message": "Tidak dapat mengatur Bitwarden sebagai pengelola kata sandi bawaan", "description": "Title for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "privacyPermissionAdditionNotGrantedDescription": {"message": "Anda harus mengizinkan perizinan privasi peramban kepada Bitwarden untuk mengaturnya sebagai pengelola kata sandi bawaan.", "description": "Description for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "makeDefault": {"message": "<PERSON><PERSON><PERSON> bawaan", "description": "Button text for the setting that allows overriding the default browser autofill settings"}, "saveCipherAttemptSuccess": {"message": "Kredensial berhasil disimpan!", "description": "Notification message for when saving credentials has succeeded."}, "passwordSaved": {"message": "Kata sandi telah disimpan!", "description": "Notification message for when saving credentials has succeeded."}, "updateCipherAttemptSuccess": {"message": "Kredensial berhasil diperbarui!", "description": "Notification message for when updating credentials has succeeded."}, "passwordUpdated": {"message": "Kata sandi telah diperbarui!", "description": "Notification message for when updating credentials has succeeded."}, "saveCipherAttemptFailed": {"message": "Gagal menyimpan kredensial. Periksa konsol untuk rinciannya.", "description": "Notification message for when saving credentials has failed."}, "success": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "removePasskey": {"message": "<PERSON><PERSON> kun<PERSON> sandi"}, "passkeyRemoved": {"message": "<PERSON><PERSON>i sandi di<PERSON>pus"}, "autofillSuggestions": {"message": "Saran isi otomatis"}, "itemSuggestions": {"message": "<PERSON>ir yang disarankan"}, "autofillSuggestionsTip": {"message": "Simpan benda login untuk situs ini ke isi otomatis"}, "yourVaultIsEmpty": {"message": "Brankas Anda kosong"}, "noItemsMatchSearch": {"message": "Tidak ada benda yang cocok dengan pencarian <PERSON>a"}, "clearFiltersOrTryAnother": {"message": "<PERSON><PERSON><PERSON><PERSON> atau coba cari kata lainnya"}, "copyInfoTitle": {"message": "Menyalin info - $ITEMNAME$", "description": "Title for a button that opens a menu with options to copy information from an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "copyNoteTitle": {"message": "Menyalin Catatan - $ITEMNAME$", "description": "Title for a button copies a note to the clipboard.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Note Item"}}}, "moreOptionsLabel": {"message": "<PERSON><PERSON><PERSON>, $ITEMNAME$", "description": "Aria label for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "moreOptionsTitle": {"message": "<PERSON><PERSON><PERSON> - $ITEMNAME$", "description": "Title for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitle": {"message": "LIhat benda - $ITEMNAME$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitleWithField": {"message": "Lihat benda - $ITEMNAME$ - $FIELD$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "autofillTitle": {"message": "<PERSON>i otomati<PERSON> - $ITEMNAME$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "autofillTitleWithField": {"message": "<PERSON>i otomatis - $ITEMNAME$ - $FIELD$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "copyFieldValue": {"message": "Salin $FIELD$, $VALUE$", "description": "Title for a button that copies a field value to the clipboard.", "placeholders": {"field": {"content": "$1", "example": "Username"}, "value": {"content": "$2", "example": "Foo"}}}, "noValuesToCopy": {"message": "Tidak ada nilai untuk disalin"}, "assignToCollections": {"message": "Menempatkan ke koleksi"}, "copyEmail": {"message": "<PERSON><PERSON> surel"}, "copyPhone": {"message": "Salin nomor telepon"}, "copyAddress": {"message": "<PERSON><PERSON>"}, "adminConsole": {"message": "<PERSON><PERSON><PERSON>"}, "accountSecurity": {"message": "<PERSON><PERSON><PERSON> akun"}, "notifications": {"message": "Pemberitahuan"}, "appearance": {"message": "Tampilan"}, "errorAssigningTargetCollection": {"message": "Gagal menetapkan ke koleksi yang dituju."}, "errorAssigningTargetFolder": {"message": "Gagal menetapkan ke folder yang dituju."}, "viewItemsIn": {"message": "Lihat benda-benda di $NAME$", "description": "Button to view the contents of a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "backTo": {"message": "Kembali ke $NAME$", "description": "Navigate back to a previous folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "new": {"message": "<PERSON><PERSON>"}, "removeItem": {"message": "Buang $NAME$", "description": "Remove a selected option, such as a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "itemsWithNoFolder": {"message": "Benda-<PERSON>a tanpa folder"}, "itemDetails": {"message": "<PERSON><PERSON><PERSON> benda"}, "itemName": {"message": "<PERSON>a benda"}, "organizationIsDeactivated": {"message": "Organisasi dinonaktifkan"}, "owner": {"message": "Pemilik"}, "selfOwnershipLabel": {"message": "<PERSON><PERSON>", "description": "Used as a label to indicate that the user is the owner of an item."}, "contactYourOrgAdmin": {"message": "Benda-benda di organisasi yang dinonaktifkan tidak dapat diakses. Hubungi pemilik organisasi Anda untuk mendapatkan panduan."}, "additionalInformation": {"message": "Informasi tambahan"}, "itemHistory": {"message": "Riwayat benda"}, "lastEdited": {"message": "<PERSON><PERSON><PERSON> disunting"}, "ownerYou": {"message": "Pemilik: <PERSON><PERSON>"}, "linked": {"message": "Terkait"}, "copySuccessful": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "upload": {"message": "<PERSON><PERSON><PERSON>"}, "addAttachment": {"message": "Tambahkan lampiran"}, "maxFileSizeSansPunctuation": {"message": "Ukuran berkas maksimal adalah 500 MB"}, "deleteAttachmentName": {"message": "Hapus lampiran $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadAttachmentName": {"message": "Unduh $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadBitwarden": {"message": "<PERSON><PERSON><PERSON>"}, "downloadBitwardenOnAllDevices": {"message": "<PERSON><PERSON><PERSON>"}, "getTheMobileApp": {"message": "Dapatkan aplikasi ponsel"}, "getTheMobileAppDesc": {"message": "<PERSON>ks<PERSON> kata sandi <PERSON>a di perjalanan dengan aplikasi ponsel Bitwarden."}, "getTheDesktopApp": {"message": "Dapatkan aplikasi desktop"}, "getTheDesktopAppDesc": {"message": "<PERSON><PERSON><PERSON> brankas <PERSON>a tanpa se<PERSON>ah peramban, kem<PERSON>an atur buka dengan biometrik untuk mempercepat membuka di aplikasi desktop dan ekstensi peramban."}, "downloadFromBitwardenNow": {"message": "Unduh dari bitwarden.com sekarang"}, "getItOnGooglePlay": {"message": "Get it on Google Play"}, "downloadOnTheAppStore": {"message": "Download on the App Store"}, "permanentlyDeleteAttachmentConfirmation": {"message": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus lampiran ini selamanya?"}, "premium": {"message": "Premium"}, "freeOrgsCannotUseAttachments": {"message": "Organisasi gratis tidak dapat menggunakan lampiran"}, "filters": {"message": "Penyaring"}, "filterVault": {"message": "Penyaring brankas"}, "filterApplied": {"message": "<PERSON><PERSON> saringan di<PERSON>"}, "filterAppliedPlural": {"message": "$COUNT$ saringan diterapkan", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "personalDetails": {"message": "Rincian p<PERSON>i"}, "identification": {"message": "Pengenalan"}, "contactInfo": {"message": "<PERSON><PERSON> kontak"}, "downloadAttachment": {"message": "Unduh - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Your File"}}}, "cardNumberEndsWith": {"message": "nomor kartu berakhiran", "description": "Used within the inline menu to provide an aria description when users are attempting to fill a card cipher."}, "loginCredentials": {"message": "Kredensial login"}, "authenticatorKey": {"message": "<PERSON><PERSON><PERSON>"}, "autofillOptions": {"message": "<PERSON><PERSON><PERSON> isi otomatis"}, "websiteUri": {"message": "Situs web (URI)"}, "websiteUriCount": {"message": "$COUNT$ Situs web (URI)", "description": "Label for an input field that contains a website URI. The input field is part of a list of fields, and the count indicates the position of the field in the list.", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "websiteAdded": {"message": "Situs web ditambahkan"}, "addWebsite": {"message": "Tambah situs web"}, "deleteWebsite": {"message": "Hapus situs web"}, "defaultLabel": {"message": "Bawaan ($VALUE$)", "description": "A label that indicates the default value for a field with the current default value in parentheses.", "placeholders": {"value": {"content": "$1", "example": "Base domain"}}}, "showMatchDetection": {"message": "Tampilkan deteksi kecocokan $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "hideMatchDetection": {"message": "Sembunyikan deteksi kecocokan $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "autoFillOnPageLoad": {"message": "Isi otomatis ketika halaman dimuat?"}, "cardExpiredTitle": {"message": "Kartu kadal<PERSON>"}, "cardExpiredMessage": {"message": "<PERSON><PERSON>a telah <PERSON>, perbarui informasi kartu"}, "cardDetails": {"message": "Rincian kartu"}, "cardBrandDetails": {"message": "Rincian $BRAND$", "placeholders": {"brand": {"content": "$1", "example": "Visa"}}}, "enableAnimations": {"message": "<PERSON><PERSON><PERSON><PERSON> an<PERSON>"}, "showAnimations": {"message": "<PERSON><PERSON><PERSON><PERSON> anima<PERSON>"}, "addAccount": {"message": "Tambah akun"}, "loading": {"message": "Memuat"}, "data": {"message": "Data"}, "passkeys": {"message": "<PERSON><PERSON><PERSON> sandi", "description": "A section header for a list of passkeys."}, "passwords": {"message": "<PERSON><PERSON>", "description": "A section header for a list of passwords."}, "logInWithPasskeyAriaLabel": {"message": "<PERSON><PERSON><PERSON> dengan kunci sandi", "description": "ARIA label for the inline menu button that logs in with a passkey."}, "assign": {"message": "Terapkan"}, "bulkCollectionAssignmentDialogDescriptionSingular": {"message": "Hanya anggota organisasi dengan akses ke koleksi berikut yang dapat melihat isinya."}, "bulkCollectionAssignmentDialogDescriptionPlural": {"message": "Hanya anggota organisasi dengan akses ke koleksi berikut yang dapat melihat isinya."}, "bulkCollectionAssignmentWarning": {"message": "Anda telah memilih $TOTAL_COUNT$ benda. Anda tidak dapat memperbarui $READONLY_COUNT$ dari benda karena Anda tidak memiliki izin untuk menyunting.", "placeholders": {"total_count": {"content": "$1", "example": "10"}, "readonly_count": {"content": "$2"}}}, "addField": {"message": "Tambahkan bidang"}, "add": {"message": "Tambah"}, "fieldType": {"message": "<PERSON><PERSON>"}, "fieldLabel": {"message": "Label bidang"}, "textHelpText": {"message": "Gunakan bidang teks untuk data seperti pertanyaan keamanan"}, "hiddenHelpText": {"message": "Gunakan bidang tersembunyi untuk data sensitif seperti kata sandi"}, "checkBoxHelpText": {"message": "Gunakan kotak centang jika Anda ingin mengisi sebuah kotak centang di formullir, seperti mengingat surel"}, "linkedHelpText": {"message": "Gunakan bidang tertaut ketika Anda mengalami masalah pengisian otomatis untuk situs web tertentu."}, "linkedLabelHelpText": {"message": "Masukkan id, name, aria-label, atau placeholder html dari bidang."}, "editField": {"message": "<PERSON><PERSON> bidang"}, "editFieldLabel": {"message": "Sunting $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "deleteCustomField": {"message": "Hapus $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "fieldAdded": {"message": "$LABEL$ ditambahkan", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderToggleButton": {"message": "Urutkan $LABEL$. Gunakan tombol panah untuk memindahkan benda ke atas atau ke bawah.", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderWebsiteUriButton": {"message": "Urutkan URI situs web. Gunakan tombol panah untuk memindahkan benda ke atas atau ke bawah."}, "reorderFieldUp": {"message": "$LABEL$ dipindah ke atas, terletak di $INDEX$ dari $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "selectCollectionsToAssign": {"message": "<PERSON><PERSON><PERSON> koleksi untuk ditetapkan"}, "personalItemTransferWarningSingular": {"message": "1 benda akan dipindahkan selamanya ke organisasi terpilih. Anda tidak akan lagi memiliki benda ini."}, "personalItemsTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ benda akan dipindahkan selamanya ke organisasi terpilih. Anda tidak akan lagi memiliki benda-benda tersebut.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}}}, "personalItemWithOrgTransferWarningSingular": {"message": "1 benda akan dipindahkan selamanya ke $ORG$. Anda tidak akan lagi memiliki benda ini.", "placeholders": {"org": {"content": "$1", "example": "Organization name"}}}, "personalItemsWithOrgTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ benda akan dipindahkan selamanya ke $ORG$. Anda tidak akan lagi memiliki benda-benda tersebut.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}, "org": {"content": "$2", "example": "Organization name"}}}, "successfullyAssignedCollections": {"message": "Successfully assigned collections"}, "nothingSelected": {"message": "You have not selected anything."}, "itemsMovedToOrg": {"message": "Items moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "itemMovedToOrg": {"message": "Item moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "reorderFieldDown": {"message": "$LABEL$ moved down, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "itemLocation": {"message": "<PERSON><PERSON>"}, "fileSend": {"message": "File Send"}, "fileSends": {"message": "File Sends"}, "textSend": {"message": "Text Send"}, "textSends": {"message": "Text Sends"}, "accountActions": {"message": "<PERSON><PERSON><PERSON> akun"}, "showNumberOfAutofillSuggestions": {"message": "Show number of login autofill suggestions on extension icon"}, "showQuickCopyActions": {"message": "Show quick copy actions on Vault"}, "systemDefault": {"message": "Baku sistem"}, "enterprisePolicyRequirementsApplied": {"message": "Persyaratan kebijakan perusahaan telah diterapkan ke pengaturan ini"}, "sshPrivateKey": {"message": "Kunci privat"}, "sshPublicKey": {"message": "Kunci publik"}, "sshFingerprint": {"message": "<PERSON><PERSON> jari"}, "sshKeyAlgorithm": {"message": "<PERSON>ipe kunci"}, "sshKeyAlgorithmED25519": {"message": "ED25519"}, "sshKeyAlgorithmRSA2048": {"message": "RSA 2048-Bit"}, "sshKeyAlgorithmRSA3072": {"message": "RSA 3072-Bit"}, "sshKeyAlgorithmRSA4096": {"message": "RSA 4096-Bit"}, "retry": {"message": "Coba lagi"}, "vaultCustomTimeoutMinimum": {"message": "Minimum custom timeout is 1 minute."}, "additionalContentAvailable": {"message": "Additional content is available"}, "fileSavedToDevice": {"message": "File saved to device. Manage from your device downloads."}, "showCharacterCount": {"message": "Tunjukkan cacah karakter"}, "hideCharacterCount": {"message": "Hide character count"}, "itemsInTrash": {"message": "Items in trash"}, "noItemsInTrash": {"message": "No items in trash"}, "noItemsInTrashDesc": {"message": "Items you delete will appear here and be permanently deleted after 30 days"}, "trashWarning": {"message": "Items that have been in trash more than 30 days will automatically be deleted"}, "restore": {"message": "Rest<PERSON>"}, "deleteForever": {"message": "Delete forever"}, "noEditPermissions": {"message": "You don't have permission to edit this item"}, "biometricsStatusHelptextUnlockNeeded": {"message": "Biometric unlock is unavailable because PIN or password unlock is required first."}, "biometricsStatusHelptextHardwareUnavailable": {"message": "Biometric unlock is currently unavailable."}, "biometricsStatusHelptextAutoSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextManualSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextDesktopDisconnected": {"message": "Biometric unlock is unavailable because the Bitwarden desktop app is closed."}, "biometricsStatusHelptextNotEnabledInDesktop": {"message": "Biometric unlock is unavailable because it is not enabled for $EMAIL$ in the Bitwarden desktop app.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "biometricsStatusHelptextUnavailableReasonUnknown": {"message": "Biometric unlock is currently unavailable for an unknown reason."}, "unlockVault": {"message": "Unlock your vault in seconds"}, "unlockVaultDesc": {"message": "You can customize your unlock and timeout settings to more quickly access your vault."}, "unlockPinSet": {"message": "Unlock PIN set"}, "authenticating": {"message": "Authenticating"}, "fillGeneratedPassword": {"message": "Fill generated password", "description": "Heading for the password generator within the inline menu"}, "passwordRegenerated": {"message": "Kata sandi dibuat ulang", "description": "Notification message for when a password has been regenerated"}, "saveToBitwarden": {"message": "Save to Bitwarden", "description": "Confirmation message for saving a login to Bitwarden"}, "spaceCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the space key in screen reader content as a readable word"}, "tildeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ~ key in screen reader content as a readable word"}, "backtickCharacterDescriptor": {"message": "<PERSON>da petik terbalik", "description": "Represents the ` key in screen reader content as a readable word"}, "exclamationCharacterDescriptor": {"message": "<PERSON>da seru", "description": "Represents the ! key in screen reader content as a readable word"}, "atSignCharacterDescriptor": {"message": "Tanda pada", "description": "Represents the @ key in screen reader content as a readable word"}, "hashSignCharacterDescriptor": {"message": "<PERSON>da pagar", "description": "Represents the # key in screen reader content as a readable word"}, "dollarSignCharacterDescriptor": {"message": "<PERSON><PERSON> dolar", "description": "Represents the $ key in screen reader content as a readable word"}, "percentSignCharacterDescriptor": {"message": "<PERSON><PERSON>sen", "description": "Represents the % key in screen reader content as a readable word"}, "caretCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ^ key in screen reader content as a readable word"}, "ampersandCharacterDescriptor": {"message": "<PERSON>da dan", "description": "Represents the & key in screen reader content as a readable word"}, "asteriskCharacterDescriptor": {"message": "<PERSON><PERSON> bin<PERSON>g", "description": "Represents the * key in screen reader content as a readable word"}, "parenLeftCharacterDescriptor": {"message": "Tanda kurung kiri", "description": "Represents the ( key in screen reader content as a readable word"}, "parenRightCharacterDescriptor": {"message": "Tanda kurung kanan", "description": "Represents the ) key in screen reader content as a readable word"}, "hyphenCharacterDescriptor": {"message": "<PERSON><PERSON> bawah", "description": "Represents the _ key in screen reader content as a readable word"}, "underscoreCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the - key in screen reader content as a readable word"}, "plusCharacterDescriptor": {"message": "<PERSON>da tambah", "description": "Represents the + key in screen reader content as a readable word"}, "equalsCharacterDescriptor": {"message": "<PERSON>da sama dengan", "description": "Represents the = key in screen reader content as a readable word"}, "braceLeftCharacterDescriptor": {"message": "<PERSON><PERSON> kurawal kiri", "description": "Represents the { key in screen reader content as a readable word"}, "braceRightCharacterDescriptor": {"message": "<PERSON><PERSON> kurawal kanan", "description": "Represents the } key in screen reader content as a readable word"}, "bracketLeftCharacterDescriptor": {"message": "Tanda kurung siku kiri", "description": "Represents the [ key in screen reader content as a readable word"}, "bracketRightCharacterDescriptor": {"message": "Tanda kurung siku kanan", "description": "Represents the ] key in screen reader content as a readable word"}, "pipeCharacterDescriptor": {"message": "Garis tegak lurus", "description": "Represents the | key in screen reader content as a readable word"}, "backSlashCharacterDescriptor": {"message": "<PERSON><PERSON> miring terbalik", "description": "Represents the back slash key in screen reader content as a readable word"}, "colonCharacterDescriptor": {"message": "Tanda titik dua", "description": "Represents the : key in screen reader content as a readable word"}, "semicolonCharacterDescriptor": {"message": "Tanda titik koma", "description": "Represents the ; key in screen reader content as a readable word"}, "doubleQuoteCharacterDescriptor": {"message": "<PERSON>da petik ganda", "description": "Represents the double quote key in screen reader content as a readable word"}, "singleQuoteCharacterDescriptor": {"message": "Tanda petik tunggal", "description": "Represents the ' key in screen reader content as a readable word"}, "lessThanCharacterDescriptor": {"message": "Tanda kurang dari", "description": "Represents the < key in screen reader content as a readable word"}, "greaterThanCharacterDescriptor": {"message": "<PERSON>da lebih besar dari", "description": "Represents the > key in screen reader content as a readable word"}, "commaCharacterDescriptor": {"message": "<PERSON>da koma", "description": "Represents the , key in screen reader content as a readable word"}, "periodCharacterDescriptor": {"message": "Tanda titik", "description": "Represents the . key in screen reader content as a readable word"}, "questionCharacterDescriptor": {"message": "<PERSON>da tanya", "description": "Represents the ? key in screen reader content as a readable word"}, "forwardSlashCharacterDescriptor": {"message": "Tanda garis miring ke depan", "description": "Represents the / key in screen reader content as a readable word"}, "lowercaseAriaLabel": {"message": "<PERSON><PERSON><PERSON> k<PERSON>"}, "uppercaseAriaLabel": {"message": "<PERSON><PERSON><PERSON>"}, "generatedPassword": {"message": "Kata sandi yang di<PERSON>kan"}, "compactMode": {"message": "<PERSON> ringkas"}, "beta": {"message": "Beta"}, "extensionWidth": {"message": "<PERSON><PERSON>"}, "wide": {"message": "<PERSON><PERSON>"}, "extraWide": {"message": "Ekstra lebar"}, "sshKeyWrongPassword": {"message": "Kata sandi yang Anda masukkan tidak benar."}, "importSshKey": {"message": "Impor"}, "confirmSshKeyPassword": {"message": "Konfirmasi kata sandi"}, "enterSshKeyPasswordDesc": {"message": "Masukkan kata sandi untuk kunci SSH."}, "enterSshKeyPassword": {"message": "<PERSON><PERSON><PERSON><PERSON> kata sandi"}, "invalidSshKey": {"message": "Kunci SSH tidak valid"}, "sshKeyTypeUnsupported": {"message": "Tipe kunci SSH tidak didukung"}, "importSshKeyFromClipboard": {"message": "<PERSON><PERSON><PERSON> kunci dari papan klip"}, "sshKeyImported": {"message": "Kunci SSH sukses diimpor"}, "cannotRemoveViewOnlyCollections": {"message": "Anda tidak dapat menghapus koleksi dengan izin hanya lihat: $COLLECTIONS$", "placeholders": {"collections": {"content": "$1", "example": "Work, Personal"}}}, "updateDesktopAppOrDisableFingerprintDialogTitle": {"message": "Harap perbarui aplikasi desktop Anda"}, "updateDesktopAppOrDisableFingerprintDialogMessage": {"message": "Untuk memakai pembuka kunci biometrik, harap perbarui aplikasi desktop Anda, atau matikan buka kunci sidik jari dalam pengaturan desktop."}, "changeAtRiskPassword": {"message": "Ubah kata sandi yang berrisiko"}, "settingsVaultOptions": {"message": "Vault options"}, "emptyVaultDescription": {"message": "The vault protects more than just your passwords. Store secure logins, IDs, cards and notes securely here."}, "introCarouselLabel": {"message": "Welcome to Bitwarden"}, "securityPrioritized": {"message": "Security, prioritized"}, "securityPrioritizedBody": {"message": "Save logins, cards, and identities to your secure vault. Bitwarden uses zero-knowledge, end-to-end encryption to protect what’s important to you."}, "quickLogin": {"message": "Quick and easy login"}, "quickLoginBody": {"message": "Set up biometric unlock and autofill to log into your accounts without typing a single letter."}, "secureUser": {"message": "Level up your logins"}, "secureUserBody": {"message": "Use the generator to create and save strong, unique passwords for all your accounts."}, "secureDevices": {"message": "Your data, when and where you need it"}, "secureDevicesBody": {"message": "Save unlimited passwords across unlimited devices with Bitwarden mobile, browser, and desktop apps."}, "nudgeBadgeAria": {"message": "1 notification"}, "emptyVaultNudgeTitle": {"message": "Import existing passwords"}, "emptyVaultNudgeBody": {"message": "Use the importer to quickly transfer logins to Bitwarden without manually adding them."}, "emptyVaultNudgeButton": {"message": "Import now"}, "hasItemsVaultNudgeTitle": {"message": "Welcome to your vault!"}, "hasItemsVaultNudgeBodyOne": {"message": "Autofill items for the current page"}, "hasItemsVaultNudgeBodyTwo": {"message": "Favorite items for easy access"}, "hasItemsVaultNudgeBodyThree": {"message": "Search your vault for something else"}, "newLoginNudgeTitle": {"message": "Save time with autofill"}, "newLoginNudgeBodyOne": {"message": "Include a", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyBold": {"message": "Situs web", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyTwo": {"message": "agar info masuk ini muncul sebagai saran pengisian otomatis.", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newCardNudgeTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> daring yang lancar"}, "newCardNudgeBody": {"message": "Dengan kartu, isi otomatis dengan mudah formulir pembayaran secara aman dan akurat."}, "newIdentityNudgeTitle": {"message": "Sederhanakan pembuatan akun"}, "newIdentityNudgeBody": {"message": "Dengan identitas, isi otomatis dengan cepat formulir pendaftaran atau kontrak yang panjang."}, "newNoteNudgeTitle": {"message": "Menjaga data sensitif Anda tetap aman"}, "newNoteNudgeBody": {"message": "<PERSON><PERSON> catatan, simpan secara aman data sensitif seperti rincian perbankan atau asuransi."}, "newSshNudgeTitle": {"message": "Akses SSH yang ramah pengembang"}, "newSshNudgeBodyOne": {"message": "Simpan kunci-kunci Anda dan sambungkan dengan agen SSH untuk otentikasi yang cepat dan terenkripsi.", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "newSshNudgeBodyTwo": {"message": "<PERSON><PERSON><PERSON><PERSON> lebih lanjut tentang agen SSH", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "generatorNudgeTitle": {"message": "Quickly create passwords"}, "generatorNudgeBodyOne": {"message": "Easily create strong and unique passwords by clicking on", "description": "Two part message", "example": "Easily create strong and unique passwords by clicking on {icon} to help you keep your logins secure."}, "generatorNudgeBodyTwo": {"message": "to help you keep your logins secure.", "description": "Two part message", "example": "Easily create strong and unique passwords by clicking on {icon} to help you keep your logins secure."}, "generatorNudgeBodyAria": {"message": "Easily create strong and unique passwords by clicking on the Generate password button to help you keep your logins secure.", "description": "Aria label for the body content of the generator nudge"}, "noPermissionsViewPage": {"message": "You do not have permissions to view this page. Try logging in with a different account."}}
<form #form (ngSubmit)="submit()" [appApiAction]="formPromise">
  <header>
    <div class="left">
      <button type="button" routerLink="/login">{{ "cancel" | i18n }}</button>
    </div>
    <h1 class="center">
      <span class="title">{{ "setMasterPassword" | i18n }}</span>
    </h1>
    <div class="right">
      <button type="submit" [disabled]="form.loading">
        <span [hidden]="form.loading">{{ "submit" | i18n }}</span>
        <i class="bwi bwi-spinner bwi-lg bwi-spin" [hidden]="!form.loading" aria-hidden="true"></i>
      </button>
    </div>
  </header>
  <main tabindex="-1">
    <div class="full-loading-spinner" *ngIf="syncLoading">
      <i class="bwi bwi-spinner bwi-spin bwi-3x" aria-hidden="true"></i>
    </div>
    <div *ngIf="!syncLoading">
      <div class="box">
        <p
          class="tw-px-4"
          *ngIf="
            forceSetPasswordReason ==
              ForceSetPasswordReason.TdeUserWithoutPasswordHasPasswordResetPermission;
            else defaultCardDesc
          "
        >
          {{ "orgPermissionsUpdatedMustSetPassword" | i18n }}
        </p>

        <ng-template #defaultCardDesc>
          <p class="tw-px-4">{{ "orgRequiresYouToSetPassword" | i18n }}</p>
        </ng-template>

        <app-callout
          type="warning"
          title="{{ 'resetPasswordPolicyAutoEnroll' | i18n }}"
          *ngIf="resetPasswordAutoEnroll"
        >
          {{ "resetPasswordAutoEnrollInviteWarning" | i18n }}
        </app-callout>
        <app-callout
          type="info"
          [enforcedPolicyOptions]="enforcedPolicyOptions"
          *ngIf="enforcedPolicyOptions"
        >
        </app-callout>
      </div>
      <div class="box">
        <div class="box-content">
          <div class="box-content-row" appBoxRow>
            <div class="box-content-row-flex">
              <div class="row-main">
                <label for="masterPassword">
                  {{ "masterPass" | i18n }}
                  <strong class="sub-label text-{{ color }}" *ngIf="text">
                    {{ text }}
                  </strong>
                </label>
                <input
                  id="masterPassword"
                  type="{{ showPassword ? 'text' : 'password' }}"
                  name="MasterPassword"
                  aria-describedby="masterPasswordHelp"
                  class="monospaced"
                  [(ngModel)]="masterPassword"
                  required
                  appInputVerbatim
                />
              </div>
              <div class="action-buttons">
                <button
                  type="button"
                  class="row-btn"
                  appStopClick
                  appA11yTitle="{{ 'toggleVisibility' | i18n }}"
                  (click)="togglePassword(false)"
                  [attr.aria-pressed]="showPassword"
                >
                  <i
                    class="bwi bwi-lg"
                    aria-hidden="true"
                    [ngClass]="{ 'bwi-eye': !showPassword, 'bwi-eye-slash': showPassword }"
                  ></i>
                </button>
              </div>
            </div>

            <app-password-strength
              [password]="masterPassword"
              [email]="email"
              (passwordStrengthResult)="getStrengthResult($event)"
              (passwordScoreColor)="getPasswordScoreText($event)"
            >
            </app-password-strength>
          </div>
        </div>
        <div id="masterPasswordHelp" class="box-footer">
          {{ "masterPassDesc" | i18n }}
        </div>
      </div>
      <div class="box">
        <div class="box-content">
          <div class="box-content-row" appBoxRow>
            <div class="box-content-row-flex">
              <div class="row-main">
                <label for="masterPasswordRetype">{{ "reTypeMasterPass" | i18n }}</label>
                <input
                  id="masterPasswordRetype"
                  type="password"
                  name="MasterPasswordRetype"
                  class="monospaced"
                  [(ngModel)]="masterPasswordRetype"
                  required
                  appInputVerbatim
                  autocomplete="new-password"
                />
              </div>
              <div class="action-buttons">
                <button
                  type="button"
                  class="row-btn"
                  appStopClick
                  appA11yTitle="{{ 'toggleVisibility' | i18n }}"
                  (click)="togglePassword(true)"
                  [attr.aria-pressed]="showPassword"
                >
                  <i
                    class="bwi bwi-lg"
                    aria-hidden="true"
                    [ngClass]="{ 'bwi-eye': !showPassword, 'bwi-eye-slash': showPassword }"
                  ></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="box last">
        <div class="box-content">
          <div class="box-content-row" appBoxRow>
            <label for="hint">{{ "masterPassHint" | i18n }}</label>
            <input
              id="hint"
              type="text"
              name="Hint"
              aria-describedby="hintHelp"
              [(ngModel)]="hint"
            />
          </div>
        </div>
        <div id="hintHelp" class="box-footer">
          {{ "masterPassHintDesc" | i18n }}
        </div>
      </div>
    </div>
  </main>
</form>

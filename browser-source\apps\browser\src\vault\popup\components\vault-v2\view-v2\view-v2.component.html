<popup-page>
  <popup-header slot="header" [pageTitle]="headerText" showBackButton>
    <app-pop-out slot="end" />
  </popup-header>

  <app-cipher-view *ngIf="cipher" [cipher]="cipher"></app-cipher-view>

  <popup-footer slot="footer" *ngIf="showFooter$ | async">
    <button
      *ngIf="!cipher.isDeleted"
      buttonType="primary"
      type="button"
      bitButton
      (click)="editCipher()"
    >
      {{ "edit" | i18n }}
    </button>

    <button
      *ngIf="cipher.isDeleted && cipher.permissions.restore"
      buttonType="primary"
      type="button"
      bitButton
      [bitAction]="restore"
    >
      {{ "restore" | i18n }}
    </button>

    <button
      slot="end"
      *ngIf="canDeleteCipher$ | async"
      [bitAction]="delete"
      type="button"
      buttonType="danger"
      bitIconButton="bwi-trash"
      [appA11yTitle]="(cipher.isDeleted ? 'deleteForever' : 'delete') | i18n"
    ></button>
  </popup-footer>
</popup-page>

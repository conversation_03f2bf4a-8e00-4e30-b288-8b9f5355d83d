name: Release Desktop
run-name: Release Desktop ${{ inputs.release_type }}

on:
  workflow_dispatch:
    inputs:
      release_type:
        description: 'Release Type'
        required: true
        default: 'Release'
        type: choice
        options:
          - Release
          - Dry Run

defaults:
  run:
    shell: bash

permissions:
  contents: read

jobs:
  setup:
    name: Setup
    runs-on: ubuntu-22.04
    permissions:
      contents: write
    outputs:
      release_version: ${{ steps.version.outputs.version }}
      release_channel: ${{ steps.release_channel.outputs.channel }}
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Branch check
        if: ${{ github.event.inputs.release_type != 'Dry Run' }}
        run: |
          if [[ "$GITHUB_REF" != "refs/heads/rc" ]] && [[ "$GITHUB_REF" != "refs/heads/hotfix-rc-desktop" ]]; then
            echo "==================================="
            echo "[!] Can only release from the 'rc' or 'hotfix-rc-desktop' branches"
            echo "==================================="
            exit 1
          fi

      - name: Check Release Version
        id: version
        uses: bitwarden/gh-actions/release-version-check@main
        with:
          release-type: ${{ inputs.release_type }}
          project-type: ts
          file: apps/desktop/src/package.json
          monorepo: true
          monorepo-project: desktop

      - name: Get Version Channel
        id: release_channel
        run: |
          case "${{ steps.version.outputs.version }}" in
            *"alpha"*)
              echo "channel=alpha" >> $GITHUB_OUTPUT
              echo "[!] We do not yet support 'alpha'"
              exit 1
              ;;
            *"beta"*)
              echo "channel=beta" >> $GITHUB_OUTPUT
              ;;
            *)
              echo "channel=latest" >> $GITHUB_OUTPUT
              ;;
          esac

      - name: Download all artifacts
        if: ${{ github.event.inputs.release_type != 'Dry Run' }}
        uses: bitwarden/gh-actions/download-artifacts@main
        with:
          workflow: build-desktop.yml
          workflow_conclusion: success
          branch: ${{ github.ref_name }}
          path: apps/desktop/artifacts

      - name: Dry Run - Download all artifacts
        if: ${{ github.event.inputs.release_type == 'Dry Run' }}
        uses: bitwarden/gh-actions/download-artifacts@main
        with:
          workflow: build-desktop.yml
          workflow_conclusion: success
          branch: main
          path: apps/desktop/artifacts

      - name: Rename .pkg to .pkg.archive
        env:
          PKG_VERSION: ${{ steps.version.outputs.version }}
        working-directory: apps/desktop/artifacts
        run: mv Bitwarden-${{ env.PKG_VERSION }}-universal.pkg Bitwarden-${{ env.PKG_VERSION }}-universal.pkg.archive

      - name: Create Release
        uses: ncipollo/release-action@cdcc88a9acf3ca41c16c37bb7d21b9ad48560d87 # v1.15.0
        if: ${{ steps.release_channel.outputs.channel == 'latest' && github.event.inputs.release_type != 'Dry Run' }}
        env:
          PKG_VERSION: ${{ steps.version.outputs.version }}
          RELEASE_CHANNEL: ${{ steps.release_channel.outputs.channel }}
        with:
          artifacts: "apps/desktop/artifacts/Bitwarden-${{ env.PKG_VERSION }}-amd64.deb,
            apps/desktop/artifacts/Bitwarden-${{ env.PKG_VERSION }}-x86_64.rpm,
            apps/desktop/artifacts/Bitwarden-${{ env.PKG_VERSION }}-x64.freebsd,
            apps/desktop/artifacts/bitwarden_${{ env.PKG_VERSION }}_amd64.snap,
            apps/desktop/artifacts/Bitwarden-${{ env.PKG_VERSION }}-x86_64.AppImage,
            apps/desktop/artifacts/Bitwarden-Portable-${{ env.PKG_VERSION }}.exe,
            apps/desktop/artifacts/Bitwarden-Installer-${{ env.PKG_VERSION }}.exe,
            apps/desktop/artifacts/Bitwarden-${{ env.PKG_VERSION }}-ia32-store.appx,
            apps/desktop/artifacts/Bitwarden-${{ env.PKG_VERSION }}-ia32.appx,
            apps/desktop/artifacts/bitwarden-${{ env.PKG_VERSION }}-ia32.nsis.7z,
            apps/desktop/artifacts/Bitwarden-${{ env.PKG_VERSION }}-x64-store.appx,
            apps/desktop/artifacts/Bitwarden-${{ env.PKG_VERSION }}-x64.appx,
            apps/desktop/artifacts/bitwarden-${{ env.PKG_VERSION }}-x64.nsis.7z,
            apps/desktop/artifacts/Bitwarden-${{ env.PKG_VERSION }}-arm64-store.appx,
            apps/desktop/artifacts/Bitwarden-${{ env.PKG_VERSION }}-arm64.appx,
            apps/desktop/artifacts/bitwarden-${{ env.PKG_VERSION }}-arm64.nsis.7z,
            apps/desktop/artifacts/bitwarden.${{ env.PKG_VERSION }}.nupkg,
            apps/desktop/artifacts/Bitwarden-${{ env.PKG_VERSION }}-universal-mac.zip,
            apps/desktop/artifacts/Bitwarden-${{ env.PKG_VERSION }}-universal.dmg,
            apps/desktop/artifacts/Bitwarden-${{ env.PKG_VERSION }}-universal.dmg.blockmap,
            apps/desktop/artifacts/Bitwarden-${{ env.PKG_VERSION }}-universal.pkg.archive,
            apps/desktop/artifacts/${{ env.RELEASE_CHANNEL }}.yml,
            apps/desktop/artifacts/${{ env.RELEASE_CHANNEL }}-linux.yml,
            apps/desktop/artifacts/${{ env.RELEASE_CHANNEL }}-mac.yml"
          commit: ${{ github.sha }}
          tag: desktop-v${{ env.PKG_VERSION }}
          name: Desktop v${{ env.PKG_VERSION }}
          body: "<insert release notes here>"
          token: ${{ secrets.GITHUB_TOKEN }}
          draft: true

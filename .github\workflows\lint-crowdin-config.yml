name: <PERSON><PERSON> Config

on:
  pull_request:
    types: [opened, synchronize]
    paths:
      - '**/crowdin.yml'
permissions: {}

jobs:
  lint-crowdin-config:
    name: <PERSON><PERSON> Config ${{ matrix.app.name }}
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        app: [
          { name: 'web', project_id: '308189', config_path: 'apps/web/crowdin.yml' },
          { name: 'desktop', project_id: '299360', config_path: 'apps/desktop/crowdin.yml' },
          { name: 'browser', project_id: '268134', config_path: 'apps/browser/crowdin.yml' }
        ]
    steps:
      - name: Check out repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 1  
      - name: Login to Azure
        uses: Azure/login@e15b166166a8746d1a47596803bd8c1b595455cf # v1.6.0
        with:
          creds: ${{ secrets.AZURE_KV_CI_SERVICE_PRINCIPAL }}
      - name: Retrieve secrets
        id: retrieve-secrets
        uses: bitwarden/gh-actions/get-keyvault-secrets@main
        with:
          keyvault: "bitwarden-ci"
          secrets: "crowdin-api-token"
      - name: Lint ${{ matrix.app.name }} config
        uses: crowdin/github-action@f214c8723025f41fc55b2ad26e67b60b80b1885d # v2.7.1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CROWDIN_PROJECT_ID: ${{ matrix.app.project_id }}
          CROWDIN_API_TOKEN: ${{ steps.retrieve-secrets.outputs.crowdin-api-token }}
        with:
          dryrun_action: true
          command: 'config lint'
          command_args: '--verbose -c ${{ matrix.app.config_path }}'
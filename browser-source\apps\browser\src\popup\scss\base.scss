@import "variables.scss";

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  overflow: hidden;
  min-height: 600px;
  height: 100%;

  &.body-sm {
    min-height: 500px;
  }

  &.body-xs {
    min-height: 400px;
  }

  &.body-xxs {
    min-height: 300px;
  }

  &.body-3xs {
    min-height: 240px;
  }

  &.body-full {
    min-height: unset;
    width: 100%;
    height: 100%;

    & body {
      width: 100%;
    }
  }
}

html,
body {
  font-family: $font-family-sans-serif;
  font-size: $font-size-base;
  line-height: $line-height-base;
  -webkit-font-smoothing: antialiased;
}

body {
  width: 380px;
  height: 100%;
  position: relative;
  min-height: inherit;
  overflow: hidden;
  color: $text-color;
  background-color: $background-color;

  @include themify($themes) {
    color: themed("textColor");
    background-color: themed("backgroundColor");
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: $font-family-sans-serif;
  font-size: $font-size-base;
  font-weight: normal;
}

p {
  margin-bottom: 10px;
}

ul,
ol {
  margin-bottom: 10px;
}

img {
  border: none;
}

a:not(popup-page a, popup-tab-navigation a) {
  text-decoration: none;

  @include themify($themes) {
    color: themed("primaryColor");
  }

  &:hover,
  &:focus {
    @include themify($themes) {
      color: darken(themed("primaryColor"), 6%);
    }
  }
}

input:not(bit-form-field input, bit-search input),
select:not(bit-form-field select),
textarea:not(bit-form-field textarea) {
  @include themify($themes) {
    color: themed("textColor");
    background-color: themed("inputBackgroundColor");
  }
}

input,
select,
textarea,
button:not(bit-chip-select button) {
  font-size: $font-size-base;
  font-family: $font-family-sans-serif;
}

input[type*="date"] {
  @include themify($themes) {
    color-scheme: themed("dateInputColorScheme");
  }
}

::-webkit-calendar-picker-indicator {
  @include themify($themes) {
    filter: themed("webkitCalendarPickerFilter");
  }
}

::-webkit-calendar-picker-indicator:hover {
  @include themify($themes) {
    filter: themed("webkitCalendarPickerHoverFilter");
  }
  cursor: pointer;
}

select {
  width: 100%;
  padding: 0.35rem;
}

button {
  cursor: pointer;
}

textarea {
  resize: vertical;
}

app-root > div {
  height: 100%;
}

main::-webkit-scrollbar,
cdk-virtual-scroll-viewport::-webkit-scrollbar,
.vault-select::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

main::-webkit-scrollbar-track,
.vault-select::-webkit-scrollbar-track {
  background-color: transparent;
}

cdk-virtual-scroll-viewport::-webkit-scrollbar-track {
  @include themify($themes) {
    background-color: themed("backgroundColor");
  }
}

main::-webkit-scrollbar-thumb,
cdk-virtual-scroll-viewport::-webkit-scrollbar-thumb,
.vault-select::-webkit-scrollbar-thumb {
  border-radius: 10px;
  margin-right: 1px;

  @include themify($themes) {
    background-color: themed("scrollbarColor");
  }

  &:hover {
    @include themify($themes) {
      background-color: themed("scrollbarHoverColor");
    }
  }
}

header:not(bit-callout header, bit-dialog header, popup-page header) {
  height: 44px;
  display: flex;

  &:not(.no-theme) {
    border-bottom: 1px solid #000000;

    @include themify($themes) {
      color: themed("headerColor");
      background-color: themed("headerBackgroundColor");
      border-bottom-color: themed("headerBorderColor");
    }
  }

  .header-content {
    display: flex;
    flex: 1 1 auto;
  }

  .header-content > .right,
  .header-content > .right > .right {
    height: 100%;
  }

  .left,
  .right {
    flex: 1;
    display: flex;
    min-width: -webkit-min-content; /* Workaround to Chrome bug */
    .header-icon {
      margin-right: 5px;
    }
  }

  .right {
    justify-content: flex-end;
    align-items: center;
    app-avatar {
      max-height: 30px;
      margin-right: 5px;
    }
  }

  .center {
    display: flex;
    align-items: center;
    text-align: center;
    min-width: 0;
  }

  .login-center {
    margin: auto;
  }

  app-pop-out > button,
  div > button:not(app-current-account button):not(.home-acc-switcher-btn),
  div > a {
    border: none;
    padding: 0 10px;
    text-decoration: none;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    height: 100%;
    white-space: pre;

    &:not(.home-acc-switcher-btn):hover,
    &:not(.home-acc-switcher-btn):focus {
      @include themify($themes) {
        background-color: themed("headerBackgroundHoverColor");
        color: themed("headerColor");
      }
    }

    &[disabled] {
      opacity: 0.65;
      cursor: default !important;
      background-color: inherit !important;
    }

    i + span {
      margin-left: 5px;
    }
  }

  app-pop-out {
    display: flex;
    padding-right: 0.5em;
  }

  .title {
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .search {
    padding: 7px 10px;
    width: 100%;
    text-align: left;
    position: relative;
    display: flex;

    .bwi {
      position: absolute;
      top: 15px;
      left: 20px;

      @include themify($themes) {
        color: themed("headerInputPlaceholderColor");
      }
    }

    input:not(bit-form-field input) {
      width: 100%;
      margin: 0;
      border: none;
      padding: 5px 10px 5px 30px;
      border-radius: $border-radius;

      @include themify($themes) {
        background-color: themed("headerInputBackgroundColor");
        color: themed("headerInputColor");
      }

      &::selection {
        @include themify($themes) {
          // explicitly set text selection to invert foreground/background
          background-color: themed("headerInputColor");
          color: themed("headerInputBackgroundColor");
        }
      }

      &:focus {
        border-radius: $border-radius;
        outline: none;

        @include themify($themes) {
          background-color: themed("headerInputBackgroundFocusColor");
        }
      }

      &::-webkit-input-placeholder {
        @include themify($themes) {
          color: themed("headerInputPlaceholderColor");
        }
      }
      /** make the cancel button visible in both dark/light themes **/
      &[type="search"]::-webkit-search-cancel-button {
        -webkit-appearance: none;
        appearance: none;
        height: 15px;
        width: 15px;
        background-repeat: no-repeat;
        mask-image: url("../images/close-button-white.svg");
        -webkit-mask-image: url("../images/close-button-white.svg");
        @include themify($themes) {
          background-color: themed("headerInputColor");
        }
      }
    }
  }

  .left + .search,
  .left + .sr-only + .search {
    padding-left: 0;

    .bwi {
      left: 10px;
    }
  }

  .search + .right {
    margin-left: -10px;
  }
}

.content {
  padding: 15px 5px;
}

app-root {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 980;
  @include themify($themes) {
    background-color: themed("backgroundColor");
  }
}

// Adds padding on each side of the content if opened in a tab
@media only screen and (min-width: 601px) {
  header,
  main {
    padding: 0 calc((100% - 500px) / 2);
  }
}

main:not(popup-page main) {
  position: absolute;
  top: 44px;
  bottom: 0;
  left: 0;
  right: 0;
  overflow-y: auto;
  overflow-x: hidden;

  @include themify($themes) {
    background-color: themed("backgroundColor");
  }

  &.no-header {
    top: 0;
  }

  &.flex {
    display: flex;
    flex-flow: column;
    height: calc(100% - 44px);
  }
}

.center-content,
.no-items,
.full-loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  flex-direction: column;
  flex-grow: 1;
}

.no-items,
.full-loading-spinner {
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  .no-items-image {
    @include themify($themes) {
      content: url("../images/search-desktop" + themed("svgSuffix"));
    }
  }

  .bwi {
    margin-bottom: 10px;

    @include themify($themes) {
      color: themed("disabledIconColor");
    }
  }
}

// cdk-virtual-scroll
.cdk-virtual-scroll-viewport {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

.cdk-virtual-scroll-content-wrapper {
  width: 100%;
}

name: Repository management

on:
  workflow_dispatch:
    inputs:
      task:
        default: "Version Bump"
        description: "Task to execute"
        options:
          - "Version Bump"
          - "Version Bump and Cut rc"
        required: true
        type: choice
      bump_browser:
        description: "Bump Browser version?"
        type: boolean
        default: false
      bump_cli:
        description: "Bump CLI version?"
        type: boolean
        default: false
      bump_desktop:
        description: "Bump Desktop version?"
        type: boolean
        default: false
      bump_web:
        description: "Bump Web version?"
        type: boolean
        default: false
      target_ref:
        default: "main"
        description: "Branch/Tag to target for cut"
        required: true
        type: string
      version_number_override:
        description: "New version override (leave blank for automatic calculation, example: '2024.1.0')"
        required: false
        type: string
permissions: {}
jobs:
  setup:
    name: Setup
    runs-on: ubuntu-24.04
    outputs:
      branch: ${{ steps.set-branch.outputs.branch }}
    steps:
      - name: Set branch
        id: set-branch
        env:
          TASK: ${{ inputs.task }}
        run: |
          if [[ "$TASK" == "Version Bump" ]]; then
            BRANCH="none"
          elif [[ "$TASK" == "Version Bump and Cut rc" ]]; then
            BRANCH="rc"
          fi

          echo "branch=$BRANCH" >> $GITHUB_OUTPUT
  bump_version:
    name: Bump Version
    if: ${{ always() }}
    runs-on: ubuntu-24.04
    needs: setup
    outputs:
      version_browser: ${{ steps.set-final-version-output.outputs.version_browser }}
      version_cli: ${{ steps.set-final-version-output.outputs.version_cli }}
      version_desktop: ${{ steps.set-final-version-output.outputs.version_desktop }}
      version_web: ${{ steps.set-final-version-output.outputs.version_web }}
    steps:
      - name: Validate version input format
        if: ${{ inputs.version_number_override != '' }}
        uses: bitwarden/gh-actions/version-check@main
        with:
          version: ${{ inputs.version_number_override }}

      - name: Generate GH App token
        uses: actions/create-github-app-token@30bf6253fa41bdc8d1501d202ad15287582246b4 # v2.0.3
        id: app-token
        with:
          app-id: ${{ secrets.BW_GHAPP_ID }}
          private-key: ${{ secrets.BW_GHAPP_KEY }}

      - name: Check out branch
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: main
          token: ${{ steps.app-token.outputs.token }}

      - name: Configure Git
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "Github Actions"

      ########################
      # VERSION BUMP SECTION #
      ########################

      ### Browser
      - name: Get current Browser version
        if: ${{ inputs.bump_browser == true }}
        id: current-browser-version
        run: |
          CURRENT_VERSION=$(cat package.json | jq -r '.version')
          echo "version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
        working-directory: apps/browser

      - name: Browser - Verify input version
        if: ${{ inputs.bump_browser == true && inputs.version_number_override != '' }}
        env:
          CURRENT_VERSION: ${{ steps.current-browser-version.outputs.version }}
          NEW_VERSION: ${{ inputs.version_number_override }}
        run: |
          # Error if version has not changed.
          if [[ "$NEW_VERSION" == "$CURRENT_VERSION" ]]; then
            echo "Version has not changed."
            exit 1
          fi

          # Check if version is newer.
          printf '%s\n' "${CURRENT_VERSION}" "${NEW_VERSION}" | sort -C -V
          if [ $? -eq 0 ]; then
            echo "Version check successful."
          else
            echo "Version check failed."
            exit 1
          fi
        working-directory: apps/browser

      - name: Calculate next Browser release version
        if: ${{ inputs.bump_browser == true && inputs.version_number_override == '' }}
        id: calculate-next-browser-version
        uses: bitwarden/gh-actions/version-next@main
        with:
          version: ${{ steps.current-browser-version.outputs.version }}

      - name: Bump Browser Version - Version Override
        if: ${{ inputs.bump_browser == true && inputs.version_number_override != '' }}
        id: bump-browser-version-override
        env:
          VERSION: ${{ inputs.version_number_override }}
        run: npm version --workspace=@bitwarden/browser $VERSION

      - name: Bump Browser Version - Automatic Calculation
        if: ${{ inputs.bump_browser == true && inputs.version_number_override == '' }}
        id: bump-browser-version-automatic
        env:
          VERSION: ${{ steps.calculate-next-browser-version.outputs.version }}
        run: npm version --workspace=@bitwarden/browser $VERSION

      - name: Bump Browser Version - Manifest - Version Override
        if: ${{ inputs.bump_browser == true && inputs.version_number_override != '' }}
        uses: bitwarden/gh-actions/version-bump@main
        with:
          file_path: "apps/browser/src/manifest.json"
          version: ${{ inputs.version_number_override }}

      - name: Bump Browser Version - Manifest - Automatic Calculation
        if: ${{ inputs.bump_browser == true && inputs.version_number_override == '' }}
        uses: bitwarden/gh-actions/version-bump@main
        with:
          file_path: "apps/browser/src/manifest.json"
          version: ${{ steps.calculate-next-browser-version.outputs.version }}

      - name: Bump Browser Version - Manifest v3 - Version Override
        if: ${{ inputs.bump_browser == true && inputs.version_number_override != '' }}
        uses: bitwarden/gh-actions/version-bump@main
        with:
          file_path: "apps/browser/src/manifest.v3.json"
          version: ${{ inputs.version_number_override }}

      - name: Bump Browser Version - Manifest v3 - Automatic Calculation
        if: ${{ inputs.bump_browser == true && inputs.version_number_override == '' }}
        uses: bitwarden/gh-actions/version-bump@main
        with:
          file_path: "apps/browser/src/manifest.v3.json"
          version: ${{ steps.calculate-next-browser-version.outputs.version }}

      - name: Run Prettier after Browser Version Bump
        if: ${{ inputs.bump_browser == true }}
        run: |
          npm install -g prettier
          prettier --write apps/browser/src/manifest.json
          prettier --write apps/browser/src/manifest.v3.json

      ### CLI
      - name: Get current CLI version
        if: ${{ inputs.bump_cli == true }}
        id: current-cli-version
        run: |
          CURRENT_VERSION=$(cat package.json | jq -r '.version')
          echo "version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
        working-directory: apps/cli

      - name: CLI - Verify input version
        if: ${{ inputs.bump_cli == true && inputs.version_number_override != '' }}
        env:
          CURRENT_VERSION: ${{ steps.current-cli-version.outputs.version }}
          NEW_VERSION: ${{ inputs.version_number_override }}
        run: |
          # Error if version has not changed.
          if [[ "$NEW_VERSION" == "$CURRENT_VERSION" ]]; then
            echo "Version has not changed."
            exit 1
          fi

          # Check if version is newer.
          printf '%s\n' "${CURRENT_VERSION}" "${NEW_VERSION}" | sort -C -V
          if [ $? -eq 0 ]; then
            echo "Version check successful."
          else
            echo "Version check failed."
            exit 1
          fi
        working-directory: apps/cli

      - name: Calculate next CLI release version
        if: ${{ inputs.bump_cli == true && inputs.version_number_override == '' }}
        id: calculate-next-cli-version
        uses: bitwarden/gh-actions/version-next@main
        with:
          version: ${{ steps.current-cli-version.outputs.version }}

      - name: Bump CLI Version - Version Override
        if: ${{ inputs.bump_cli == true && inputs.version_number_override != '' }}
        id: bump-cli-version-override
        env:
          VERSION: ${{ inputs.version_number_override }}
        run: npm version --workspace=@bitwarden/cli $VERSION

      - name: Bump CLI Version - Automatic Calculation
        if: ${{ inputs.bump_cli == true && inputs.version_number_override == '' }}
        id: bump-cli-version-automatic
        env:
          VERSION: ${{ steps.calculate-next-cli-version.outputs.version }}
        run: npm version --workspace=@bitwarden/cli $VERSION

      ### Desktop
      - name: Get current Desktop version
        if: ${{ inputs.bump_desktop == true }}
        id: current-desktop-version
        run: |
          CURRENT_VERSION=$(cat package.json | jq -r '.version')
          echo "version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
        working-directory: apps/desktop

      - name: Desktop - Verify input version
        if: ${{ inputs.bump_desktop == true && inputs.version_number_override != '' }}
        env:
          CURRENT_VERSION: ${{ steps.current-desktop-version.outputs.version }}
          NEW_VERSION: ${{ inputs.version_number_override }}
        run: |
          # Error if version has not changed.
          if [[ "$NEW_VERSION" == "$CURRENT_VERSION" ]]; then
            echo "Version has not changed."
            exit 1
          fi

          # Check if version is newer.
          printf '%s\n' "${CURRENT_VERSION}" "${NEW_VERSION}" | sort -C -V
          if [ $? -eq 0 ]; then
            echo "Version check successful."
          else
            echo "Version check failed."
            exit 1
          fi
        working-directory: apps/desktop

      - name: Calculate next Desktop release version
        if: ${{ inputs.bump_desktop == true && inputs.version_number_override == '' }}
        id: calculate-next-desktop-version
        uses: bitwarden/gh-actions/version-next@main
        with:
          version: ${{ steps.current-desktop-version.outputs.version }}

      - name: Bump Desktop Version - Root - Version Override
        if: ${{ inputs.bump_desktop == true && inputs.version_number_override != '' }}
        id: bump-desktop-version-override
        env:
          VERSION: ${{ inputs.version_number_override }}
        run: npm version --workspace=@bitwarden/desktop $VERSION

      - name: Bump Desktop Version - Root - Automatic Calculation
        if: ${{ inputs.bump_desktop == true && inputs.version_number_override == '' }}
        id: bump-desktop-version-automatic
        env:
          VERSION: ${{ steps.calculate-next-desktop-version.outputs.version }}
        run: npm version --workspace=@bitwarden/desktop $VERSION

      - name: Bump Desktop Version - App - Version Override
        if: ${{ inputs.bump_desktop == true && inputs.version_number_override != '' }}
        env:
          VERSION: ${{ inputs.version_number_override }}
        run: npm version $VERSION
        working-directory: "apps/desktop/src"

      - name: Bump Desktop Version - App - Automatic Calculation
        if: ${{ inputs.bump_desktop == true && inputs.version_number_override == '' }}
        env:
          VERSION: ${{ steps.calculate-next-desktop-version.outputs.version }}
        run: npm version $VERSION
        working-directory: "apps/desktop/src"

      ### Web
      - name: Get current Web version
        if: ${{ inputs.bump_web == true }}
        id: current-web-version
        run: |
          CURRENT_VERSION=$(cat package.json | jq -r '.version')
          echo "version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
        working-directory: apps/web

      - name: Web - Verify input version
        if: ${{ inputs.bump_web == true && inputs.version_number_override != '' }}
        env:
          CURRENT_VERSION: ${{ steps.current-web-version.outputs.version }}
          NEW_VERSION: ${{ inputs.version_number_override }}
        run: |
          # Error if version has not changed.
          if [[ "$NEW_VERSION" == "$CURRENT_VERSION" ]]; then
            echo "Version has not changed."
            exit 1
          fi

          # Check if version is newer.
          printf '%s\n' "${CURRENT_VERSION}" "${NEW_VERSION}" | sort -C -V
          if [ $? -eq 0 ]; then
            echo "Version check successful."
          else
            echo "Version check failed."
            exit 1
          fi
        working-directory: apps/web

      - name: Calculate next Web release version
        if: ${{ inputs.bump_web == true && inputs.version_number_override == '' }}
        id: calculate-next-web-version
        uses: bitwarden/gh-actions/version-next@main
        with:
          version: ${{ steps.current-web-version.outputs.version }}

      - name: Bump Web Version - Version Override
        if: ${{ inputs.bump_web == true && inputs.version_number_override != '' }}
        id: bump-web-version-override
        env:
          VERSION: ${{ inputs.version_number_override }}
        run: npm version --workspace=@bitwarden/web-vault $VERSION

      - name: Bump Web Version - Automatic Calculation
        if: ${{ inputs.bump_web == true && inputs.version_number_override == '' }}
        id: bump-web-version-automatic
        env:
          VERSION: ${{ steps.calculate-next-web-version.outputs.version }}
        run: npm version --workspace=@bitwarden/web-vault $VERSION

      ########################

      - name: Set final version output
        id: set-final-version-output
        env:
          VERSION: ${{ inputs.version_number_override }}
        run: |
          if [[ "${{ steps.bump-browser-version-override.outcome }}" = "success" ]]; then
            echo "version_browser=$VERSION" >> $GITHUB_OUTPUT
          elif [[ "${{ steps.bump-browser-version-automatic.outcome }}" = "success" ]]; then
            echo "version_browser=${{ steps.calculate-next-browser-version.outputs.version }}" >> $GITHUB_OUTPUT
          fi

          if [[ "${{ steps.bump-cli-version-override.outcome }}" = "success" ]]; then
            echo "version_cli=$VERSION" >> $GITHUB_OUTPUT
          elif [[ "${{ steps.bump-cli-version-automatic.outcome }}" = "success" ]]; then
            echo "version_cli=${{ steps.calculate-next-cli-version.outputs.version }}" >> $GITHUB_OUTPUT
          fi

          if [[ "${{ steps.bump-desktop-version-override.outcome }}" = "success" ]]; then
            echo "version_desktop=$VERSION" >> $GITHUB_OUTPUT
          elif [[ "${{ steps.bump-desktop-version-automatic.outcome }}" = "success" ]]; then
            echo "version_desktop=${{ steps.calculate-next-desktop-version.outputs.version }}" >> $GITHUB_OUTPUT
          fi

          if [[ "${{ steps.bump-web-version-override.outcome }}" = "success" ]]; then
            echo "version_web=$VERSION" >> $GITHUB_OUTPUT
          elif [[ "${{ steps.bump-web-version-automatic.outcome }}" = "success" ]]; then
            echo "version_web=${{ steps.calculate-next-web-version.outputs.version }}" >> $GITHUB_OUTPUT
          fi

      - name: Check if version changed
        id: version-changed
        run: |
          if [ -n "$(git status --porcelain)" ]; then
            echo "changes_to_commit=TRUE" >> $GITHUB_OUTPUT
          else
            echo "changes_to_commit=FALSE" >> $GITHUB_OUTPUT
            echo "No changes to commit!";
          fi

      - name: Commit files
        if: ${{ steps.version-changed.outputs.changes_to_commit == 'TRUE' }}
        run: git commit -m "Bumped client version(s)" -a

      - name: Push changes
        if: ${{ steps.version-changed.outputs.changes_to_commit == 'TRUE' }}
        run: git push
  cut_branch:
    name: Cut branch
    if: ${{ needs.setup.outputs.branch == 'rc' }}
    needs:
      - setup
      - bump_version
    runs-on: ubuntu-24.04
    steps:
      - name: Generate GH App token
        uses: actions/create-github-app-token@30bf6253fa41bdc8d1501d202ad15287582246b4 # v2.0.3
        id: app-token
        with:
          app-id: ${{ secrets.BW_GHAPP_ID }}
          private-key: ${{ secrets.BW_GHAPP_KEY }}

      - name: Check out target ref
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{ inputs.target_ref }}
          token: ${{ steps.app-token.outputs.token }}

      - name: Check if ${{ needs.setup.outputs.branch }} branch exists
        env:
          BRANCH_NAME: ${{ needs.setup.outputs.branch }}
        run: |
          if [[ $(git ls-remote --heads origin $BRANCH_NAME) ]]; then
            echo "$BRANCH_NAME already exists! Please delete $BRANCH_NAME before running again." >> $GITHUB_STEP_SUMMARY
            exit 1
          fi

      - name: Cut branch
        env:
          BRANCH_NAME: ${{ needs.setup.outputs.branch }}
        run: |
          git switch --quiet --create $BRANCH_NAME
          git push --quiet --set-upstream origin $BRANCH_NAME
@import "variables.scss";

small,
.small {
  font-size: $font-size-small;
}

.bg-primary {
  @include themify($themes) {
    background-color: themed("primaryColor") !important;
  }
}

.bg-success {
  @include themify($themes) {
    background-color: themed("successColor") !important;
  }
}

.bg-danger {
  @include themify($themes) {
    background-color: themed("dangerColor") !important;
  }
}

.bg-info {
  @include themify($themes) {
    background-color: themed("infoColor") !important;
  }
}

.bg-warning {
  @include themify($themes) {
    background-color: themed("warningColor") !important;
  }
}

.text-primary {
  @include themify($themes) {
    color: themed("primaryColor") !important;
  }
}

.text-success {
  @include themify($themes) {
    color: themed("successColor") !important;
  }
}

.text-muted {
  @include themify($themes) {
    color: themed("mutedColor") !important;
  }
}

.text-default {
  @include themify($themes) {
    color: themed("textColor") !important;
  }
}

.text-danger {
  @include themify($themes) {
    color: themed("dangerColor") !important;
  }
}

.text-info {
  @include themify($themes) {
    color: themed("infoColor") !important;
  }
}

.text-warning {
  @include themify($themes) {
    color: themed("warningColor") !important;
  }
}

.text-center {
  text-align: center;
}

.font-weight-semibold {
  font-weight: 600;
}

p.lead {
  font-size: $font-size-large;
  margin-bottom: 20px;
  font-weight: normal;
}

.flex-right {
  margin-left: auto;
}

.flex-bottom {
  margin-top: auto;
}

.no-margin {
  margin: 0 !important;
}

.display-block {
  display: block !important;
}

.monospaced {
  font-family: $font-family-monospace;
}

.show-whitespace {
  white-space: pre-wrap;
}

.img-responsive {
  display: block;
  max-width: 100%;
  height: auto;
}

.img-rounded {
  border-radius: $border-radius;
}

.select-index-top {
  position: relative;
  z-index: 100;
}

.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  border: 0 !important;
}

:not(:focus) > .exists-only-on-parent-focus {
  display: none;
}

.password-wrapper {
  overflow-wrap: break-word;
  white-space: pre-wrap;
  min-width: 0;
}

.password-number {
  @include themify($themes) {
    color: themed("passwordNumberColor");
  }
}

.password-special {
  @include themify($themes) {
    color: themed("passwordSpecialColor");
  }
}

.password-character {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  width: 30px;
  height: 36px;
  font-weight: 600;

  &:nth-child(odd) {
    @include themify($themes) {
      background-color: themed("backgroundColor");
    }
  }
}

.password-count {
  white-space: nowrap;
  font-size: 8px;

  @include themify($themes) {
    color: themed("passwordCountText") !important;
  }
}

#duo-frame {
  background: url("../images/loading.svg") 0 0 no-repeat;
  width: 100%;
  height: 470px;
  margin-bottom: -10px;

  iframe {
    width: 100%;
    height: 100%;
    border: none;
  }
}

#web-authn-frame {
  width: 100%;
  height: 40px;

  iframe {
    border: none;
    height: 100%;
    width: 100%;
  }
}

body.linux-webauthn {
  width: 485px !important;
  #web-authn-frame {
    iframe {
      width: 375px;
      margin: 0 55px;
    }
  }
}

app-root > #loading {
  display: flex;
  text-align: center;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  color: $text-muted;

  @include themify($themes) {
    color: themed("mutedColor");
  }
}

app-vault-icon,
.app-vault-icon {
  display: flex;
}

.logo-image {
  margin: 0 auto;
  width: 142px;
  height: 21px;
  background-size: 142px 21px;
  background-repeat: no-repeat;
  @include themify($themes) {
    background-image: url("../images/logo-" + themed("logoSuffix") + "@2x.png");
  }
  @media (min-width: 219px) {
    width: 189px;
    height: 28px;
    background-size: 189px 28px;
  }
  @media (min-width: 314px) {
    width: 284px;
    height: 43px;
    background-size: 284px 43px;
  }
}

[hidden] {
  display: none !important;
}

.draggable {
  cursor: move;
}

input[type="password"]::-ms-reveal {
  display: none;
}

.flex {
  display: flex;

  &.flex-grow {
    > * {
      flex: 1;
    }
  }
}

// Text selection styles
// Set explicit selection styles (assumes primary accent color has sufficient
// contrast against the background, so its inversion is also still readable)
// and suppress user selection for most elements (to make it more app-like)

:not(bit-form-field input)::selection {
  @include themify($themes) {
    color: themed("backgroundColor");
    background-color: themed("primaryAccentColor");
  }
}

h1,
h2,
h3,
label,
a,
button,
p,
img,
.box-header,
.box-footer,
.callout,
.row-label,
.modal-title,
.overlay-container {
  user-select: none;

  &.user-select {
    user-select: auto;
  }
}

/* tweak for inconsistent line heights in cipher view */
.box-footer button,
.box-footer a {
  line-height: 1;
}

// Workaround for slow performance on external monitors on Chrome + MacOS
// See: https://bugs.chromium.org/p/chromium/issues/detail?id=971701#c64
@keyframes redraw {
  0% {
    opacity: 0.99;
  }
  100% {
    opacity: 1;
  }
}
html.force_redraw {
  animation: redraw 1s linear infinite;
}

/* override for vault icon in browser (pre extension refresh) */
app-vault-icon:not(app-vault-list-items-container app-vault-icon) > div {
  display: flex;
  justify-content: center;
  align-items: center;
  float: left;
  height: 36px;
  width: 34px;
  margin-left: -5px;
}

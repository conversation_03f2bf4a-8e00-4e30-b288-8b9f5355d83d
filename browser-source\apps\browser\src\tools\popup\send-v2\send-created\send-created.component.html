<main class="tw-top-0">
  <popup-page>
    <popup-header
      slot="header"
      [pageTitle]="'createdSend' | i18n"
      showBackButton
      [backAction]="goToEditSend.bind(this)"
    >
      <ng-container slot="end">
        <app-pop-out></app-pop-out>
      </ng-container>
    </popup-header>

    <div
      class="tw-flex tw-bg-background-alt tw-flex-col tw-justify-center tw-items-center tw-gap-2 tw-h-full tw-px-5"
    >
      <bit-icon [icon]="sendCreatedIcon"></bit-icon>
      <h3 tabindex="0" appAutofocus class="tw-font-semibold">
        {{ "createdSendSuccessfully" | i18n }}
      </h3>
      <p class="tw-text-center">
        {{ formatExpirationDate() }}
      </p>
      <button bitButton type="button" buttonType="primary" (click)="copyLink()">
        <b>{{ "copyLink" | i18n }}</b>
      </button>
    </div>
    <popup-footer slot="footer">
      <button bitButton type="button" buttonType="primary" (click)="copyLink()">
        <b>{{ "copyLink" | i18n }}</b>
      </button>
      <button bitButton type="button" buttonType="secondary" (click)="goBack()">
        {{ "close" | i18n }}
      </button>
    </popup-footer>
  </popup-page>
</main>

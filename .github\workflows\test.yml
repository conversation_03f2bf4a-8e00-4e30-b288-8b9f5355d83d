name: Testing

on:
  workflow_dispatch:
  push:
    branches:
      - "main"
      - "rc"
      - "hotfix-rc-*"
  pull_request:
    types: [ opened, synchronize ]

permissions: {}

jobs:

  testing:
    name: Run tests
    runs-on: ubuntu-22.04
    permissions:
      checks: write
      contents: read
      pull-requests: write

    steps:
      - name: Check out repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Get Node Version
        id: retrieve-node-version
        run: |
          NODE_NVMRC=$(cat .nvmrc)
          NODE_VERSION=${NODE_NVMRC/v/''}
          echo "node_version=$NODE_VERSION" >> $GITHUB_OUTPUT

      - name: Set up Node
        uses: actions/setup-node@39370e3970a6d050c480ffad4ff0ed4d3fdee5af # v4.1.0
        with:
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'
          node-version: ${{ steps.retrieve-node-version.outputs.node_version }}

      - name: Print environment
        run: |
          node --version
          npm --version

      - name: Install Node dependencies
        run: npm ci

      # We use isolatedModules: true which disables typechecking in tests
      # Tests in apps/ are typechecked when their app is built, so we just do it here for libs/
      # See https://bitwarden.atlassian.net/browse/EC-497
      - name: Run typechecking
        run: npm run test:types

      - name: Run tests
        # maxWorkers is a workaround for a memory leak that crashes tests in CI:
        # https://github.com/facebook/jest/issues/9430#issuecomment-1149882002
        run: npm test -- --coverage --maxWorkers=3

      - name: Report test results
        uses: dorny/test-reporter@6e6a65b7a0bd2c9197df7d0ae36ac5cee784230c # v2.0.0
        if: ${{ github.event.pull_request.head.repo.full_name == github.repository && !cancelled() }}
        with:
          name: Test Results
          path: "junit.xml"
          reporter: jest-junit
          fail-on-error: true

      - name: Upload results to codecov.io
        uses: codecov/test-results-action@f2dba722c67b86c6caa034178c6e4d35335f6706 # v1.1.0

      - name: Upload test coverage
        uses: actions/upload-artifact@65c4c4a1ddee5b72f698fdd19549f0f0fb45cf08 # v4.6.0
        with:
          name: jest-coverage
          path: ./coverage/lcov.info

  rust:
    name: Run Rust tests on ${{ matrix.os }}
    runs-on: ${{ matrix.os || 'ubuntu-22.04' }}
    permissions:
      contents: read

    strategy:
      matrix:
        os:
          - ubuntu-22.04
          - macos-14
          - windows-2022

    steps:
      - name: Check Rust version
        run: rustup --version

      - name: Install gnome-keyring
        if: ${{ matrix.os=='ubuntu-22.04' }}
        run: |
          sudo apt-get update
          sudo apt-get install -y gnome-keyring dbus-x11

      - name: Check out repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Build
        working-directory: ./apps/desktop/desktop_native
        run: cargo build

      - name: Test Ubuntu
        if: ${{ matrix.os=='ubuntu-22.04' }}
        working-directory: ./apps/desktop/desktop_native
        run: |
          eval "$(dbus-launch --sh-syntax)"
          mkdir -p ~/.cache
          mkdir -p ~/.local/share/keyrings
          eval "$(printf '\n' | gnome-keyring-daemon --unlock)"
          eval "$(printf '\n' | /usr/bin/gnome-keyring-daemon --start)"
          cargo test -- --test-threads=1

      - name: Test macOS
        if: ${{ matrix.os=='macos-14' }}
        working-directory: ./apps/desktop/desktop_native
        run: cargo test -- --test-threads=1

      - name: Test Windows
        if: ${{ matrix.os=='windows-2022'}}
        working-directory: ./apps/desktop/desktop_native/core
        run: cargo test -- --test-threads=1

  rust-coverage:
    name: Rust Coverage
    runs-on: macos-14
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Install rust
        uses: dtolnay/rust-toolchain@b3b07ba8b418998c39fb20f53e8b695cdcc8de1b # stable
        with:
          toolchain: stable
          components: llvm-tools

      - name: Cache cargo registry
        uses: Swatinem/rust-cache@82a92a6e8fbeee089604da2575dc567ae9ddeaab # v2.7.5
        with:
          workspaces: "apps/desktop/desktop_native -> target"

      - name: Install cargo-llvm-cov
        run: cargo install cargo-llvm-cov --version 0.6.16

      - name: Generate coverage
        working-directory: ./apps/desktop/desktop_native
        run: cargo llvm-cov --all-features --lcov --output-path lcov.info --workspace --no-cfg-coverage

      - name: Upload test coverage
        uses: actions/upload-artifact@65c4c4a1ddee5b72f698fdd19549f0f0fb45cf08 # v4.6.0
        with:
          name: rust-coverage
          path: ./apps/desktop/desktop_native/lcov.info

  upload-codecov:
    name: Upload to Codecov
    runs-on: ubuntu-22.04
    needs:
      - testing
      - rust-coverage
    steps:
      - name: Check out repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Download jest coverage
        uses: actions/download-artifact@fa0a91b85d4f404e444e00e005971372dc801d16 # v4.1.8
        with:
          name: jest-coverage
          path: ./

      - name: Download rust coverage
        uses: actions/download-artifact@fa0a91b85d4f404e444e00e005971372dc801d16 # v4.1.8
        with:
          name: rust-coverage
          path: ./apps/desktop/desktop_native

      - name: Upload coverage to codecov.io
        uses: codecov/codecov-action@ad3126e916f78f00edff4ed0317cf185271ccc2d # v5.4.2
        with:
          files: |
            ./lcov.info
            ./apps/desktop/desktop_native/lcov.info

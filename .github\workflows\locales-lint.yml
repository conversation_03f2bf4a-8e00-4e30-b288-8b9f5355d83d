name: Locales lint for Crow<PERSON>

on:
  pull_request:
    branches-ignore:
      - 'l10n_master'
      - 'cf-pages'
    paths:
      - '**/messages.json'

permissions:
  contents: read

jobs:
  lint:
    name: <PERSON>t
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Checkout base branch repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{ github.event.pull_request.base.sha }}
          path: base
      - name: Install dependencies
        run: npm ci
      - name: Compare
        run: |
          npm run test:locales
          if [ $? -eq 0 ]; then
            echo "Lint check successful."
          else
            echo "Lint check failed."
            exit 1
          fi

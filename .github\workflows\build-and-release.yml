name: Build and Release Warden's Key

on:
  push:
    branches:
      - 'main'
    paths:
      - 'browser-source/**'
      - '.github/workflows/build-and-release.yml'
    tags:
      - 'v*'
  pull_request:
    types: [opened, synchronize]
    paths:
      - 'browser-source/**'
      - '.github/workflows/build-and-release.yml'
  workflow_dispatch:
    inputs:
      create_release:
        description: 'Create GitHub Release'
        required: false
        default: false
        type: boolean

defaults:
  run:
    shell: bash

permissions:
  contents: write

jobs:
  setup:
    name: Setup
    runs-on: ubuntu-22.04
    outputs:
      version: ${{ steps.version.outputs.version }}
      build_number: ${{ steps.version.outputs.build_number }}
      node_version: ${{ steps.node-version.outputs.node_version }}
      should_release: ${{ steps.release-check.outputs.should_release }}
    steps:
      - name: Check out repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Get version info
        id: version
        run: |
          VERSION=$(jq -r '.version' browser-source/apps/browser/src/manifest.json)
          BUILD_NUMBER=${GITHUB_SHA:0:7}
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "build_number=$BUILD_NUMBER" >> $GITHUB_OUTPUT

      - name: Get Node Version
        id: node-version
        run: |
          if [ -f "browser-source/.nvmrc" ]; then
            NODE_NVMRC=$(cat browser-source/.nvmrc)
            NODE_VERSION=${NODE_NVMRC/v/''}
            echo "Found .nvmrc with version: $NODE_VERSION"
          else
            NODE_VERSION="20"
            echo "No .nvmrc found, using default version: $NODE_VERSION"
          fi
          echo "node_version=$NODE_VERSION" >> $GITHUB_OUTPUT

      - name: Check if should release
        id: release-check
        run: |
          echo "=== Release Check Debug ==="
          echo "Event name: ${{ github.event_name }}"
          echo "Ref: ${{ github.ref }}"
          echo "Create release input: ${{ inputs.create_release }}"

          should_release=false
          if [[ "${{ github.event_name }}" == "push" && "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "✅ Condition met: Push to main branch"
            should_release=true
          elif [[ "${{ github.event_name }}" == "push" && "${{ github.ref }}" == refs/tags/v* ]]; then
            echo "✅ Condition met: Push to version tag"
            should_release=true
          elif [[ "${{ github.event_name }}" == "workflow_dispatch" && "${{ inputs.create_release }}" == "true" ]]; then
            echo "✅ Condition met: Manual dispatch with create_release=true"
            should_release=true
          else
            echo "❌ No release conditions met"
          fi

          echo "Final decision: should_release=$should_release"
          echo "should_release=$should_release" >> $GITHUB_OUTPUT

  locales-test:
    name: Locales Test
    runs-on: ubuntu-22.04
    needs: setup
    steps:
      - name: Check out repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Testing locales - extName length
        run: |
         found_error=false

         echo "Locales Test"
         echo "============"
         echo "extName string must be 40 characters or less"
         echo
         for locale in $(ls browser-source/apps/browser/src/_locales/); do
           string_length=$(jq '.extName.message | length' browser-source/apps/browser/src/_locales/$locale/messages.json)
           if [[ $string_length -gt 40 ]]; then
             echo "$locale: $string_length"
             found_error=true
           fi
         done

         if $found_error; then
           echo
           echo "Please fix 'extName' for the locales listed above."
           exit 1
         else
           echo "Test passed!"
         fi

  build-source:
    name: Build browser source
    runs-on: ubuntu-22.04
    needs: [setup, locales-test]
    env:
      _BUILD_NUMBER: ${{ needs.setup.outputs.build_number }}
      _NODE_VERSION: ${{ needs.setup.outputs.node_version }}
    steps:
      - name: Check out repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up Node
        uses: actions/setup-node@39370e3970a6d050c480ffad4ff0ed4d3fdee5af # v4.1.0
        with:
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'
          node-version: ${{ env._NODE_VERSION }}

      - name: Print environment
        run: |
          node --version
          npm --version

      - name: Build sources for reviewers
        run: |
          # Create a copy of browser-source for packaging
          cp -r browser-source browser-source-package
          
          # Remove .git directory if it exists
          rm -rf browser-source-package/.git
          
          # Create zip package
          zip -r browser-source.zip browser-source-package

      - name: Upload browser source
        uses: actions/upload-artifact@65c4c4a1ddee5b72f698fdd19549f0f0fb45cf08 # v4.6.0
        with:
          name: browser-source-${{ env._BUILD_NUMBER }}.zip
          path: browser-source.zip
          if-no-files-found: error

  build:
    name: Build Extensions
    runs-on: ubuntu-22.04
    needs: [setup, locales-test, build-source]
    env:
      _BUILD_NUMBER: ${{ needs.setup.outputs.build_number }}
      _NODE_VERSION: ${{ needs.setup.outputs.node_version }}
    strategy:
      matrix:
        include:
          - name: "chrome"
            npm_command: "dist:chrome"
            archive_name: "dist-chrome.zip"
            artifact_name: "dist-chrome-MV3"
          - name: "edge"
            npm_command: "dist:edge"
            archive_name: "dist-edge.zip"
            artifact_name: "dist-edge-MV3"
          - name: "firefox"
            npm_command: "dist:firefox"
            archive_name: "dist-firefox.zip"
            artifact_name: "dist-firefox"
          - name: "opera-mv3"
            npm_command: "dist:opera:mv3"
            archive_name: "dist-opera.zip"
            artifact_name: "dist-opera-MV3"
    steps:
      - name: Check out repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set up Node
        uses: actions/setup-node@39370e3970a6d050c480ffad4ff0ed4d3fdee5af # v4.1.0
        with:
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'
          node-version: ${{ env._NODE_VERSION }}

      - name: Print environment
        run: |
          node --version
          npm --version

      - name: Download browser source
        uses: actions/download-artifact@fa0a91b85d4f404e444e00e005971372dc801d16 # v4.1.8
        with:
          name: browser-source-${{ env._BUILD_NUMBER }}.zip

      - name: Unzip browser source artifact
        run: |
          unzip browser-source.zip
          rm browser-source.zip

      - name: NPM setup (root)
        run: npm ci
        working-directory: browser-source-package/

      - name: Fix script permissions and verify structure
        run: |
          echo "=== Checking directory structure ==="
          ls -la browser-source-package/apps/browser/scripts/

          echo "=== Setting script permissions ==="
          chmod +x browser-source-package/apps/browser/scripts/compress.sh
          chmod +x browser-source-package/apps/browser/scripts/package-safari.ps1

          echo "=== Verifying permissions ==="
          ls -la browser-source-package/apps/browser/scripts/

          echo "=== Checking compress.sh content ==="
          head -5 browser-source-package/apps/browser/scripts/compress.sh

      - name: Check source file size
        if: ${{ startsWith(matrix.name, 'firefox') }}
        run: |
          # Declare variable as indexed array
          declare -a FILES

          # Search for source files that are greater than 4M
          TARGET_DIR='./browser-source-package/apps/browser'
          while IFS=' ' read -r RESULT; do
              FILES+=("$RESULT")
          done < <(find $TARGET_DIR -size +4M)

          # Validate results and provide messaging
          if [[ ${#FILES[@]} -ne 0 ]]; then
              echo "File(s) exceeds size limit: 4MB"
              for FILE in ${FILES[@]}; do
                  echo "- $(du --si $FILE)"
              done
              echo "ERROR Firefox rejects extension uploads that contain files larger than 4MB"
              # Invoke failure
              exit 1
          fi

      - name: Build extension
        run: |
          echo "=== Building ${{ matrix.name }} extension ==="
          echo "Running command: npm run ${{ matrix.npm_command }}"
          npm run ${{ matrix.npm_command }}

          echo "=== Checking build output ==="
          ls -la dist/ || echo "No dist directory found"
          ls -la build/ || echo "No build directory found"
        working-directory: browser-source-package/apps/browser

      - name: Upload extension artifact
        uses: actions/upload-artifact@65c4c4a1ddee5b72f698fdd19549f0f0fb45cf08 # v4.6.0
        with:
          name: ${{ matrix.artifact_name }}-${{ env._BUILD_NUMBER }}.zip
          path: browser-source-package/apps/browser/dist/${{ matrix.archive_name }}
          if-no-files-found: error

  release:
    name: Create GitHub Release
    runs-on: ubuntu-22.04
    needs: [setup, build]
    if: ${{ needs.setup.outputs.should_release == 'true' }}
    env:
      _VERSION: ${{ needs.setup.outputs.version }}
      _BUILD_NUMBER: ${{ needs.setup.outputs.build_number }}
    steps:
      - name: Debug release info
        run: |
          echo "=== Release Debug Info ==="
          echo "Should release: ${{ needs.setup.outputs.should_release }}"
          echo "Version: ${{ needs.setup.outputs.version }}"
          echo "Build number: ${{ needs.setup.outputs.build_number }}"
          echo "Event: ${{ github.event_name }}"
          echo "Ref: ${{ github.ref }}"

      - name: Check out repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Download all build artifacts
        uses: actions/download-artifact@fa0a91b85d4f404e444e00e005971372dc801d16 # v4.1.8
        with:
          pattern: '*-${{ env._BUILD_NUMBER }}.zip'
          merge-multiple: true

      - name: Debug downloaded artifacts
        run: |
          echo "=== Downloaded artifacts ==="
          ls -la *.zip || echo "No zip files found"
          echo "=== All files ==="
          ls -la

      - name: Rename build artifacts
        run: |
          echo "=== Renaming artifacts ==="

          # Rename browser source if it exists
          if [[ -f "browser-source.zip" ]]; then
            mv browser-source.zip wardens-key-browser-source-${{ env._VERSION }}.zip
            echo "✅ Renamed browser-source.zip"
          else
            echo "❌ browser-source.zip not found"
          fi

          # Find and rename extension artifacts
          echo "Looking for extension artifacts..."
          for file in *-${{ env._BUILD_NUMBER }}.zip; do
            if [[ -f "$file" && "$file" != "browser-source-${{ env._BUILD_NUMBER }}.zip" ]]; then
              echo "Processing: $file"

              # Extract browser name more carefully
              if [[ "$file" =~ dist-chrome-MV3-.* ]]; then
                browser_name="chrome"
              elif [[ "$file" =~ dist-firefox-.* ]]; then
                browser_name="firefox"
              elif [[ "$file" =~ dist-edge-MV3-.* ]]; then
                browser_name="edge"
              elif [[ "$file" =~ dist-opera-MV3-.* ]]; then
                browser_name="opera"
              else
                # Fallback: extract from filename
                browser_name=$(echo "$file" | sed 's/dist-\(.*\)-MV3-.*/\1/' | sed 's/dist-\(.*\)-.*/\1/')
              fi

              new_name="wardens-key-${browser_name}-${{ env._VERSION }}.zip"
              mv "$file" "$new_name"
              echo "✅ Renamed $file to $new_name"
            fi
          done

          echo "=== Final artifact list ==="
          ls -la wardens-key-*.zip || echo "No wardens-key artifacts found"

      - name: Generate release notes
        id: release-notes
        run: |
          cat > release_notes.md << 'EOF'
          # Warden's Key v${{ env._VERSION }}

          🔓 **TOTP 功能完全解锁版本**

          ## 🎯 主要特性
          - ✅ **完全解锁 TOTP 功能**：免费用户可使用所有 TOTP 相关功能
          - ✅ **多浏览器支持**：Chrome、Firefox、Edge、Opera
          - ✅ **保持原有功能**：不影响其他 Bitwarden 功能的正常使用
          - ✅ **UI 完整性**：移除所有付费限制提示
          - ✅ **自动填充支持**：TOTP 代码可用于自动填充

          ## 📦 下载说明
          请根据您的浏览器选择对应的扩展包：

          - **Chrome**: `wardens-key-chrome-${{ env._VERSION }}.zip`
          - **Firefox**: `wardens-key-firefox-${{ env._VERSION }}.zip`
          - **Edge**: `wardens-key-edge-${{ env._VERSION }}.zip`
          - **Opera**: `wardens-key-opera-${{ env._VERSION }}.zip`
          - **源码包**: `wardens-key-browser-source-${{ env._VERSION }}.zip`

          ## 📋 安装指南
          详细安装说明请参考：[TOTP_UNLOCK_INSTALLATION_GUIDE.md](https://github.com/${{ github.repository }}/blob/main/TOTP_UNLOCK_INSTALLATION_GUIDE.md)

          ## ⚠️ 免责声明
          本修改版本仅供技术学习和研究使用。请尊重软件版权，支持官方正版软件的开发。
          EOF

          echo "Generated release notes"

      - name: Create release
        uses: ncipollo/release-action@cdcc88a9acf3ca41c16c37bb7d21b9ad48560d87 # v1.15.0
        with:
          artifacts: 'wardens-key-*.zip'
          commit: ${{ github.sha }}
          tag: "v${{ env._VERSION }}"
          name: "Warden's Key v${{ env._VERSION }}"
          bodyFile: "release_notes.md"
          token: ${{ secrets.GITHUB_TOKEN }}
          draft: false
          prerelease: false

  check-failures:
    name: Check for failures
    if: always()
    runs-on: ubuntu-22.04
    needs: [setup, locales-test, build-source, build, release]
    steps:
      - name: Check if any job failed
        if: |
          github.event_name != 'pull_request'
          && contains(needs.*.result, 'failure')
        run: |
          echo "One or more jobs failed"
          exit 1

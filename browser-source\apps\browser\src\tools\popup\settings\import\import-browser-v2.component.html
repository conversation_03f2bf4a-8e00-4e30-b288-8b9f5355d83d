<popup-page>
  <popup-header slot="header" [pageTitle]="'importData' | i18n" showBackButton>
    <ng-container slot="end">
      <app-pop-out></app-pop-out>
    </ng-container>
  </popup-header>

  <tools-import
    (formDisabled)="this.disabled = $event"
    (formLoading)="this.loading = $event"
    (onSuccessfulImport)="this.onSuccessfulImport($event)"
  >
  </tools-import>

  <popup-footer slot="footer">
    <button
      [disabled]="disabled"
      [loading]="loading"
      form="import_form_importForm"
      bitButton
      type="submit"
      bitFormButton
      buttonType="primary"
    >
      {{ "importData" | i18n }}
    </button>
  </popup-footer>
</popup-page>

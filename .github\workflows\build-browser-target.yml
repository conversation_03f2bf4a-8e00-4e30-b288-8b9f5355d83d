# This workflow is intended to be run when we need to build the client and produce artifacts that require secrets
# when the PR source branch does not have access to secrets (e.g. a fork).
# This workflow will run in the context of the target of the PR and have access to secrets.
# This should only be done after reviewing the PR to ensure that no malicious code has been introduced,
# as it could allow the code on the forked branch to have access to workflow secrets.

name: Build Warden's Key on PR Target

on:
  pull_request_target:
    types: [opened, synchronize, reopened]
    branches:
      - main
    paths:
      - 'browser-source/**'
      - '.github/workflows/**'
      - '!*.md'
      - '!*.txt'
  workflow_call:
    inputs: {}

defaults:
  run:
    shell: bash

jobs:
  check-run:
    name: Check PR run
    uses: bitwarden/gh-actions/.github/workflows/check-run.yml@main

  run-workflow:
    name: Build Warden's Key
    needs: check-run
    if: ${{ github.event.pull_request.head.repo.full_name != github.repository }}
    uses: ./.github/workflows/build-browser.yml
    secrets: inherit


name: Auto Update Branch

on:
  push:
    branches:
      - 'main'
      - 'rc'
    paths:
      - 'apps/web/**'
      - 'libs/**'
      - '*'
      - '!*.md'
      - '!*.txt'
      - '.github/workflows/build-web.yml'
  workflow_dispatch:
    inputs: {}

jobs:
  update:
    name: Update Branch
    runs-on: ubuntu-22.04
    env:
      _BOT_EMAIL: <EMAIL>
      _BOT_NAME: bitwarden-devops-bot
    permissions:
      contents: write
    steps:
      - name: Setup
        id: setup
        run: echo "branch=${GITHUB_REF#refs/heads/}" >> $GITHUB_OUTPUT

      - name: Checkout repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: 'eu-web-${{ steps.setup.outputs.branch }}'
          fetch-depth: 0

      - name: Merge ${{ steps.setup.outputs.branch }}
        run: |
          git config --local user.email "${{ env._BOT_EMAIL }}"
          git config --local user.name "${{ env._BOT_NAME }}"
          git merge origin/${{ steps.setup.outputs.branch }}
          git push

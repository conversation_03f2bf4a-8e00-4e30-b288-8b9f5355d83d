{"appName": {"message": "Bitwarden"}, "appLogoLabel": {"message": "Bitwarden logotips"}, "extName": {"message": "Bitwarden paroļ<PERSON> p<PERSON>ldnieks", "description": "Extension name, MUST be less than 40 characters (Safari restriction)"}, "extDesc": {"message": "Bitwarden viegli aizsargā visas paroles, paro<PERSON>u atslēgas un jutīgu informāciju mājā<PERSON>, da<PERSON><PERSON> vai ceļā", "description": "Extension description, MUST be less than 112 characters (Safari restriction)"}, "loginOrCreateNewAccount": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vai j<PERSON>iz<PERSON>ido jauns kont<PERSON>, lai pie<PERSON><PERSON><PERSON><PERSON> d<PERSON> gla<PERSON>ta<PERSON>."}, "inviteAccepted": {"message": "Uzaicinājums apstiprināts"}, "createAccount": {"message": "Izveidot kontu"}, "newToBitwarden": {"message": "Bitwarden iepriekš nav izmantots?"}, "logInWithPasskey": {"message": "Pieteikties ar piekļuves atslēgu"}, "useSingleSignOn": {"message": "Izmantot vienoto piete<PERSON>"}, "welcomeBack": {"message": "Laipni lūdzam atpakaļ"}, "setAStrongPassword": {"message": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>"}, "finishCreatingYourAccountBySettingAPassword": {"message": "Sava konta izveido<PERSON>na j<PERSON>pabeidz ar paroles iesta<PERSON>"}, "enterpriseSingleSignOn": {"message": "Uzņēmum<PERSON> vienotā piete<PERSON>"}, "cancel": {"message": "Atcelt"}, "close": {"message": "Aizvērt"}, "submit": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "emailAddress": {"message": "E-pasta adrese"}, "masterPass": {"message": "Galvenā parole"}, "masterPassDesc": {"message": "Galvenā parole ir parole, kas tie<PERSON>, lai pie<PERSON><PERSON>. <PERSON><PERSON> <PERSON><PERSON><PERSON>, ka tā net<PERSON>, jo tād<PERSON> gadīju<PERSON> to nav iespējams atgūt."}, "masterPassHintDesc": {"message": "Galvenās paroles norāde var palīdz<PERSON>t atcerēties paroli, ja tā ir a<PERSON>."}, "masterPassHintText": {"message": "Ja tiks a<PERSON>, tās norādi var nosūtīt uz e-pasta adresi. $CURRENT$/$MAXIMUM$ lielākais pieļauja<PERSON> r<PERSON>tz<PERSON> skaits.", "placeholders": {"current": {"content": "$1", "example": "0"}, "maximum": {"content": "$2", "example": "50"}}}, "reTypeMasterPass": {"message": "Atkārtoti ievadīt galveno paroli "}, "masterPassHint": {"message": "Galvenās paroles nor<PERSON> (nav nepieciešama)"}, "passwordStrengthScore": {"message": "Paroles stipruma novērtējums $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "joinOrganization": {"message": "Pievienoties apvienībai"}, "joinOrganizationName": {"message": "Pievienoties $ORGANIZATIONNAME$", "placeholders": {"organizationName": {"content": "$1", "example": "My Org Name"}}}, "finishJoiningThisOrganizationBySettingAMasterPassword": {"message": "<PERSON><PERSON><PERSON> a<PERSON>vienībai ar galvenās paroles i<PERSON>."}, "tab": {"message": "Cilne"}, "vault": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "myVault": {"message": "<PERSON><PERSON>"}, "allVaults": {"message": "Visa<PERSON> glab<PERSON>"}, "tools": {"message": "<PERSON><PERSON><PERSON>"}, "settings": {"message": "Iestatījumi"}, "currentTab": {"message": "Pašreizējā cilne"}, "copyPassword": {"message": "Ievietot paroli starpliktuvē"}, "copyPassphrase": {"message": "Ievietot paroles vārdkopu starpliktuvē"}, "copyNote": {"message": "Ievietot <PERSON>"}, "copyUri": {"message": "Ievietot viet<PERSON><PERSON><PERSON> starpliktuvē"}, "copyUsername": {"message": "Ievietot lietotājvārdu starpliktuvē"}, "copyNumber": {"message": "Ievietot numuru starpliktuvē"}, "copySecurityCode": {"message": "Ievietot drošības kodu starpliktuvē"}, "copyName": {"message": "Ievietot no<PERSON><PERSON><PERSON> starpliktuvē"}, "copyCompany": {"message": "Ievietot uzņēmumu starpliktuvē"}, "copySSN": {"message": "Ievietot so<PERSON><PERSON><PERSON><PERSON><PERSON> numuru starpliktuvē"}, "copyPassportNumber": {"message": "Ievietot pases numuru starpliktuvē"}, "copyLicenseNumber": {"message": "Ievietot licences numuru starpliktuvē"}, "copyPrivateKey": {"message": "Ievietot starpliktuvē privāto atslēgu"}, "copyPublicKey": {"message": "Ievietot starpliktuvē publisko atslēgu"}, "copyFingerprint": {"message": "Ievietot starpliktuvē pirkstu nospiedumu"}, "copyCustomField": {"message": "Ievietot $FIELD$ starpliktuvē", "placeholders": {"field": {"content": "$1", "example": "Custom field label"}}}, "copyWebsite": {"message": "Ievietot tīmekļvietni starpliktuvē"}, "copyNotes": {"message": "Ievietot piezī<PERSON>pliktuv<PERSON>"}, "copy": {"message": "Ievietot starpliktuvē", "description": "Copy to clipboard"}, "fill": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "This string is used on the vault page to indicate autofilling. Horizontal space is limited in the interface here so try and keep translations as concise as possible."}, "autoFill": {"message": "Automātiskā aiz<PERSON>lde"}, "autoFillLogin": {"message": "Automātiski aizpild<PERSON>"}, "autoFillCard": {"message": "Automātis<PERSON> a<PERSON> karti"}, "autoFillIdentity": {"message": "Automātiski aizpildīt identitāti"}, "fillVerificationCode": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON> kodu"}, "fillVerificationCodeAria": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON> kodu", "description": "Aria label for the heading displayed the inline menu for totp code autofill"}, "generatePasswordCopied": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> (tiks ievietota starpliktuvē)"}, "copyElementIdentifier": {"message": "<PERSON><PERSON><PERSON><PERSON> lauka nosaukumu"}, "noMatchingLogins": {"message": "Nav at<PERSON> piete<PERSON> vienumu"}, "noCards": {"message": "Nav kar<PERSON>u"}, "noIdentities": {"message": "Nav identitāšu"}, "addLoginMenu": {"message": "<PERSON><PERSON><PERSON> v<PERSON>u"}, "addCardMenu": {"message": "<PERSON><PERSON><PERSON> karti"}, "addIdentityMenu": {"message": "Pievienot identitāti"}, "unlockVaultMenu": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "loginToVaultMenu": {"message": "Pieteikties savā glabātavā"}, "autoFillInfo": {"message": "Nav piete<PERSON><PERSON><PERSON><PERSON><PERSON>, kurus automātiski ievadīt pašreizējā pārlūka cilnē."}, "addLogin": {"message": "<PERSON><PERSON><PERSON> v<PERSON>u"}, "addItem": {"message": "<PERSON><PERSON><PERSON> vien<PERSON>"}, "accountEmail": {"message": "Konta e-pasta adrese"}, "requestHint": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "requestPasswordHint": {"message": "Pie<PERSON><PERSON><PERSON><PERSON> paroles nor<PERSON>"}, "enterYourAccountEmailAddressAndYourPasswordHintWillBeSentToYou": {"message": "<PERSON>āievada sava konta e-pasta adrese, un paroles norāde tiks nos<PERSON>ta"}, "getMasterPasswordHint": {"message": "<PERSON><PERSON><PERSON>t galvenās paroles nor<PERSON>di"}, "continue": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sendVerificationCode": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> aplie<PERSON>ājuma kodu e-pastā"}, "sendCode": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kodu"}, "codeSent": {"message": "Kods <PERSON>ūtī<PERSON>"}, "verificationCode": {"message": "A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kods"}, "confirmIdentity": {"message": "<PERSON><PERSON><PERSON><PERSON>cina sava identitāte, lai tur<PERSON><PERSON>."}, "changeMasterPassword": {"message": "<PERSON><PERSON><PERSON> galveno paroli"}, "continueToWebApp": {"message": "<PERSON><PERSON>riet uz tīmekļa lietotni?"}, "continueToWebAppDesc": {"message": "Vairāk sava Bitwarden konta iespēju var izpētīt tīmekļa vietnē."}, "continueToHelpCenter": {"message": "Pāriet uz palīdzības centru?"}, "continueToHelpCenterDesc": {"message": "<PERSON><PERSON><PERSON><PERSON> par to, k<PERSON> <PERSON><PERSON><PERSON>, var uzzin<PERSON>t palīd<PERSON> centrā."}, "continueToBrowserExtensionStore": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> pārlūka paplašinājumu veikal<PERSON>?"}, "continueToBrowserExtensionStoreDesc": {"message": "Var palīdz<PERSON>t citiem <PERSON>, vai Bitwarden viņiem der. To var izdarīt pārlūka lietotņu veikalā, atst<PERSON>jot vērtējumu."}, "changeMasterPasswordOnWebConfirmation": {"message": "Savu galveno paroli var mainīt Bitwarden tīmekļa lietotnē."}, "fingerprintPhrase": {"message": "Atpazī<PERSON><PERSON> vārdkopa", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "yourAccountsFingerprint": {"message": "Konta atpazīšanas vārdkopa", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "twoStepLogin": {"message": "Divpakāpju <PERSON>ā<PERSON>"}, "logOut": {"message": "Atteikties"}, "aboutBitwarden": {"message": "<PERSON><PERSON>"}, "about": {"message": "Par"}, "moreFromBitwarden": {"message": "Vairāk no Bitwarden"}, "continueToBitwardenDotCom": {"message": "Pāriet uz bitwarden.com?"}, "bitwardenForBusiness": {"message": "Bitwarden uzņēmējdarbībai"}, "bitwardenAuthenticator": {"message": "Bitwarden autentificētājs"}, "continueToAuthenticatorPageDesc": {"message": "Bitwarden autentificētājs ļauj glabāt autentificētāja atslēgas un izveidot TOTP kodus divpakāpju apliecināšanas plūsmām. Vairāk var uzzināt tīmekļvietnē bitwarden.com"}, "bitwardenSecretsManager": {"message": "Bitwarden noslēpumu pārvaldnieks"}, "continueToSecretsManagerPageDesc": {"message": "<PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON>tāju noslēpumu uz<PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON>šana un kopīgošana ar Bitwarden noslēpumu pārvaldnieku. Vairāk var uzzināt tīmekļvietnē bitwarden.com."}, "passwordlessDotDev": {"message": "Passwordless.dev"}, "continueToPasswordlessDotDevPageDesc": {"message": "Plūdenu un drošu piete<PERSON><PERSON>, kas ir brīvas no ierastajām parolēm, iz<PERSON><PERSON>šana ar Passwordless.dev. <PERSON><PERSON><PERSON> var uzzināt tīmekļvietnē bitwarden.com."}, "freeBitwardenFamilies": {"message": "Bezmaksas Bitwarden ģimenēm"}, "freeBitwardenFamiliesPageDesc": {"message": "Tev ir tiesības uz bezmaksas Bitwarden ģimenēm. Šo piedāvājumu šodien var pieņemt tīmekļa lietotnē."}, "version": {"message": "Laidiens"}, "save": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "move": {"message": "Pārvietot"}, "addFolder": {"message": "<PERSON><PERSON><PERSON>i"}, "name": {"message": "Nosa<PERSON>ms"}, "editFolder": {"message": "<PERSON><PERSON> mapi"}, "editFolderWithName": {"message": "Labot mapi: $FOLDERNAME$", "placeholders": {"foldername": {"content": "$1", "example": "Social"}}}, "newFolder": {"message": "Jauna mape"}, "folderName": {"message": "<PERSON><PERSON>"}, "folderHintText": {"message": "Apakšma<PERSON> var <PERSON>, ja pievieno iekļau<PERSON><PERSON> mapes nosaukumu, aiz kura ir \"/\". Piemēram: Tīklošanās/Forumi"}, "noFoldersAdded": {"message": "Nav pievienota neviena mape"}, "createFoldersToOrganize": {"message": "Mapes ir i<PERSON><PERSON><PERSON><PERSON>, lai sakārtotu savas glab<PERSON>tavas vienumus"}, "deleteFolderPermanently": {"message": "Vai tiešām neatgriezeniski izdzēst šo mapi?"}, "deleteFolder": {"message": "Dzēst mapi"}, "folders": {"message": "Mapes"}, "noFolders": {"message": "Nav parād<PERSON><PERSON> mapju."}, "helpFeedback": {"message": "Palīdzība un atsauksmes"}, "helpCenter": {"message": "Bitwarden palīdzības centrs"}, "communityForums": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>t Bitwarden kopienas forumus"}, "contactSupport": {"message": "Sazināties ar Bitwarden atbalstu"}, "sync": {"message": "Sinhronizēt"}, "syncVaultNow": {"message": "Sinhroniz<PERSON><PERSON>"}, "lastSync": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:"}, "passGen": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "generator": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Short for 'credential generator'."}, "passGenInfo": {"message": "Automātiski ve<PERSON>, neatk<PERSON>rtojamas paroles visiem pieteik<PERSON>ā<PERSON> vienumiem."}, "bitWebVaultApp": {"message": "Bitwarden tīmekļa lieto<PERSON>ne"}, "importItems": {"message": "<PERSON><PERSON><PERSON><PERSON> vienumus"}, "select": {"message": "Izvēlēties"}, "generatePassword": {"message": "Veidot paroli"}, "generatePassphrase": {"message": "Izveidot paroles vārdkopu"}, "passwordGenerated": {"message": "Parole izveidota"}, "passphraseGenerated": {"message": "Paroles vārdkopa izveidota"}, "usernameGenerated": {"message": "Lietotājvārds izveidots"}, "emailGenerated": {"message": "E-pasta adrese izveidota"}, "regeneratePassword": {"message": "Pārizveidot paroli"}, "options": {"message": "<PERSON>es<PERSON>ējas"}, "length": {"message": "Garums"}, "include": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Card header for password generator include block"}, "uppercaseDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> burtus", "description": "Tooltip for the password generator uppercase character checkbox"}, "uppercaseLabel": {"message": "A-Z", "description": "Label for the password generator uppercase character checkbox"}, "lowercaseDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON> burtus", "description": "Full description for the password generator lowercase character checkbox"}, "lowercaseLabel": {"message": "a-z", "description": "Label for the password generator lowercase character checkbox"}, "numbersDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> ciparu<PERSON>", "description": "Full description for the password generator numbers checkbox"}, "numbersLabel": {"message": "0-9", "description": "Label for the password generator numbers checkbox"}, "specialCharactersDescription": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Full description for the password generator special characters checkbox"}, "numWords": {"message": "<PERSON><PERSON><PERSON><PERSON> skaits"}, "wordSeparator": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "capitalize": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Make the first letter of a work uppercase."}, "includeNumber": {"message": "<PERSON><PERSON><PERSON><PERSON> ciparu"}, "minNumbers": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ciparu skaits"}, "minSpecial": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>"}, "avoidAmbiguous": {"message": "Izvairīties no viegli sajaucamām rakstzīmēm", "description": "Label for the avoid ambiguous characters checkbox."}, "generatorPolicyInEffect": {"message": "Veidotāja iespējām tika piemērotas uzņēmējdarbības nosacījumu p<PERSON>.", "description": "Indicates that a policy limits the credential generator screen."}, "searchVault": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> gla<PERSON>"}, "edit": {"message": "<PERSON><PERSON>"}, "view": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "noItemsInList": {"message": "Nav vienumu, ko parād<PERSON>t."}, "itemInformation": {"message": "Vienuma informācija"}, "username": {"message": "Lietotājvārds"}, "password": {"message": "Parole"}, "totp": {"message": "Autentificētāja noslēpums"}, "passphrase": {"message": "Pa<PERSON><PERSON>"}, "favorite": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unfavorite": {"message": "Noņemt no izlases"}, "itemAddedToFavorites": {"message": "Vienums pievienots izlasē"}, "itemRemovedFromFavorites": {"message": "Vienums noņemts no izlases"}, "notes": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "privateNote": {"message": "<PERSON>isk<PERSON>"}, "note": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "editItem": {"message": "Labot vienumu"}, "folder": {"message": "Mape"}, "deleteItem": {"message": "Izdzēst vienumu"}, "viewItem": {"message": "<PERSON><PERSON><PERSON><PERSON> vien<PERSON>u"}, "launch": {"message": "Palaist"}, "launchWebsite": {"message": "At<PERSON><PERSON>rt tīmekļvietni"}, "launchWebsiteName": {"message": "Atvērt tīmekļvietni $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret item"}}}, "website": {"message": "Tīmekļa vietne"}, "toggleVisibility": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "manage": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "other": {"message": "Cits"}, "unlockMethods": {"message": "Atslēgšanas iespē<PERSON>"}, "unlockMethodNeededToChangeTimeoutActionDesc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON>l<PERSON> veids, lai main<PERSON>tu gla<PERSON> noildzes darbību."}, "unlockMethodNeeded": {"message": "J<PERSON>uzst<PERSON>da atslēgšanas veids iestatījumos"}, "sessionTimeoutHeader": {"message": "Sesijas noildze"}, "vaultTimeoutHeader": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "otherOptions": {"message": "Citas iespējas"}, "rateExtension": {"message": "Novērt<PERSON><PERSON> p<PERSON>"}, "browserNotSupportClipboard": {"message": "Pārl<PERSON><PERSON> neatbal<PERSON> vienkārš<PERSON> iev<PERSON> starpliktuvē. Tā vietā tas jāievieto starpliktuvē pašrocīgi."}, "verifyYourIdentity": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> savu identitāti"}, "weDontRecognizeThisDevice": {"message": "<PERSON><PERSON><PERSON>er<PERSON>. Jāievada kods, kas tika nosūtīts e-pastā, lai apliecinātu savu identitāti."}, "continueLoggingIn": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "yourVaultIsLocked": {"message": "Glabātava ir aizslēgta. Jāapliecina sava identitāte, lai turpin<PERSON>."}, "yourVaultIsLockedV2": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir a<PERSON>lēgta"}, "yourAccountIsLocked": {"message": "<PERSON><PERSON> ir slēgts"}, "or": {"message": "vai"}, "unlock": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "loggedInAsOn": {"message": "Pieteicies $HOSTNAME$ kā $EMAIL$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "hostname": {"content": "$2", "example": "bitwarden.com"}}}, "invalidMasterPassword": {"message": "Neder<PERSON>ga galvenā parole"}, "vaultTimeout": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "vaultTimeout1": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lockNow": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lockAll": {"message": "<PERSON>z<PERSON><PERSON><PERSON><PERSON> visu"}, "immediately": {"message": "Nekavējoties"}, "tenSeconds": {"message": "10 sekundes"}, "twentySeconds": {"message": "20 sekundes"}, "thirtySeconds": {"message": "30 sekundes"}, "oneMinute": {"message": "1 minūte"}, "twoMinutes": {"message": "2 minūtes"}, "fiveMinutes": {"message": "5 minūtes"}, "fifteenMinutes": {"message": "15 minūtes"}, "thirtyMinutes": {"message": "30 minūtes"}, "oneHour": {"message": "1 stunda"}, "fourHours": {"message": "4 stundas"}, "onLocked": {"message": "<PERSON><PERSON><PERSON> sist<PERSON><PERSON>"}, "onRestart": {"message": "Pēc pārlūka pā<PERSON><PERSON>"}, "never": {"message": "<PERSON><PERSON><PERSON>"}, "security": {"message": "Dr<PERSON><PERSON><PERSON><PERSON>"}, "confirmMasterPassword": {"message": "Aps<PERSON><PERSON><PERSON><PERSON> galveno paroli"}, "masterPassword": {"message": "Galvenā parole"}, "masterPassImportant": {"message": "<PERSON><PERSON><PERSON><PERSON> paroli nevar at<PERSON>, ja tā tiek a<PERSON>."}, "masterPassHintLabel": {"message": "Galvenās paroles nor<PERSON><PERSON>"}, "errorOccurred": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "emailRequired": {"message": "E-pasta adrese ir <PERSON>."}, "invalidEmail": {"message": "Nederīga e-pasta adrese."}, "masterPasswordRequired": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> galvenā parole."}, "confirmMasterPasswordRequired": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> galvenās paroles atkārtota ievadīšana."}, "masterPasswordMinlength": {"message": "Galvenajai parolei ir jābūt vismaz $VALUE$ rakstzīmes garai.", "description": "The Master Password must be at least a specific number of characters long.", "placeholders": {"value": {"content": "$1", "example": "8"}}}, "masterPassDoesntMatch": {"message": "Galvenās paroles aps<PERSON><PERSON><PERSON><PERSON><PERSON>."}, "newAccountCreated": {"message": "Jaunais konts ir izveidots. Tagad vari pieteikties."}, "newAccountCreated2": {"message": "Jaunais konts tika izveidots."}, "youHaveBeenLoggedIn": {"message": "Tu esi pieteicies."}, "youSuccessfullyLoggedIn": {"message": "Pieteikšanās bija ve<PERSON>ga"}, "youMayCloseThisWindow": {"message": "Šo logu var aizvērt"}, "masterPassSent": {"message": "<PERSON><PERSON><PERSON> nos<PERSON><PERSON>ām galvenās paroles norādi e-pastā."}, "verificationCodeRequired": {"message": "Apliecinājuma kods ir nepiecieša<PERSON>."}, "webauthnCancelOrTimeout": {"message": "Autentifikācija tika atcelta vai tā aizņēma pārāk daudz laika. Lūgums mēģināt vēlreiz."}, "invalidVerificationCode": {"message": "Nederīgs apliecinājuma kods"}, "valueCopied": {"message": "$VALUE$ ir starpliktuvē", "description": "Value has been copied to the clipboard.", "placeholders": {"value": {"content": "$1", "example": "Password"}}}, "autofillError": {"message": "Neizdevās automātiski aizpildīt izvēlēto vienumu šajā lapā. Tā vietā informācija ir jāievieto starpliktuvē un jāielīmē pašrocīgi."}, "totpCaptureError": {"message": "Neizdevās no<PERSON>t k<PERSON>drātkodu pašreizējā tīmekļa lapā"}, "totpCaptureSuccess": {"message": "Autentificētāja atslēga ir pievienota"}, "totpCapture": {"message": "Nolasīt autentificētāja kvadrātkodu pašreizējā tīmekļa lapā"}, "totpHelperTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> di<PERSON><PERSON> a<PERSON><PERSON><PERSON> plū<PERSON>u"}, "totpHelper": {"message": "Bitwarden var glabāt un aizpildīt divpakāpju apliecinājuma kodus. Atslēga jāievieto starpliktuvē un jāielīmē šajā laukā."}, "totpHelperWithCapture": {"message": "Bitwarden var glabāt un aizpildīt divpakāpju apliecinājuma kodus. Jāizv<PERSON><PERSON>, lai veiktu tīmekļvietnes autentificētāja kvadrātkoda ekrānuzņē<PERSON>, vai jāievieto starpliktuvē atslēga un jāielīmē šajā laukā."}, "learnMoreAboutAuthenticators": {"message": "Uzzin<PERSON>t vairāk par autentificētājiem"}, "copyTOTP": {"message": "Ievietot starpliktuvē autentificētāja atslēgu (TOTP)"}, "loggedOut": {"message": "Atte<PERSON>es"}, "loggedOutDesc": {"message": "Notika izrakstīša<PERSON>ā<PERSON> no Ta<PERSON> konta."}, "loginExpired": {"message": "Pieteik<PERSON><PERSON><PERSON><PERSON> sesija ir be<PERSON>."}, "logIn": {"message": "Pieteikties"}, "logInToBitwarden": {"message": "Pieteikties Bitwarden"}, "enterTheCodeSentToYourEmail": {"message": "Jāievada e-pastā nosūtītais kods"}, "enterTheCodeFromYourAuthenticatorApp": {"message": "Jāievada kods no savas autentificētājlietotnes"}, "pressYourYubiKeyToAuthenticate": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> sava <PERSON>, lai autentificētu"}, "duoTwoFactorRequiredPageSubtitle": {"message": "<PERSON><PERSON> <PERSON> divpakā<PERSON><PERSON>, lai pieteik<PERSON> savā kontā. <PERSON><PERSON><PERSON><PERSON> esoša<PERSON> nor<PERSON>, lai p<PERSON><PERSON><PERSON> piete<PERSON>."}, "followTheStepsBelowToFinishLoggingIn": {"message": "<PERSON><PERSON><PERSON><PERSON> es<PERSON>, lai p<PERSON><PERSON><PERSON>."}, "followTheStepsBelowToFinishLoggingInWithSecurityKey": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> zem<PERSON>k eso<PERSON><PERSON><PERSON>, lai p<PERSON><PERSON> pieteik<PERSON> ar savu drošības atslēgu."}, "restartRegistration": {"message": "Sākt reģistrēšanos no jauna"}, "expiredLink": {"message": "<PERSON><PERSON><PERSON> be<PERSON> derī<PERSON>s"}, "pleaseRestartRegistrationOrTryLoggingIn": {"message": "Lūgums sākt reģistrēšanos no jauna vai mēģināt pieteikties."}, "youMayAlreadyHaveAnAccount": {"message": "<PERSON><PERSON> jau varētu būt konts"}, "logOutConfirmation": {"message": "Vai tiešām atte<PERSON>?"}, "yes": {"message": "Jā"}, "no": {"message": "Nē"}, "location": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vieta"}, "unexpectedError": {"message": "<PERSON>r radus<PERSON> neparedz<PERSON>."}, "nameRequired": {"message": "Nosaukums ir ne<PERSON>."}, "addedFolder": {"message": "Pievienoja mapi"}, "twoStepLoginConfirmation": {"message": "Divpakāpju pieteikšanās padara kontu krietni droš<PERSON>ku, pieprasot apstiprināt pieteikšanos ar tādu citu ierīču vai pakalpojumu starpniecību kā dro<PERSON><PERSON> atsl<PERSON>ga, autentific<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, t<PERSON><PERSON><PERSON><PERSON><PERSON> zvan<PERSON> vai e-pasts. Divpakāpju pieteikšanos var iespējot bitwarden.com tīmekļa glabātavā. Vai tagad apmeklēt tīmekļvietni?"}, "twoStepLoginConfirmationContent": {"message": "Savu kontu var padarīt drošāku ar divpakāpju pieteikšanās uzstādī<PERSON>nu Bitwarden tīmekļa lietotnē."}, "twoStepLoginConfirmationTitle": {"message": "<PERSON><PERSON>riet uz tīmekļa lietotni?"}, "editedFolder": {"message": "Mape labota"}, "deleteFolderConfirmation": {"message": "Vai tiešām izdzēst šo mapi?"}, "deletedFolder": {"message": "Mape izdzēsta"}, "gettingStartedTutorial": {"message": "Uzsāk<PERSON><PERSON> p<PERSON>"}, "gettingStartedTutorialVideo": {"message": "Noskaties mūsu u<PERSON>, lai u<PERSON>, k<PERSON> <PERSON>eg<PERSON><PERSON> vis<PERSON><PERSON><PERSON><PERSON> labumu no pārlūka paplaš<PERSON>ju<PERSON>!"}, "syncingComplete": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "syncingFailed": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "passwordCopied": {"message": "Parole ievietota starpliktuvē"}, "uri": {"message": "URI"}, "uriPosition": {"message": "URI $POSITION$", "description": "A listing of URIs. Ex: URI 1, URI 2, URI 3, etc.", "placeholders": {"position": {"content": "$1", "example": "2"}}}, "newUri": {"message": "Jauns URI"}, "addDomain": {"message": "<PERSON><PERSON><PERSON> v<PERSON>", "description": "'Domain' here refers to an internet domain name (e.g. 'bitwarden.com') and the message in whole described the act of putting a domain value into the context."}, "addedItem": {"message": "Vienums pievienots"}, "editedItem": {"message": "Vienums labots"}, "deleteItemConfirmation": {"message": "Vai tiešām pār<PERSON>tot uz atkritni?"}, "deletedItem": {"message": "Vienums pārvietots uz atkritni"}, "overwritePassword": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> paroli"}, "overwritePasswordConfirmation": {"message": "Vai tiešām pārrakst<PERSON>t esošo paroli?"}, "overwriteUsername": {"message": "Pārraks<PERSON><PERSON><PERSON> lietotā<PERSON>u"}, "overwriteUsernameConfirmation": {"message": "Vai tiešām pārrakstīt pašreizējo lietotājvārdu?"}, "searchFolder": {"message": "Meklēt mapē"}, "searchCollection": {"message": "Me<PERSON><PERSON><PERSON><PERSON> k<PERSON>"}, "searchType": {"message": "Meklē<PERSON>nas veids"}, "noneFolder": {"message": "Nav mapes", "description": "This is the folder for uncategorized items"}, "enableAddLoginNotification": {"message": "<PERSON><PERSON><PERSON><PERSON>, lai pievienotu piete<PERSON> vienumu"}, "vaultSaveOptionsTitle": {"message": "<PERSON>g<PERSON><PERSON><PERSON><PERSON> glab<PERSON> iespējās"}, "addLoginNotificationDesc": {"message": "<PERSON><PERSON><PERSON><PERSON>, ja tāds nav atrodams glabātavā."}, "addLoginNotificationDescAlt": {"message": "<PERSON><PERSON><PERSON><PERSON>, vai pievie<PERSON> v<PERSON>, ja glab<PERSON>tavā tāds nav atrodams. Attiecas uz visiem kontiem, kuri ir pieteik<PERSON>."}, "showCardsInVaultViewV2": {"message": "Glabā<PERSON><PERSON> skatā vienmēr rādīt kartes kā automātiskās a<PERSON>lde<PERSON> ieteik<PERSON>us"}, "showCardsCurrentTab": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> kartes cilnes lapā"}, "showCardsCurrentTabDesc": {"message": "Attēlot kartes ciļņu lapā vieglākai aizpildīšanai."}, "showIdentitiesInVaultViewV2": {"message": "Glabā<PERSON><PERSON> skatā vienmēr rādīt identitātes kā automātiskās a<PERSON>lde<PERSON> iete<PERSON>us"}, "showIdentitiesCurrentTab": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> identitātes cilnes pārskatā"}, "showIdentitiesCurrentTabDesc": {"message": "Attēlot identitātes ciļņu lapā vieglākai aizpildei."}, "clickToAutofillOnVault": {"message": "Glabātavas skatā jāklikšķina uz vienumiem, lai automā<PERSON> a<PERSON>"}, "clickToAutofill": {"message": "Klikšķināt uz vienumiem automātiskās aizpildes ieteikum<PERSON>, lai aiz<PERSON>"}, "clearClipboard": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "clearClipboardDesc": {"message": "Automātiski noņemt starpliktuvē ievietotās v<PERSON>.", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "notificationAddDesc": {"message": "Vai Bitwarden atcerēties šo paroli?"}, "notificationAddSave": {"message": "Jā, saglabāt"}, "notificationViewAria": {"message": "Apskatīt $ITEMNAME$, tiks atvērts jaunā logā", "placeholders": {"itemName": {"content": "$1"}}, "description": "Aria label for the view button in notification bar confirmation message"}, "notificationNewItemAria": {"message": "Jauns vienums, atvērsies jaunā logā", "description": "Aria label for the new item button in notification bar confirmation message when error is prompted"}, "notificationEditTooltip": {"message": "Labot pirms sagla<PERSON><PERSON><PERSON>", "description": "Tooltip and Aria label for edit button on cipher item"}, "newNotification": {"message": "<PERSON><PERSON>ns <PERSON>"}, "labelWithNotification": {"message": "$LABEL$: jauns paziņ<PERSON>ms", "description": "Label for the notification with a new login suggestion.", "placeholders": {"label": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "notificationLoginSaveConfirmation": {"message": "saglabāts Bitwarden.", "description": "Shown to user after item is saved."}, "notificationLoginUpdatedConfirmation": {"message": "atjaunināts Bitwarden.", "description": "Shown to user after item is updated."}, "selectItemAriaLabel": {"message": "Atlasīt $ITEMTYPE$, $ITEMNAME$", "description": "Used by screen readers. $1 is the item type (like vault or folder), $2 is the selected item name.", "placeholders": {"itemType": {"content": "$1"}, "itemName": {"content": "$2"}}}, "saveAsNewLoginAction": {"message": "Saglabāt kā jaunu piete<PERSON> vienumu", "description": "Button text for saving login details as a new entry."}, "updateLoginAction": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pie<PERSON> vienumu", "description": "Button text for updating an existing login entry."}, "unlockToSave": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lai saglab<PERSON><PERSON>o piete<PERSON> vienumu", "description": "User prompt to take action in order to save the login they just entered."}, "saveLogin": {"message": "Saglabāt pie<PERSON> vienumu", "description": "Prompt asking the user if they want to save their login details."}, "updateLogin": {"message": "Atjaunināt es<PERSON> vienumu", "description": "Prompt asking the user if they want to update an existing login entry."}, "loginSaveSuccess": {"message": "Pieteikšanās vienums saglabāts", "description": "Message displayed when login details are successfully saved."}, "loginUpdateSuccess": {"message": "Pieteikšanās vienums atjaunināts", "description": "Message displayed when login details are successfully updated."}, "loginUpdateTaskSuccess": {"message": "Lielisks darbs! Tu attiecīgi r<PERSON>, lai padarītu savi un $ORGANIZATION$ drošāku.", "placeholders": {"organization": {"content": "$1"}}, "description": "Shown to user after login is updated."}, "loginUpdateTaskSuccessAdditional": {"message": "Paldies par $ORGANIZATION$ padarīšanu drošāku! Tev ir vēl $TASK_COUNT$ paroļu, ko atjaunin<PERSON>t.", "placeholders": {"organization": {"content": "$1"}, "task_count": {"content": "$2"}}, "description": "Shown to user after login is updated."}, "nextSecurityTaskAction": {"message": "<PERSON><PERSON><PERSON> paroli", "description": "Message prompting user to undertake completion of another security task."}, "saveFailure": {"message": "<PERSON><PERSON><PERSON><PERSON> sagla<PERSON> laik<PERSON>", "description": "Error message shown when the system fails to save login details."}, "saveFailureDetails": {"message": "Ak nē! Mēs nevarējā šo saglabāt. Jāmēģina pašrocīgi ievadīt informāciju.", "description": "Detailed error message shown when saving login details fails."}, "enableChangedPasswordNotification": {"message": "<PERSON><PERSON><PERSON><PERSON> at<PERSON>t esošu piete<PERSON> vienumu"}, "changedPasswordNotificationDesc": {"message": "<PERSON><PERSON><PERSON><PERSON> at<PERSON> pie<PERSON> vienuma paroli, ja tīmekļvietnē ir noteiktas tās i<PERSON>."}, "changedPasswordNotificationDescAlt": {"message": "<PERSON><PERSON><PERSON><PERSON>, vai atjauni<PERSON>t piete<PERSON><PERSON> vienuma paroli, kad tīmekļ<PERSON>tnē ir noteikta atšķirība. Attiecas uz visiem kontiem, kuri ir pieteikuš<PERSON>."}, "enableUsePasskeys": {"message": "<PERSON><PERSON><PERSON><PERSON>, vai saglabāt un izmantot piekļ<PERSON> at<PERSON>lēgas"}, "usePasskeysDesc": {"message": "<PERSON><PERSON><PERSON><PERSON>, vai saglabāt jaunas piekļuves atslēgas vai pieteikties ar glabātavā esošajām piekļuves atslēgām. Attiecas uz visiem kontiem, kuri ir pieteikuš<PERSON>."}, "notificationChangeDesc": {"message": "Vai at<PERSON>uni<PERSON>āt šo paroli Bitwarden?"}, "notificationChangeSave": {"message": "Jā, atjaunināt"}, "notificationUnlockDesc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lai p<PERSON><PERSON> automātisk<PERSON><PERSON> a<PERSON>lde<PERSON> piepra<PERSON>."}, "notificationUnlock": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "additionalOptions": {"message": "Pa<PERSON>ld<PERSON> iespējas"}, "enableContextMenuItem": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> konteksta izvēlnes iespējas"}, "contextMenuItemDesc": {"message": "<PERSON><PERSON><PERSON><PERSON> otrējo klikšķi, lai piekļūtu paroļu ve<PERSON> un tīmekļvietnei atbilstošajiem pieteikšanās vienumiem."}, "contextMenuItemDescAlt": {"message": "<PERSON>zman<PERSON>t otrējo klikšķi, lai piekļūtu paroļu ve<PERSON> un tīmekļvietnei atbilstošajiem pieteikšanās vienumiem. Attiecas uz visiem kontiem, kuri ir pieteik<PERSON>š<PERSON>."}, "defaultUriMatchDetection": {"message": "Noklusējuma URI atbilstības noteikšana", "description": "Default URI match detection for autofill."}, "defaultUriMatchDetectionDesc": {"message": "Izvēlē<PERSON> noklus<PERSON><PERSON><PERSON> veidu, kād<PERSON> tiek apstrād<PERSON>ta pieteik<PERSON>nās vienumu URI atbilstības noteikšana, kad tiek veiktas tādas darbības kā automātiska aizpilde."}, "theme": {"message": "Izskats"}, "themeDesc": {"message": "Mainīt lietotnes izskata krāsas."}, "themeDescAlt": {"message": "Mainīt lietotnes izskata krāsas. Attiecas uz visiem kontiem, kuri ir pieteikušies."}, "dark": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Dark color"}, "light": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Light color"}, "exportFrom": {"message": "Izgūt no"}, "exportVault": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON> saturu"}, "fileFormat": {"message": "Datnes veids"}, "fileEncryptedExportWarningDesc": {"message": "<PERSON><PERSON> dat<PERSON> iz<PERSON> būs a<PERSON>sar<PERSON> ar paroli, un būs nepiecieša<PERSON> datnes parole, lai to atšifrētu."}, "filePassword": {"message": "Datnes parole"}, "exportPasswordDescription": {"message": "Šī <PERSON> tiks i<PERSON>, lai izgūtu un ievietotu šo datni"}, "accountRestrictedOptionDescription": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> konta š<PERSON> atslēga, kas iegūta no lietotājvārda un galvenās paroles, lai šifrētu izguvi un atļautu ievietošanu tikai pašreizējā Bitwarden kontā."}, "passwordProtectedOptionDescription": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> paroli, lai <PERSON><PERSON><PERSON><PERSON><PERSON> izguvi un tad to ievietotu jebkurā Bitwarden kontā, i<PERSON><PERSON><PERSON><PERSON> at<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> paroli."}, "exportTypeHeading": {"message": "<PERSON>zg<PERSON><PERSON><PERSON> veids"}, "accountRestricted": {"message": "<PERSON>nts ir ierobežots"}, "filePasswordAndConfirmFilePasswordDoNotMatch": {"message": "\"Datnes parole\" un \"Apstiprināt datnes paroli\" vē<PERSON><PERSON><PERSON>."}, "warning": {"message": "UZMANĪBU", "description": "WARNING (should stay in capitalized letters if the language permits)"}, "warningCapitalized": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Warning (should maintain locale-relevant capitalization)"}, "confirmVaultExport": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gla<PERSON>vas satura izg<PERSON>"}, "exportWarningDesc": {"message": "<PERSON><PERSON> izguve satur glabātavas datus nešifrētā veidā. Izgūto datni nevajadzētu glabāt vai sūtīt nedro<PERSON> veido<PERSON> (piemēram, e-pastā). Tā ir jāizdzēš uzreiz pēc i<PERSON>."}, "encExportKeyWarningDesc": {"message": "<PERSON><PERSON> izguve šifrē datus ar konta šifrē<PERSON> atslēgu. Ja tā jebkad tiks main<PERSON>, iz<PERSON>di vajadz<PERSON>tu veikt v<PERSON><PERSON>, jo vairs nebūs iespējams atšifrēt šo datni."}, "encExportAccountWarningDesc": {"message": "<PERSON><PERSON> kontam ir <PERSON><PERSON><PERSON><PERSON><PERSON>, tād<PERSON><PERSON> nav iespējams ievietot šifrētu izguvi citā kontā."}, "exportMasterPassword": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> galveno paroli, lai izg<PERSON><PERSON> glab<PERSON><PERSON> saturu."}, "shared": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "bitwardenForBusinessPageDesc": {"message": "Bitwarden uzņēmējdarbībai nodrošina iespēju kopīgot savas glabātavas vienumus ar citiem, kad tiek izmantota apvienība. Vairāk var uzzināt tīmekļvietnē bitwarden.com."}, "moveToOrganization": {"message": "Pārvietot uz apvienību"}, "movedItemToOrg": {"message": "$ITEMNAME$ pārvietots uz $ORGNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "orgname": {"content": "$2", "example": "Company Name"}}}, "moveToOrgDesc": {"message": "Izvēlies apvienību, uz kuru pārvietot šo vienumu. P<PERSON>rvie<PERSON><PERSON><PERSON> nodod šī vienuma piederību apvienībai. Tu vairs nebūsi šī vienuma tiešais īpašnieks pēc tā pārvietošanas."}, "learnMore": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "authenticatorKeyTotp": {"message": "Autentificētāja atslēga (TOTP)"}, "verificationCodeTotp": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON> (TOTP)"}, "copyVerificationCode": {"message": "Ievietot apliecinājuma kodu starpliktuvē"}, "attachments": {"message": "<PERSON><PERSON><PERSON>"}, "deleteAttachment": {"message": "<PERSON>zdzēst pielikumu"}, "deleteAttachmentConfirmation": {"message": "Vai tiešām izdzēst šo pielikumu?"}, "deletedAttachment": {"message": "Pielikums izdzēsts"}, "newAttachment": {"message": "<PERSON><PERSON><PERSON>"}, "noAttachments": {"message": "Nav pielikumu."}, "attachmentSaved": {"message": "Pielikums tika saglabāts."}, "file": {"message": "<PERSON><PERSON><PERSON>"}, "fileToShare": {"message": "<PERSON><PERSON><PERSON>, ko kopīgot"}, "selectFile": {"message": "<PERSON><PERSON><PERSON> da<PERSON>"}, "maxFileSize": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>is datnes izmērs ir 500 MB."}, "featureUnavailable": {"message": "Iespēja nav pieejama"}, "legacyEncryptionUnsupported": {"message": "Mantota šifr<PERSON><PERSON><PERSON> vairs netiek atbalstīta. <PERSON><PERSON><PERSON><PERSON> sazin<PERSON> ar atbalstu, lai atkoptu savu kontu."}, "premiumMembership": {"message": "Premium dalība"}, "premiumManage": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "premiumManageAlert": {"message": "Dalību ir iespējams pārvaldīt bitwarden.com tīmekļa glabātavā. Vai tagad apmeklēt tīmekļvietni?"}, "premiumRefresh": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "premiumNotCurrentMember": {"message": "Tu pašlaik neesi Premium dalībnieks."}, "premiumSignUpAndGet": {"message": "Piesakies Premium dalībai un saņem:"}, "ppremiumSignUpStorage": {"message": "1 GB šifrētas krātuves datņu pielikumiem."}, "premiumSignUpEmergency": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "premiumSignUpTwoStepOptions": {"message": "<PERSON><PERSON><PERSON> slēgtā pirmavota divpakāpju pieteikša<PERSON>ās iespējas kā YubiKey un Duo."}, "ppremiumSignUpReports": {"message": "<PERSON><PERSON><PERSON><PERSON>, konta vesel<PERSON> un datu noplū<PERSON><PERSON> p<PERSON>, lai uzturētu glabātavu droš<PERSON>."}, "ppremiumSignUpTotp": {"message": "TOTP apliecinājuma koda (2FA) veidotājs glabātavas pieteikšanās vienumiem."}, "ppremiumSignUpSupport": {"message": "Priekšrocīgs lietotāju atbalsts."}, "ppremiumSignUpFuture": {"message": "Visas nākotnes Premium iespējas. Vairāk drīzumā!"}, "premiumPurchase": {"message": "Iegādāties Premium"}, "premiumPurchaseAlertV2": {"message": "Premium var iegādāties Bitwarden tīmekļa lietotnē sava konta iestatījumos."}, "premiumCurrentMember": {"message": "Tu esi Premium dalībnieks!"}, "premiumCurrentMemberThanks": {"message": "<PERSON>ld<PERSON>, ka atbalsti Bitwarden!"}, "premiumFeatures": {"message": "Uzlabo uz \"Premium\" un saņem:"}, "premiumPrice": {"message": "Viss par tikai $PRICE$ gadā!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "premiumPriceV2": {"message": "Viss par tikai $PRICE$ gadā.", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "refreshComplete": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "enableAutoTotpCopy": {"message": "Automātiski ievietot TOTP starpliktuvē"}, "disableAutoTotpCopyDesc": {"message": "<PERSON><PERSON> piete<PERSON><PERSON> vienumam ir pievienota autentificētāja atslēga, TOTP apliecinājuma kods tiks automātiski ievietots starpliktuvē, kad vien tiks automātiski aizpildīta piete<PERSON>n<PERSON> ve<PERSON>."}, "enableAutoBiometricsPrompt": {"message": "<PERSON><PERSON><PERSON>ot vaicāt biometriju"}, "premiumRequired": {"message": "Nepieciešams Premium"}, "premiumRequiredDesc": {"message": "<PERSON>r <PERSON><PERSON><PERSON>šama Premium dalība, lai izman<PERSON>tu <PERSON>o i<PERSON>ēju."}, "authenticationTimeout": {"message": "Autentific<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "authenticationSessionTimedOut": {"message": "Iestā<PERSON><PERSON><PERSON> no<PERSON>. Lūgums sākt pieteikšanos no jauna."}, "verificationCodeEmailSent": {"message": "Apliecinājuma e-pasta ziņojums nosūtīts uz $EMAIL$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "dontAskAgainOnThisDeviceFor30Days": {"message": "<PERSON><PERSON><PERSON> 30 dienas vairs neva<PERSON>t"}, "selectAnotherMethod": {"message": "Atlasīt citu veidu", "description": "Select another two-step login method"}, "useYourRecoveryCode": {"message": "Izmantot savu atkopes kodu"}, "insertU2f": {"message": "Ievieto savu drošības atslēgu datora USB ligzdā! Ja tai ir poga, pieskaries tai!"}, "openInNewTab": {"message": "<PERSON><PERSON><PERSON><PERSON> jaunā cilnē"}, "webAuthnAuthenticate": {"message": "Autentificēt WebAuthn"}, "readSecurityKey": {"message": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>lē<PERSON>"}, "awaitingSecurityKeyInteraction": {"message": "<PERSON><PERSON><PERSON> ar d<PERSON><PERSON> atslēgu..."}, "loginUnavailable": {"message": "Pie<PERSON><PERSON><PERSON><PERSON>ā<PERSON> nav pieejama"}, "noTwoStepProviders": {"message": "<PERSON><PERSON> k<PERSON> ir iespējota divpakāpju pieteik<PERSON>ā<PERSON>, bet šajā pārlūkā netiek atbalstīts neviens no uzstādītajiem divpakāpju pārbaudes nodrošin<PERSON>tājiem."}, "noTwoStepProviders2": {"message": "<PERSON><PERSON><PERSON><PERSON> izman<PERSON>t atbalstītu tīmekļa pārlū<PERSON> (piemēram Chrome) un/vai pievienot papildu nodro<PERSON>, kas tiek labāk atbalstīti da<PERSON> pārl<PERSON> (piemēram autentificētāja lietotni)."}, "twoStepOptions": {"message": "Divpakāpju pieteikša<PERSON>ā<PERSON>"}, "selectTwoStepLoginMethod": {"message": "<PERSON><PERSON><PERSON> veidu"}, "recoveryCodeDesc": {"message": "Zaudēta piekļuve visiem divpakāpju nodrošinātājiem? <PERSON><PERSON><PERSON> at<PERSON> kodus, lai atspējotu visus sava konta divpakāpju nodrošinātājus!"}, "recoveryCodeTitle": {"message": "Atgūšanas kods"}, "authenticatorAppTitle": {"message": "Autentificētāja lieto<PERSON>ne"}, "authenticatorAppDescV2": {"message": "Jāievada autentific<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Bitwarden Authenticator, izveidots kods.", "description": "'Bitwarden Authenticator' is a product name and should not be translated."}, "yubiKeyTitleV2": {"message": "Yubico OTP drošības atslēga"}, "yubiKeyDesc": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, lai <PERSON><PERSON><PERSON> sa<PERSON>m kont<PERSON>. Darbojas ar <PERSON> 4, 4 <PERSON><PERSON>, 4C un NEO ierīcēm."}, "duoDescV2": {"message": "Jāievada Duo Security izveidots kods.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "duoOrganizationDesc": {"message": "A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar savas apvienības Duo Security, izmantojot Duo Mobile lietotni, <PERSON><PERSON><PERSON><PERSON><PERSON>, t<PERSON><PERSON><PERSON><PERSON><PERSON> zvanu vai U2F drošības atslēgu.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "webAuthnTitle": {"message": "FIDO2 WebAuthn"}, "webAuthnDesc": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>Authn atbalstoš<PERSON> d<PERSON> atslēgu, lai piek<PERSON><PERSON><PERSON> kont<PERSON>."}, "emailTitle": {"message": "E-pasts"}, "emailDescV2": {"message": "Jāievada e-pastā nosūtītais kods."}, "selfHostedEnvironment": {"message": "<PERSON>š<PERSON><PERSON><PERSON><PERSON> vide"}, "selfHostedBaseUrlHint": {"message": "Jānorāda sava pašizvietotā Bitward servera pamata URL. Piemērs: https://bitwarden.uznemums.lv"}, "selfHostedCustomEnvHeader": {"message": "Pa<PERSON><PERSON><PERSON> konfigurācijā ir iespējams norādīt URL katram pakalpojumam atsevišķi."}, "selfHostedEnvFormInvalid": {"message": "Jāpievieno vai no servera pamata URL vai vismaz viena pielāgota vide."}, "customEnvironment": {"message": "Pielāgota vide"}, "baseUrl": {"message": "Servera URL"}, "selfHostBaseUrl": {"message": "Pašmitināta servera URL", "description": "Label for field requesting a self-hosted integration service URL"}, "apiUrl": {"message": "API servera URL"}, "webVaultUrl": {"message": "Tīmekļa glabātavas servera URL"}, "identityUrl": {"message": "Identitātes servera URL"}, "notificationsUrl": {"message": "Paziņojumu servera URL"}, "iconsUrl": {"message": "Ikonu servera URL"}, "environmentSaved": {"message": "Vides URL ir sa<PERSON>."}, "showAutoFillMenuOnFormFields": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> automātisk<PERSON><PERSON> a<PERSON>lde<PERSON> izvēlni veidlapu laukos", "description": "Represents the message for allowing the user to enable the autofill overlay"}, "autofillSuggestionsSectionTitle": {"message": "Automātisk<PERSON><PERSON> a<PERSON> i<PERSON>"}, "autofillSpotlightTitle": {"message": "Viegla automātiskās aizpildes ieteikumu at<PERSON>na"}, "autofillSpotlightDesc": {"message": "Jāizslēdz sava pārlūka automātiskās a<PERSON> i<PERSON>, lai tie nebūtu pretrunā ar Bitwarden."}, "turnOffBrowserAutofill": {"message": "Izslēgt $BROWSER$ automātisko aizpildi", "placeholders": {"browser": {"content": "$1", "example": "Chrome"}}}, "turnOffAutofill": {"message": "<PERSON>z<PERSON><PERSON><PERSON>gt automātis<PERSON>"}, "showInlineMenuLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON> i<PERSON>ik<PERSON> ve<PERSON> la<PERSON>"}, "showInlineMenuIdentitiesLabel": {"message": "Att<PERSON><PERSON> identitātes kā ieteikumus"}, "showInlineMenuCardsLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> kartes k<PERSON>us"}, "showInlineMenuOnIconSelectionLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>, kad tiek atlas<PERSON><PERSON> i<PERSON>a"}, "showInlineMenuOnFormFieldsDescAlt": {"message": "Attiecas uz visiem kontiem, kuri ir pieteikušies."}, "turnOffBrowserBuiltInPasswordManagerSettings": {"message": "Jāizslēdz sava pārlūka iebūvētā paroļu pārvaldnieka iestatījumi, lai izvairītos no nesaderībām."}, "turnOffBrowserBuiltInPasswordManagerSettingsLink": {"message": "<PERSON><PERSON><PERSON> p<PERSON> i<PERSON>."}, "autofillOverlayVisibilityOff": {"message": "<PERSON>z<PERSON>lē<PERSON><PERSON>", "description": "Overlay setting select option for disabling autofill overlay"}, "autofillOverlayVisibilityOnFieldFocus": {"message": "<PERSON><PERSON> lauk<PERSON> ir atlas<PERSON> (atlasot)", "description": "Overlay appearance select option for showing the field on focus of the input element"}, "autofillOverlayVisibilityOnButtonClick": {"message": "Kad tiek atlasīta automātiskās aizpilde<PERSON> ikona", "description": "Overlay appearance select option for showing the field on click of the overlay icon"}, "enableAutoFillOnPageLoadSectionTitle": {"message": "Automā<PERSON><PERSON> a<PERSON>t lapas iel<PERSON> brīdī"}, "enableAutoFillOnPageLoad": {"message": "Automā<PERSON><PERSON> a<PERSON>t lapas iel<PERSON> brīdī"}, "enableAutoFillOnPageLoadDesc": {"message": "<PERSON>a tiek noteikta piete<PERSON>, tā tiks aizpildīta lapas ielādes brīdī."}, "experimentalFeature": {"message": "Pārveidotās vai neuzticamās tīmekļvietnēs automātiskā aizpilde lapas ielādes laikā var tikt ļaunprātī<PERSON> i<PERSON>."}, "learnMoreAboutAutofillOnPageLoadLinkText": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> vairāk par iespējamām bīstamībām"}, "learnMoreAboutAutofill": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> vairāk par automā<PERSON>ko a<PERSON>pildi"}, "defaultAutoFillOnPageLoad": {"message": "Noklusējuma automātiskās aizpildes iestatījums pieteikšanās vienumiem"}, "defaultAutoFillOnPageLoadDesc": {"message": "Automātisko a<PERSON>i lapas ielādes brīdī atsevišķiem pieteikšanās vienumiem var atslēgt vienuma laboša<PERSON> skatā."}, "itemAutoFillOnPageLoad": {"message": "Automā<PERSON><PERSON> a<PERSON>t lapas ielā<PERSON> brīdī (ja iespējots iestatījumos)"}, "autoFillOnPageLoadUseDefault": {"message": "Izmantot noklusējuma iestatījumu"}, "autoFillOnPageLoadYes": {"message": "Automā<PERSON><PERSON> a<PERSON>t lapas iel<PERSON> brīdī"}, "autoFillOnPageLoadNo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lapas ielādes brīdī"}, "commandOpenPopup": {"message": "At<PERSON><PERSON><PERSON> glabā<PERSON>vas u<PERSON> logu"}, "commandOpenSidebar": {"message": "<PERSON><PERSON><PERSON><PERSON> glab<PERSON>vu sānu joslā"}, "commandAutofillLoginDesc": {"message": "Automātiski aizpildīt ar iep<PERSON> i<PERSON>ā<PERSON> vienumu pašreizējā tīmekļvietnē"}, "commandAutofillCardDesc": {"message": "Automātiski aizpildīt ar ieprie<PERSON>š i<PERSON>to karti pašreizējā tīmekļvietnē"}, "commandAutofillIdentityDesc": {"message": "Automātiski aizpildīt ar iepriekš izmantoto identitāti pašreizējā tīmekļvietnē"}, "commandGeneratePasswordDesc": {"message": "Izveidot jaunu nejau<PERSON>u paroli un ievietot to starpliktuvē"}, "commandLockVaultDesc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "customFields": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> lauki"}, "copyValue": {"message": "Ievietot vērtību starpliktuvē"}, "value": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "newCustomField": {"message": "Jauns pielā<PERSON> lauks"}, "dragToSort": {"message": "<PERSON>ilkt, lai kārtotu"}, "dragToReorder": {"message": "<PERSON>il<PERSON>, lai pārkārtotu"}, "cfTypeText": {"message": "Teksts"}, "cfTypeHidden": {"message": "Paslēpts"}, "cfTypeBoolean": {"message": "Patiesuma vērtība"}, "cfTypeCheckbox": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "cfTypeLinked": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "This describes a field that is 'linked' (tied) to another field."}, "linkedValue": {"message": "<PERSON><PERSON><PERSON><PERSON> vērt<PERSON>ba", "description": "This describes a value that is 'linked' (tied) to another value."}, "popup2faCloseMessage": {"message": "Klikšķināšana ārpus uznirstošā loga, lai e-pastā apskatītu aplie<PERSON>ju<PERSON> kodu, to aizvērs. Vai atvērt to atsevišķā logā, lai tas netiktu aizvērts?"}, "popupU2fCloseMessage": {"message": "<PERSON><PERSON> pārlūks nevar apstrādāt U2F pieprasījumus šajā uznirstošajā logā. Vai atvērt to atsevišķā logā, lai varētu pieteik<PERSON>, izmantojot U2F?"}, "enableFavicon": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> tīmekļvietņu ikonas"}, "faviconDesc": {"message": "Att<PERSON><PERSON> atpazīstamu attēlu pie katra pieteikšanās vienuma."}, "faviconDescAlt": {"message": "<PERSON>r<PERSON><PERSON><PERSON><PERSON> atpa<PERSON> attēlu pie katra pieteikšanās vienuma. Attiecas uz visiem kontiem, kuros ir notikusi pieteikšanās."}, "enableBadgeCounter": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>ta <PERSON>"}, "badgeCounterDesc": {"message": "Attēlot pašreizējāi tīmekļa vietnei atbilstošo pieteik<PERSON>ā<PERSON> vienumu skaitu."}, "cardholderName": {"message": "<PERSON><PERSON><PERSON> v<PERSON>"}, "number": {"message": "<PERSON><PERSON><PERSON>"}, "brand": {"message": "Zī<PERSON>ls"}, "expirationMonth": {"message": "<PERSON><PERSON><PERSON><PERSON> mēnesis"}, "expirationYear": {"message": "<PERSON><PERSON><PERSON><PERSON> gads"}, "expiration": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "january": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "february": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "march": {"message": "Marts"}, "april": {"message": "<PERSON><PERSON><PERSON>"}, "may": {"message": "<PERSON><PERSON><PERSON>"}, "june": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "july": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "august": {"message": "Augusts"}, "september": {"message": "Septembris"}, "october": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "november": {"message": "Novembris"}, "december": {"message": "Decembris"}, "securityCode": {"message": "Drošības kods"}, "ex": {"message": "piem."}, "title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "mr": {"message": "K-gs"}, "mrs": {"message": "K-dze"}, "ms": {"message": "Jk-dze"}, "dr": {"message": "Dr."}, "mx": {"message": "Mx"}, "firstName": {"message": "<PERSON><PERSON><PERSON>"}, "middleName": {"message": "Citi vārdi"}, "lastName": {"message": "Uzvārds"}, "fullName": {"message": "Pilnais vārds"}, "identityName": {"message": "Identitā<PERSON> nosa<PERSON>"}, "company": {"message": "Uzņēmums"}, "ssn": {"message": "Personas kods"}, "passportNumber": {"message": "<PERSON><PERSON> numurs"}, "licenseNumber": {"message": "Autovadītāja apliecības numurs"}, "email": {"message": "E-pasts"}, "phone": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "address": {"message": "<PERSON><PERSON><PERSON>"}, "address1": {"message": "Adrese 1"}, "address2": {"message": "Adrese 2"}, "address3": {"message": "Adrese 3"}, "cityTown": {"message": "Pilsēta / ciems"}, "stateProvince": {"message": "Novads / pagasts"}, "zipPostalCode": {"message": "Pasta indekss"}, "country": {"message": "Valsts"}, "type": {"message": "Veids"}, "typeLogin": {"message": "Pieteikša<PERSON>ā<PERSON> vienums"}, "typeLogins": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vien<PERSON>"}, "typeSecureNote": {"message": "<PERSON><PERSON><PERSON>"}, "typeCard": {"message": "<PERSON><PERSON>"}, "typeIdentity": {"message": "Identitā<PERSON>"}, "typeSshKey": {"message": "SSH atslēga"}, "newItemHeader": {"message": "Jauns/a $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "editItemHeader": {"message": "Labot $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "viewItemHeader": {"message": "Apskatīt $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "passwordHistory": {"message": "Paroļu vēsture"}, "generatorHistory": {"message": "Veidotāja vēsture"}, "clearGeneratorHistoryTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON> vēst<PERSON>"}, "cleargGeneratorHistoryDescription": {"message": "Turpinot visi veidotāja vēstures ieraksti tiks neatgrieziniski izdzēsti. Vai tiešām turpināt?"}, "back": {"message": "Atpakaļ"}, "collections": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "nCollections": {"message": "$COUNT$ krājumi", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "favorites": {"message": "<PERSON><PERSON><PERSON>"}, "popOutNewWindow": {"message": "Atvērt atsevišķā logā"}, "refresh": {"message": "Atsvaidzināt"}, "cards": {"message": "<PERSON><PERSON><PERSON>"}, "identities": {"message": "<PERSON><PERSON>it<PERSON><PERSON>"}, "logins": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vien<PERSON>"}, "secureNotes": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sshKeys": {"message": "SSH atslēgas"}, "clear": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "To clear something out. example: To clear browser history."}, "checkPassword": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, vai parole ir bijusi noplu<PERSON>ta."}, "passwordExposed": {"message": "Šī parole datu noplūdēs ir atklāta $VALUE$ reizi(es). To vajadz<PERSON>tu nomain<PERSON>t.", "placeholders": {"value": {"content": "$1", "example": "2"}}}, "passwordSafe": {"message": "Šī parole netika atrasta nevienā zināmā datu noplūdē. Tai vajadzētu būt droši i<PERSON>."}, "baseDomain": {"message": "<PERSON><PERSON> dom<PERSON>ns", "description": "Domain name. Ex. website.com"}, "baseDomainOptionRecommended": {"message": "Pamata domēns (ieteicams)", "description": "Domain name. Ex. website.com"}, "domainName": {"message": "<PERSON><PERSON><PERSON>", "description": "Domain name. Ex. website.com"}, "host": {"message": "Saimniekdators", "description": "A URL's host value. For example, the host of https://sub.domain.com:443 is 'sub.domain.com:443'."}, "exact": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "startsWith": {"message": "<PERSON><PERSON><PERSON> ar"}, "regEx": {"message": "Reg<PERSON><PERSON><PERSON><PERSON>", "description": "A programming term, also known as 'RegEx'."}, "matchDetection": {"message": "Atbilstības note<PERSON>", "description": "URI match detection for autofill."}, "defaultMatchDetection": {"message": "Noklusējuma atbilstības noteikšana", "description": "Default URI match detection for autofill."}, "toggleOptions": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "toggleCurrentUris": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pa<PERSON><PERSON>iz<PERSON>jos URI", "description": "Toggle the display of the URIs of the currently open tabs in the browser."}, "currentUri": {"message": "Pašreizējais URI", "description": "The URI of one of the current open tabs in the browser."}, "organization": {"message": "Apvienība", "description": "An entity of multiple related people (ex. a team or business organization)."}, "types": {"message": "<PERSON><PERSON><PERSON>"}, "allItems": {"message": "Visi vienumi"}, "noPasswordsInList": {"message": "Nav paro<PERSON>u, ko parā<PERSON>t."}, "clearHistory": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>"}, "nothingToShow": {"message": "Nav nekā, ko parādīt"}, "nothingGeneratedRecently": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON> nav nekas izveidots"}, "remove": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "default": {"message": "Noklusējums"}, "dateUpdated": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "ex. Date this item was updated"}, "dateCreated": {"message": "Izveidots", "description": "ex. Date this item was created"}, "datePasswordUpdated": {"message": "<PERSON><PERSON><PERSON>", "description": "ex. Date this password was updated"}, "neverLockWarning": {"message": "<PERSON>ai tiešām izmantot uzstād<PERSON>ju<PERSON> \"Nekad\"? Uzstādot aizslēgšanas iespēju uz \"Nekad\", <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> atslēga tiek glabāta ierīcē. Ja šī iespēja tiek izman<PERSON>ta, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka ierīce tiek pienācīgi aizsar<PERSON>ta."}, "noOrganizationsList": {"message": "Tu neesi iekļauts nevienā apvienībā. Apvienības sniedz iespēju droši kopīgot vienumus ar citiem lietotājiem."}, "noCollectionsInList": {"message": "<PERSON>v k<PERSON><PERSON><PERSON><PERSON>, ko par<PERSON>."}, "ownership": {"message": "Īpašumtiesības"}, "whoOwnsThisItem": {"message": "Kam pieder šis vienums?"}, "strong": {"message": "Spē<PERSON>īga", "description": "ex. A strong password. Scale: Weak -> Good -> Strong"}, "good": {"message": "Lab<PERSON>", "description": "ex. A good password. Scale: Weak -> Good -> Strong"}, "weak": {"message": "<PERSON><PERSON><PERSON>", "description": "ex. A weak password. Scale: Weak -> Good -> Strong"}, "weakMasterPassword": {"message": "<PERSON><PERSON><PERSON> gal<PERSON>"}, "weakMasterPasswordDesc": {"message": "<PERSON><PERSON>su izvēlētā galvenā parole ir vāja. Jums vajadzētu izmantot drošu galveno paroli (vai paroles vārdkopu), lai pienā<PERSON><PERSON>gi aizsargātu savu Bitwarden kontu. Vai tiešām vēlaties izmantot šo galveno paroli?"}, "pin": {"message": "PIN", "description": "PIN code. Ex. The short code (often numeric) that you use to unlock a device."}, "unlockWithPin": {"message": "Atslēgt ar P<PERSON>"}, "setYourPinTitle": {"message": "Iestatīt PIN"}, "setYourPinButton": {"message": "Iestatīt PIN"}, "setYourPinCode": {"message": "Iestatīt PIN kodu Bitwarden atslēgšanai. PIN iestatījumi tiks atiestatīti pēc pilnīgas izrakstīšanās no lietotnes."}, "setPinCode": {"message": "Šo PIN var i<PERSON>, lai atslēgtu Bitwarden. PIN tiks atiestatīts pēc pilnīgas atteikšanās lietotnē."}, "pinRequired": {"message": "<PERSON><PERSON> <PERSON> PIN kods."}, "invalidPin": {"message": "Nederīgs PIN kods."}, "tooManyInvalidPinEntryAttemptsLoggingOut": {"message": "Pārāk daudz nederīgu PIN ievadīšanas mēģinājumu. Atsakās."}, "unlockWithBiometrics": {"message": "Atslēgt ar biometriju"}, "unlockWithMasterPassword": {"message": "Atslēgt ar galveno paroli"}, "awaitDesktop": {"message": "Tiek gaidīts apstiprinājums no darbvirsmas"}, "awaitDesktopDesc": {"message": "Lūgums apstiprināt ar biometriju Bitwarden darbvirsmas lieto<PERSON>, lai iespējotu biometriju pārlūkā."}, "lockWithMasterPassOnRestart": {"message": "Aizslēgt ar galveno paroli pēc pārlūka at<PERSON>"}, "lockWithMasterPassOnRestart1": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> galveno paroli pēc pārlūka pārsāk<PERSON><PERSON><PERSON>"}, "selectOneCollection": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vismaz viens krājums."}, "cloneItem": {"message": "Pavairot vienumu"}, "clone": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "passwordGenerator": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "usernameGenerator": {"message": "Lietotāj<PERSON><PERSON><PERSON><PERSON> ve<PERSON>s"}, "useThisEmail": {"message": "Izmantot šo e-pasta adresi"}, "useThisPassword": {"message": "Izmantot š<PERSON> paroli"}, "useThisPassphrase": {"message": "Izmantot šo paroles vārdkopu"}, "useThisUsername": {"message": "Izmantot šo lieto<PERSON>āj<PERSON>u"}, "securePasswordGenerated": {"message": "Dr<PERSON>ša <PERSON> izveidota. Neaizmirsti arī at<PERSON>n<PERSON>t savu paroli tīmekļvietnē!"}, "useGeneratorHelpTextPartOne": {"message": "Veidotā<PERSON>s ir <PERSON>", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "useGeneratorHelpTextPartTwo": {"message": ", lai izveidotu spēcīgu un vienreizēju paroli", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "vaultCustomization": {"message": "Glabā<PERSON><PERSON>"}, "vaultTimeoutAction": {"message": "Glabātavas noildzes darbība"}, "vaultTimeoutAction1": {"message": "Noildzes darbība"}, "lock": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Verb form: to make secure or inaccessible by"}, "trash": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Noun: a special folder to hold deleted items"}, "searchTrash": {"message": "Meklēt at<PERSON>rit<PERSON>ē"}, "permanentlyDeleteItem": {"message": "Neatgriezeniski izdzēst vienumu"}, "permanentlyDeleteItemConfirmation": {"message": "Vai tiešām neatgriezeniski izdzēst šo vienumu?"}, "permanentlyDeletedItem": {"message": "Vienums ir neatgriezeniski izdzēsts"}, "restoreItem": {"message": "Atjaunot vienumu"}, "restoredItem": {"message": "Vienums atjaunots"}, "alreadyHaveAccount": {"message": "Jau ir konts?"}, "vaultTimeoutLogOutConfirmation": {"message": "Atteikšanās noņems piekļuvi glabātavai un pieprasīs tiešsaistes pieteikšanos pēc noildzes laika. Vai tiešām izmantot šo iestatījumu?"}, "vaultTimeoutLogOutConfirmationTitle": {"message": "Noildzes darbības aps<PERSON>na"}, "autoFillAndSave": {"message": "Automātiski a<PERSON>t un saglabāt"}, "fillAndSave": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> un saglabāt"}, "autoFillSuccessAndSavedUri": {"message": "Automātiski aizpildīts vienums un saglabāts URI"}, "autoFillSuccess": {"message": "Automātiski aizpildīts vienums"}, "insecurePageWarning": {"message": "Brīdinājums: <PERSON><PERSON> ir <PERSON><PERSON><PERSON><PERSON> HTTP lapa, un ir iespējams, ka citi var redzēt vai mainīt visu tajā iesniegto informāciju. Šis pieteikšanās vienums sākotnēji tika saglabāts drošā (HTTPS) lapā."}, "insecurePageWarningFillPrompt": {"message": "Vai tiešām joprojām aizpildīt šo piete<PERSON> vienumu?"}, "autofillIframeWarning": {"message": "<PERSON><PERSON><PERSON><PERSON> ir izvietota citā domēnā, nekā saglab<PERSON>tā pieteikšanās vienuma URI. J<PERSON>izv<PERSON><PERSON> \"Labi\", lai vienalga automātis<PERSON> a<PERSON>, vai \"Atcelt\", lai aptur<PERSON><PERSON>."}, "autofillIframeWarningTip": {"message": "Lai novērstu šī brī<PERSON><PERSON><PERSON> turpmāku rā<PERSON>, jā<PERSON><PERSON><PERSON><PERSON><PERSON> šis URI, $HOSTNAME$, šīs vietnes Bitwarde pieteikšanās vienum<PERSON>.", "placeholders": {"hostname": {"content": "$1", "example": "www.example.com"}}}, "setMasterPassword": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> galveno paroli"}, "currentMasterPass": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gal<PERSON>"}, "newMasterPass": {"message": "<PERSON><PERSON><PERSON><PERSON> galvenā parole"}, "confirmNewMasterPass": {"message": "Aps<PERSON><PERSON><PERSON><PERSON> jauno galveno paroli"}, "masterPasswordPolicyInEffect": {"message": "Viena vai vairākas apvienības nosacīju<PERSON> ir nor<PERSON>, lai galvenā parole atbilst šādām prasībām:"}, "policyInEffectMinComplexity": {"message": "Mazākais <PERSON>au<PERSON> sarežģītības novērtējums ir $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "policyInEffectMinLength": {"message": "Mazākais pieļaujamais garums ir $LENGTH$", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "policyInEffectUppercase": {"message": "Satur vienu vai vairākus lielos burtus"}, "policyInEffectLowercase": {"message": "Satur vienu vai vairākus mazos burtus"}, "policyInEffectNumbers": {"message": "Satur vienu vai vairākus skaitļus"}, "policyInEffectSpecial": {"message": "Satur vienu vai vairākas no šīm īpašajām rakstzīmēm: $CHARS$", "placeholders": {"chars": {"content": "$1", "example": "!@#$%^&*"}}}, "masterPasswordPolicyRequirementsNotMet": {"message": "Jaunā galvenā parole neatbilst nosa<PERSON><PERSON><PERSON><PERSON> pras<PERSON>."}, "receiveMarketingEmailsV2": {"message": "<PERSON><PERSON><PERSON><PERSON> savā ies<PERSON><PERSON><PERSON><PERSON> padomus, pazi<PERSON><PERSON><PERSON><PERSON> un izpētes iespējas no Bitwarden."}, "unsubscribe": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "atAnyTime": {"message": "j<PERSON><PERSON><PERSON><PERSON> laik<PERSON>."}, "byContinuingYouAgreeToThe": {"message": "Turpinot tiek sniegta piekrišana"}, "and": {"message": "un"}, "acceptPolicies": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON> atz<PERSON>ēšanu tiek piekrists se<PERSON>:"}, "acceptPoliciesRequired": {"message": "Nav apstiprināti izman<PERSON>šanas noteikumi un privātuma nosacījumi."}, "termsOfService": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "privacyPolicy": {"message": "<PERSON><PERSON>v<PERSON><PERSON><PERSON>"}, "yourNewPasswordCannotBeTheSameAsYourCurrentPassword": {"message": "Jaunā parole nevar būt tāda pati kā pašreizējā."}, "hintEqualsPassword": {"message": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> būt tāda pati kā parole."}, "ok": {"message": "<PERSON><PERSON>"}, "errorRefreshingAccessToken": {"message": "Piekļuves pilnvaras atsvaizināšanas kļūda"}, "errorRefreshingAccessTokenDesc": {"message": "Netika atrastas atsvaidzināšanas pilnvaras vai API atslēgas. Lūgums mēģināt izrakstīties un atkal pieteikties."}, "desktopSyncVerificationTitle": {"message": "Darb<PERSON><PERSON><PERSON> a<PERSON>"}, "desktopIntegrationVerificationText": {"message": "<PERSON><PERSON><PERSON><PERSON>, ka darb<PERSON><PERSON>s lietotne rāda šo at<PERSON> vārdkopu:"}, "desktopIntegrationDisabledTitle": {"message": "Savienojums ar pārlūku nav iespējots"}, "desktopIntegrationDisabledDesc": {"message": "Savienojums ar pārlūku nav iespējots Bitwarden darbvirsmas lietotnē. L<PERSON><PERSON><PERSON> iespējot to darbvirsmas lietotnes iestatījumos."}, "startDesktopTitle": {"message": "Palaist Bitwarden darbvirsmas lietotni"}, "startDesktopDesc": {"message": "Bitwarden darbvirsmas lietotnei ir jāb<PERSON>t s<PERSON>, pirms š<PERSON> iespēja var tikt i<PERSON>."}, "errorEnableBiometricTitle": {"message": "Nevar iespējot biometriju"}, "errorEnableBiometricDesc": {"message": "Darbvirsmas lietot<PERSON> atcē<PERSON> da<PERSON>"}, "nativeMessagingInvalidEncryptionDesc": {"message": "Darbvirsmas lietotne drošo saziņas avotu padarīja par nederīgu. Lūgums atkārtot šo darb<PERSON>bu"}, "nativeMessagingInvalidEncryptionTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON> tika p<PERSON>"}, "nativeMessagingWrongUserDesc": {"message": "Darbvirsmas lietotne ir pieteikusies atšķirīgā kontā. <PERSON><PERSON><PERSON><PERSON>, ka abas lietotnes ir pieteikušās vienā un tajā pašā kontā."}, "nativeMessagingWrongUserTitle": {"message": "Konta nesaderība"}, "nativeMessagingWrongUserKeyTitle": {"message": "Biometriskās atslēgas neatbilstība"}, "nativeMessagingWrongUserKeyDesc": {"message": "Biometriskā atslēgšana neizdevās. Biometriskā slepenā atslēgai neizdevās atslēgt glabātavu. Lūgums vēlreiz mēģināt iestatīt biometriju."}, "biometricsNotEnabledTitle": {"message": "Biometrija nav iespējota"}, "biometricsNotEnabledDesc": {"message": "Vispirms ir nepieciešams iespējot biometriju darbvirsmas iestatījumos, lai to varētu i<PERSON>t pārlūkā."}, "biometricsNotSupportedTitle": {"message": "Biometrija nav nodrošināta"}, "biometricsNotSupportedDesc": {"message": "<PERSON><PERSON><PERSON> i<PERSON> netiek atbalstīta pārlūka biometrija."}, "biometricsNotUnlockedTitle": {"message": "Lietotājs a<PERSON>lē<PERSON>s vai izrakstījies"}, "biometricsNotUnlockedDesc": {"message": "Lū<PERSON>s atslēgt šo lietotāju darbvirsmas lietotnē un mēģināt vēlreiz."}, "biometricsNotAvailableTitle": {"message": "Atslēgšana ar biometriju nav pieejama"}, "biometricsNotAvailableDesc": {"message": "Atslēgšana ar biometriju pašlaik nav pieejama. Lūgums vēlāk mēģināt vēlreiz."}, "biometricsFailedTitle": {"message": "Biometrija neizdevās"}, "biometricsFailedDesc": {"message": "Biometriju <PERSON>, jāapsver galvenās paroles i<PERSON><PERSON><PERSON> vai atteik<PERSON>nā<PERSON>. <PERSON>a <PERSON> t<PERSON>, l<PERSON><PERSON><PERSON> sazin<PERSON> ar Bitwarden atbalstu."}, "nativeMessaginPermissionErrorTitle": {"message": "Atļauja nav nodroš<PERSON>āta"}, "nativeMessaginPermissionErrorDesc": {"message": "Bez atļaujas sazināties ar Bitwarden darbvirsmas lietotni mēs nevaram nodrošināt biometriju pārlūka paplašinājumā. Lūgums mēģināt vēlreiz."}, "nativeMessaginPermissionSidebarTitle": {"message": "Atļaujas pieprasījuma k<PERSON>"}, "nativeMessaginPermissionSidebarDesc": {"message": "<PERSON><PERSON> da<PERSON>ba nav izpild<PERSON>ma s<PERSON>, tāpēc lūgums mēģināt to veikt uz<PERSON> vai jaunā logā."}, "personalOwnershipSubmitError": {"message": "Uzņēmuma nosacījumi liedz saglabāt vienumus privātajā glabātavā. Norādi piederību apvienībai un izvēlies kādu no pieejamajiem krājumiem."}, "personalOwnershipPolicyInEffect": {"message": "Apvienības nosacījumi ietekmē Tavas <PERSON>tiesī<PERSON> iespē<PERSON>."}, "personalOwnershipPolicyInEffectImports": {"message": "Apvienības nosacījums neļauj ievietot ār<PERSON><PERSON> vienumus savā personīgajā glabātavā."}, "domainsTitle": {"message": "<PERSON><PERSON><PERSON>", "description": "A category title describing the concept of web domains"}, "blockedDomains": {"message": "<PERSON><PERSON><PERSON> do<PERSON> v<PERSON>"}, "learnMoreAboutBlockedDomains": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> vairāk par liegtajiem domēna vārdiem"}, "excludedDomains": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "excludedDomainsDesc": {"message": "Bitwarden nevaicās saglab<PERSON>t piete<PERSON>s datus šiem do<PERSON>. Ir jā<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lai izmai<PERSON> i<PERSON>."}, "excludedDomainsDescAlt": {"message": "Bitwarden nevaicās saglab<PERSON>t piete<PERSON>s datus visiem š<PERSON> do<PERSON> kont<PERSON>, kuri ir pieteik<PERSON>. <PERSON>r j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lai i<PERSON> i<PERSON>."}, "blockedDomainsDesc": {"message": "Automātiskā aizpilde un citas saistītās iespējas šajās tīmekļvietnēs netiks piedāvātas. Ir j<PERSON>atsva<PERSON><PERSON> lapa, lai izma<PERSON> iedarbot<PERSON>."}, "autofillBlockedNoticeV2": {"message": "Automātiskā aiz<PERSON>lde šajā tīmekļvietnē ir liegta."}, "autofillBlockedNoticeGuidance": {"message": "To var main<PERSON>t i<PERSON>ī<PERSON>"}, "change": {"message": "<PERSON><PERSON><PERSON>"}, "changePassword": {"message": "<PERSON><PERSON><PERSON>", "description": "Change password button for browser at risk notification on login."}, "changeButtonTitle": {"message": "Mainīt paroli - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "atRiskPassword": {"message": "<PERSON><PERSON>"}, "atRiskPasswords": {"message": "<PERSON><PERSON> paroles"}, "atRiskPasswordDescSingleOrg": {"message": "$ORGANIZATION$ pieprasa mainīt vienu paroli, jo tā ir pak<PERSON><PERSON>a riskam.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}}, "atRiskPasswordsDescSingleOrgPlural": {"message": "$ORGANIZATION$ pieprasa mainīt $COUNT$ paroles, jo tās ir pak<PERSON><PERSON> riskam.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}, "count": {"content": "$2", "example": "2"}}}, "atRiskPasswordsDescMultiOrgPlural": {"message": "Apvienības pieprasa mainīt $COUNT$ paroles, jo tās ir pak<PERSON><PERSON> riskam.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "atRiskChangePrompt": {"message": "<PERSON><PERSON>s v<PERSON> parole ir pak<PERSON>a riskam. $ORGANIZATION$ pieprasīja, lai tā tiktu no<PERSON>ta.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}, "description": "Notification body when a login triggers an at-risk password change request and the change password domain is known."}, "atRiskNavigatePrompt": {"message": "$ORGANIZATION$ vēlas, lai šī parole tiktu no<PERSON>, jo tā ir pakļauta riskam. Jādodas uz sava konta iesta<PERSON>īju<PERSON>, lai nomain<PERSON>tu paroli.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}, "description": "Notification body when a login triggers an at-risk password change request and no change password domain is provided."}, "reviewAndChangeAtRiskPassword": {"message": "Pārskatīt un mainīt vienu riskam pak<PERSON>autu paroli"}, "reviewAndChangeAtRiskPasswordsPlural": {"message": "Pārskatīt un mainīt $COUNT$ riskam pakļautās paroles", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "changeAtRiskPasswordsFaster": {"message": "<PERSON><PERSON><PERSON> p<PERSON> paroles <PERSON>"}, "changeAtRiskPasswordsFasterDesc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> savi i<PERSON>, lai varētu veikli a<PERSON>pildīt paroles automātiski un izveidot jaunas"}, "reviewAtRiskLogins": {"message": "Pārskatīt <PERSON>am p<PERSON> pie<PERSON> vien<PERSON>us"}, "reviewAtRiskPasswords": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> paroles"}, "reviewAtRiskLoginsSlideDesc": {"message": "Apvienības paroles ir p<PERSON><PERSON><PERSON>, jo tās ir v<PERSON>, atk<PERSON>rtoti izman<PERSON> un/vai nopl<PERSON>.", "description": "Description of the review at-risk login slide on the at-risk password page carousel"}, "reviewAtRiskLoginSlideImgAltPeriod": {"message": "<PERSON><PERSON> p<PERSON> vienumu sarak<PERSON> attēloju<PERSON>."}, "generatePasswordSlideDesc": {"message": "<PERSON><PERSON> p<PERSON>o vienumu vietnē ar automātiskās a<PERSON>lde<PERSON> iz<PERSON>ēlni var ātri iz<PERSON>t stipru, neat<PERSON><PERSON><PERSON><PERSON><PERSON> paroli.", "description": "Description of the generate password slide on the at-risk password page carousel"}, "generatePasswordSlideImgAltPeriod": {"message": "Bitwarden automātiskās izvēln<PERSON> attēlojums, kur<PERSON> ir redzama izveidota parole."}, "updateInBitwarden": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "updateInBitwardenSlideDesc": {"message": "Bitwarden tad vaicās at<PERSON>t paroli paroļu pārva<PERSON>.", "description": "Description of the update in Bitwarden slide on the at-risk password page carousel"}, "updateInBitwardenSlideImgAltPeriod": {"message": "Bitwarden p<PERSON>, kas aicina lietotāju at<PERSON>uni<PERSON>t piete<PERSON> vienum<PERSON>, attēlojums."}, "turnOnAutofill": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> automā<PERSON>"}, "turnedOnAutofill": {"message": "Automātiskā aizpilde ieslēgta"}, "dismiss": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "websiteItemLabel": {"message": "Tīmekļvietne $number$ (URI)", "placeholders": {"number": {"content": "$1", "example": "3"}}}, "excludedDomainsInvalidDomain": {"message": "$DOMAIN$ nav derīgs domēns", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "blockedDomainsSavedSuccess": {"message": "<PERSON><PERSON><PERSON> do<PERSON>na vārda i<PERSON> sgla<PERSON>"}, "excludedDomainsSavedSuccess": {"message": "Saglabātas vērā neņemto domēna vārdu izmai<PERSON>as"}, "limitSendViews": {"message": "Ierobežot skatī<PERSON>"}, "limitSendViewsHint": {"message": "Neviens nevar a<PERSON>t šo <PERSON>ēc tam, kad ir sasniegts ierobežojums.", "description": "Displayed under the limit views field on Send"}, "limitSendViewsCount": {"message": "Atlikuši $ACCESSCOUNT$ skatījumi", "description": "Displayed under the limit views field on Send", "placeholders": {"accessCount": {"content": "$1", "example": "2"}}}, "send": {"message": "Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDetails": {"message": "Informācija par <PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendTypeText": {"message": "Teksts"}, "sendTypeTextToShare": {"message": "Kopīgoja<PERSON><PERSON> te<PERSON>"}, "sendTypeFile": {"message": "<PERSON><PERSON><PERSON>"}, "allSends": {"message": "Visi Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "maxAccessCountReached": {"message": "Sasniegts lielākais <PERSON>ļaujama<PERSON> piekļuves reižu skaits", "description": "This text will be displayed after a Send has been accessed the maximum amount of times."}, "hideTextByDefault": {"message": "<PERSON><PERSON><PERSON> no<PERSON><PERSON><PERSON> paslēpt tekstu"}, "expired": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> laiks"}, "passwordProtected": {"message": "Aizsargā<PERSON> ar paroli"}, "copyLink": {"message": "Ievietot saiti starpliktuvē"}, "copySendLink": {"message": "Ievietot Send saiti starpliktuvē", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "removePassword": {"message": "Noņemt paroli"}, "delete": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "removedPassword": {"message": "<PERSON><PERSON><PERSON>"}, "deletedSend": {"message": "Send izdzēsts", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLink": {"message": "Send saite", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "disabled": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "removePasswordConfirmation": {"message": "Vai tiešām no<PERSON> paroli?"}, "deleteSend": {"message": "Izdzēst Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendConfirmation": {"message": "Vai tiešām izdzēst šo Send?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendPermanentConfirmation": {"message": "Vai tiešām neatgriezeniski izdzēst šo Send?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editSend": {"message": "Labot Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deletionDate": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> datums"}, "deletionDateDescV2": {"message": "Send šajā datumā tiks neatgriezeniski izdzēsts.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "expirationDate": {"message": "<PERSON><PERSON><PERSON><PERSON> beigu datums"}, "oneDay": {"message": "1 diena"}, "days": {"message": "$DAYS$ dienas", "placeholders": {"days": {"content": "$1", "example": "2"}}}, "custom": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sendPasswordDescV3": {"message": "Pēc izv<PERSON> var pievienot paroli, lai saņēmēji varētu piekļūt šim <PERSON>.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createSend": {"message": "Jauns <PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "newPassword": {"message": "Jauna parole"}, "sendDisabled": {"message": "Send noņemts", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDisabledWarning": {"message": "Uzņēmuma nosacījumu dēļ ir iespējams izdzēst tikai esošu Send.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSend": {"message": "Send izveidots", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSendSuccessfully": {"message": "Send tika ve<PERSON> izveido<PERSON>.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHoursSingle": {"message": "Send būs pieejams nākamo stundu ik<PERSON>, kuram ir saite.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHours": {"message": "Send būs pieejams nākamās $HOURS$ stundas ikvienam, kuram ir saite.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"hours": {"content": "$1", "example": "5"}}}, "sendExpiresInDaysSingle": {"message": "Send būs pieejams nākamo dienu i<PERSON>, kuram ir saite.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInDays": {"message": "Send būs pieejams nākamās $DAYS$ dienas ikvienam, kuram ir saite.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"days": {"content": "$1", "example": "5"}}}, "sendLinkCopied": {"message": "Send saite ievietota starpliktuvē", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editedSend": {"message": "Send saglabāts", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogText": {"message": "Atvērt paplašinājumu atsevišķi?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogDesc": {"message": "<PERSON> izve<PERSON> da<PERSON>, ne<PERSON><PERSON><PERSON><PERSON> atvērt paplašinājumu atsevišķā logā.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLinuxChromiumFileWarning": {"message": "<PERSON> iz<PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir jāatver sānjoslā (ja iespējams) vai atsevišķā logā, klikšķinot uz šī paziņojuma."}, "sendFirefoxFileWarning": {"message": "<PERSON> i<PERSON><PERSON><PERSON>, ja tiek i<PERSON><PERSON>ts Firefox, pap<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir jāatver sānjoslā vai atsevišķā logā, klikšķinot uz <PERSON>ī paziņ<PERSON>ma."}, "sendSafariFileWarning": {"message": "<PERSON><PERSON><PERSON>, ja tiek <PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir jāatver jaunā logā, klikšķinot uz <PERSON><PERSON> pazi<PERSON>."}, "popOut": {"message": "Atvērt atsevišķi"}, "sendFileCalloutHeader": {"message": "<PERSON><PERSON>"}, "expirationDateIsInvalid": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> derīguma beigu datums nav derīgs."}, "deletionDateIsInvalid": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON> datums nav derīgs."}, "expirationDateAndTimeRequired": {"message": "<PERSON>r <PERSON><PERSON><PERSON><PERSON><PERSON> derīguma beigu datums un laiks."}, "deletionDateAndTimeRequired": {"message": "<PERSON>r <PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON> datums un laiks."}, "dateParsingError": {"message": "Atgadījusies kļūda d<PERSON>ša<PERSON> un derīguma beigu datumu saglabāšanā."}, "hideYourEmail": {"message": "Paslēpt e-pasta adresi no apskatītājiem."}, "passwordPrompt": {"message": "Galvenās <PERSON> p<PERSON><PERSON>"}, "passwordConfirmation": {"message": "Galvenās paroles a<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "passwordConfirmationDesc": {"message": "<PERSON><PERSON> ir a<PERSON>. <PERSON>, ir j<PERSON><PERSON><PERSON>da galvenā <PERSON>, lai aplie<PERSON>tu savu identitāti."}, "emailVerificationRequired": {"message": "Nepieciešama e-pasta adreses apliecināšana"}, "emailVerifiedV2": {"message": "E-pasta adrese ir aplie<PERSON>ta"}, "emailVerificationRequiredDesc": {"message": "Ir ne<PERSON>ms apliecināt savu e-pasta adresi, lai būtu iespējams izmantot šo iespēju. To var izdarīt tīmekļa glabātavā."}, "updatedMasterPassword": {"message": "Galvenā parole atjaunin<PERSON>ta"}, "updateMasterPassword": {"message": "At<PERSON><PERSON>n<PERSON><PERSON> galveno paroli"}, "updateMasterPasswordWarning": {"message": "Apvienības pārvaldnieks nesen nomainīja galveno paroli. Tā ir j<PERSON><PERSON>, lai varētu piekļūt glab<PERSON>tavai. Turpinot tiks izbeigta pašreizējā sesija un tiks pieprasīta atkārtota pieteikšanās. Esošās sesijas citās ierīcēs var turpināt darboties līdz vienai stundai."}, "updateWeakMasterPasswordWarning": {"message": "Galvenā parole neatbilst vienam vai vairākiem apvienības nosacījumiem. Ir jāat<PERSON><PERSON>na galvenā parole, lai varētu piekļūt glab<PERSON>tavai. Turpinot notiks atteikšanās no pašreizējās sesi<PERSON>, un būs nepieciešams pieteikties no jauna. Citās ierīcēs esošās sesi<PERSON> var turpināt darboties līdz vienai stundai."}, "tdeDisabledMasterPasswordRequired": {"message": "Tava apvienība ir atspējojusi uzticamo ierīč<PERSON>. Lū<PERSON>s iestatīt galveno paroli, lai piekļ<PERSON>tu savai glab<PERSON>i."}, "resetPasswordPolicyAutoEnroll": {"message": "Automātiska ievieto<PERSON>"}, "resetPasswordAutoEnrollInviteWarning": {"message": "<PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON> ir uz<PERSON><PERSON><PERSON>, kas automātiski ievieto lietotājus paroles atiestatīšanas sarakstā. Tas ļauj apvienības pārvaldniekiem mainīt lietotāju galveno paroli."}, "selectFolder": {"message": "Izvēlēties mapi..."}, "noFoldersFound": {"message": "Nav atrasta neviena mape", "description": "Used as a message within the notification bar when no folders are found"}, "orgPermissionsUpdatedMustSetPassword": {"message": "Apvien<PERSON><PERSON> atļaujas tika atjaunin<PERSON>tas, un tās pieprasa iestatīt galveno paroli.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "orgRequiresYouToSetPassword": {"message": "Apvienība pieprasa iestatīt galveno paroli.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "cardMetrics": {"message": "no $TOTAL$", "placeholders": {"total": {"content": "$1", "example": "5"}}}, "verificationRequired": {"message": "Nepieciešams apliecinājums", "description": "Default title for the user verification dialog."}, "hours": {"message": "Stundas"}, "minutes": {"message": "<PERSON><PERSON><PERSON>"}, "vaultTimeoutPolicyAffectingOptions": {"message": "Noildzes iespējām tika piemērotas uzņēmējdarbības nosacījumu prasības"}, "vaultTimeoutPolicyInEffect": {"message": "Apvienības nosacījumi ietekmē glabātavas noildzi. Lielākā atļautā glabātavas noildze ir $HOURS$ stunda(s) un $MINUTES$ minūte(s)", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyInEffect1": {"message": "Ne vairāk kā $HOURS$ stunda(s) un $MINUTES$ minūte(s).", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyMaximumError": {"message": "Noildze pārsniedz apvienības iestatīto ierobežojumu: ne vairāk kā $HOURS$ stunda(s) un $MINUTES$ minūte(s)", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyWithActionInEffect": {"message": "Apvienības nosacījumi ietekmē glabātavas noildzi. Lie<PERSON>āk<PERSON> atļautā glabātavas noildze ir $HOURS$ stunda(s) un $MINUTES$ minūte(s). Kā glabātavas noildzes darbība ir uzstādīta $ACTION$.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}, "action": {"content": "$3", "example": "Lock"}}}, "vaultTimeoutActionPolicyInEffect": {"message": "Apvienības nosacījumos kā glabātavas noildzes darbība ir uzstādīta $ACTION$.", "placeholders": {"action": {"content": "$1", "example": "Lock"}}}, "vaultTimeoutTooLarge": {"message": "Glabātavas noildze pārsniedz apvienības uzstādītos ierobežojumus."}, "vaultExportDisabled": {"message": "Glabātavas iz<PERSON> ir atspējota"}, "personalVaultExportPolicyInEffect": {"message": "Viens vai vairāki apvienības nosacījumi neļauj izgūt privātās glab<PERSON>ta<PERSON> saturu."}, "copyCustomFieldNameInvalidElement": {"message": "Nav iespējams noteikt derīgu veidlapas daļu. Var mēģināt pārbaudīt HTML."}, "copyCustomFieldNameNotUnique": {"message": "Nav atrasts neviens neatkārtojams identifikators"}, "removeMasterPasswordForOrganizationUserKeyConnector": {"message": "Galvenā parole vairs nav nepieciešama turpmāk minētās apvienības dalībniekiem. Lūgums saskaņot zemāk esošo domēnu ar savas apvienības pārvaldītāju."}, "organizationName": {"message": "Apvienības <PERSON>"}, "keyConnectorDomain": {"message": "Key Connector domēns"}, "leaveOrganization": {"message": "Pamest apvienību"}, "removeMasterPassword": {"message": "Noņemt galveno paroli"}, "removedMasterPassword": {"message": "Galvenā parole noņemta."}, "leaveOrganizationConfirmation": {"message": "Vai tiešām pamest šo ap<PERSON>bu?"}, "leftOrganization": {"message": "Apvienī<PERSON> ir pame<PERSON>."}, "toggleCharacterCount": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>"}, "sessionTimeout": {"message": "<PERSON><PERSON><PERSON><PERSON> i<PERSON> no<PERSON>. Lūgums mēģināt pieteikties vēlreiz."}, "exportingPersonalVaultTitle": {"message": "Izgūst person<PERSON><PERSON>"}, "exportingIndividualVaultDescription": {"message": "Tiks izgūti tikai atsevišķi glabātavas vienumi, kas ir saistīti ar $EMAIL$. Apvienības glabātavas vienumi netiks iekļauti. Tiks izgūta tikai glabātavas vienumu informācija, un saistītie pielikumi netiks iekļauti.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingIndividualVaultWithAttachmentsDescription": {"message": "Tiks izdoti tikai atsevišķi glab<PERSON><PERSON><PERSON> vienumi, taj<PERSON> s<PERSON>, kas ir saistīti ar $EMAIL$. Apvienības glabātavas vienumi netiks iekļauti", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingOrganizationVaultTitle": {"message": "Izgūst apvienības gla<PERSON>tavu"}, "exportingOrganizationVaultDesc": {"message": "Tiks izgūta tikai apvienības gla<PERSON>, kas ir saistīta ar $ORGANIZATION$. Atsevišķu glabātavu vai citu apvienību vienumi netiks iekļauti.", "placeholders": {"organization": {"content": "$1", "example": "ACME Moving Co."}}}, "error": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "decryptionError": {"message": "Atšifrēšanas k<PERSON>ū<PERSON>"}, "couldNotDecryptVaultItemsBelow": {"message": "Bitwarden nevarēja atšifrēt zemāk uzskaitītos glabātavas vienumus."}, "contactCSToAvoidDataLossPart1": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar k<PERSON>u at<PERSON>,", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "contactCSToAvoidDataLossPart2": {"message": "lai izvairītos no papildu datu zaudējumiem.", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "generateUsername": {"message": "Izveidot lietotājvārdu"}, "generateEmail": {"message": "Izveidot e-pasta adresi"}, "spinboxBoundariesHint": {"message": "Vērtībai jābūt starp $MIN$ un $MAX$.", "description": "Explains spin box minimum and maximum values to the user", "placeholders": {"min": {"content": "$1", "example": "8"}, "max": {"content": "$2", "example": "128"}}}, "passwordLengthRecommendationHint": {"message": " Jāizmanto $RECOMMENDED$ vai vairāk r<PERSON>, la izveidotu spēcīgu paroli.", "description": "Appended to `spinboxBoundariesHint` to recommend a length to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "14"}}}, "passphraseNumWordsRecommendationHint": {"message": " Jāizmanto $RECOMMENDED$ vai vairāk vārdu, lai aizveidotu spēcīgu paroles vārdkopu.", "description": "Appended to `spinboxBoundariesHint` to recommend a number of words to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "6"}}}, "plusAddressedEmail": {"message": "E-pasta adrese ar plusu", "description": "Username generator option that appends a random sub-address to the username. For example: <EMAIL>"}, "plusAddressedEmailDesc": {"message": "Izmantot e-pasta pakalpojuma nodrošinātāja apa<PERSON>ša<PERSON>sē<PERSON> spē<PERSON>."}, "catchallEmail": {"message": "Visu tveroša e-pasta adrese"}, "catchallEmailDesc": {"message": "Izmantot uz<PERSON>ād<PERSON>to do<PERSON>na visu tverošo i<PERSON>."}, "random": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "randomWord": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "websiteName": {"message": "Tīmekļvietnes nosaukums"}, "service": {"message": "Pakalpojums"}, "forwardedEmail": {"message": "Pārvirzīto e-pastu aizstājvārds"}, "forwardedEmailDesc": {"message": "Izveidot e-pasta aizstājadresi ar ārēju pār<PERSON><PERSON><PERSON><PERSON> paka<PERSON>."}, "forwarderDomainName": {"message": "E-pasta domēns", "description": "Labels the domain name email forwarder service option"}, "forwarderDomainNameHint": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kuru atbalsta atlasītais pakalpoju<PERSON>", "description": "Guidance provided for email forwarding services that support multiple email domains."}, "forwarderError": {"message": "$SERVICENAME$ kļūda: $ERRORMESSAGE$", "description": "Reports an error returned by a forwarding service to the user.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Invalid characters in domain name."}}}, "forwarderGeneratedBy": {"message": "Izveidoja <PERSON>.", "description": "Displayed with the address on the forwarding service's configuration screen."}, "forwarderGeneratedByWithWebsite": {"message": "Tīmekļvietne: $WEBSITE$. Izveidoja Bitwarden.", "description": "Displayed with the address on the forwarding service's configuration screen.", "placeholders": {"WEBSITE": {"content": "$1", "example": "www.example.com"}}}, "forwaderInvalidToken": {"message": "Nederīga $SERVICENAME$ API pilnvara", "description": "Displayed when the user's API token is empty or rejected by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidTokenWithMessage": {"message": "Nederīga $SERVICENAME$ API pilnvara: $ERRORMESSAGE$", "description": "Displayed when the user's API token is rejected by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwaderInvalidOperation": {"message": "$SERVICENAME$ atteica pieprasījumu. <PERSON><PERSON><PERSON><PERSON> sazin<PERSON> ar savu pakalpojma nod<PERSON>, lai ieg<PERSON><PERSON> pal<PERSON>.", "description": "Displayed when the user is forbidden from using the API by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidOperationWithMessage": {"message": "$SERVICENAME$ atteica pieprasījumu: $ERRORMESSAGE$", "description": "Displayed when the user is forbidden from using the API by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwarderNoAccountId": {"message": "Neizdevās iegūt $SERVICENAME$ aizsegta e-pasta konta Id.", "description": "Displayed when the forwarding service fails to return an account ID.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoDomain": {"message": "Nederīgs $SERVICENAME$ domēna vārds.", "description": "Displayed when the domain is empty or domain authorization failed at the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoUrl": {"message": "Nederīgs $SERVICENAME$ URL.", "description": "Displayed when the url of the forwarding service wasn't supplied.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownError": {"message": "Atgadījās nezināma $SERVICENAME$ kļūda.", "description": "Displayed when the forwarding service failed due to an unknown error.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownForwarder": {"message": "Nezināms pārsūtītājs: '$SERVICENAME$'.", "description": "Displayed when the forwarding service is not supported.", "placeholders": {"servicename": {"content": "$1", "example": "JustTrust.us"}}}, "hostname": {"message": "Resurs<PERSON><PERSON><PERSON>", "description": "Part of a URL."}, "apiAccessToken": {"message": "API piekļuves pilnvara"}, "apiKey": {"message": "API atslēga"}, "ssoKeyConnectorError": {"message": "Key Connector kļūda: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka <PERSON> Connector ir pieejams un darbojas pareizi."}, "premiumSubcriptionRequired": {"message": "Nepieciešams Premium abonements"}, "organizationIsDisabled": {"message": "Apvienība ir at<PERSON>."}, "disabledOrganizationFilterError": {"message": "Atspējotu apvienību vienumiem nevar piekļūt. Jāsazinās ar apvienības <PERSON>, lai iegū<PERSON> palīdzī<PERSON>."}, "loggingInTo": {"message": "Piesakās $DOMAIN$", "placeholders": {"domain": {"content": "$1", "example": "example.com"}}}, "serverVersion": {"message": "Servera versija"}, "selfHostedServer": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "thirdParty": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> puses"}, "thirdPartyServerMessage": {"message": "Savienots ar trešās puses izvietotu serveri $SERVERNAME$. Lūgums pārbaudīt nepilnību esamību oficiālajā serverī vai ziņot par tām trešās puses servera uzturētājiem.", "placeholders": {"servername": {"content": "$1", "example": "ThirdPartyServerName"}}}, "lastSeenOn": {"message": "pēd<PERSON><PERSON><PERSON><PERSON> manīts $DATE$", "placeholders": {"date": {"content": "$1", "example": "Jun 15, 2015"}}}, "loginWithMasterPassword": {"message": "Pieteikties ar galveno paroli"}, "newAroundHere": {"message": "<PERSON><PERSON><PERSON>?"}, "rememberEmail": {"message": "Atcerēties e-pasta adresi"}, "loginWithDevice": {"message": "Pieteikties ar <PERSON>"}, "fingerprintPhraseHeader": {"message": "Atpazī<PERSON><PERSON> vārdkopa"}, "fingerprintMatchInfo": {"message": "Lū<PERSON><PERSON>, ka glabātava ir atslēgta un atpazīša<PERSON> vārd<PERSON>pa ir tāda pati arī citā ierīcē."}, "resendNotification": {"message": "Atkārtoti nosūtīt pazi<PERSON>"}, "viewAllLogInOptions": {"message": "Skatīt visas pieteik<PERSON>"}, "notificationSentDevice": {"message": "<PERSON><PERSON> i<PERSON><PERSON>ci ir nosūt<PERSON>ts paziņoju<PERSON>."}, "notificationSentDevicePart1": {"message": "Bitwarden jāatslēdz savā ierīcē vai"}, "notificationSentDeviceAnchor": {"message": "tīmekļa lietotnē"}, "notificationSentDevicePart2": {"message": "Pirms apstiprināšanas j<PERSON>, ka pirkstu nospieduma vārdkopa atbilst zemāk esošajai."}, "aNotificationWasSentToYourDevice": {"message": "Uz ierīci tika nosūtīts paziņojums"}, "youWillBeNotifiedOnceTheRequestIsApproved": {"message": "<PERSON><PERSON><PERSON>, tik<PERSON><PERSON><PERSON><PERSON>pra<PERSON> būs a<PERSON>"}, "needAnotherOptionV1": {"message": "Nepieciešama cita iespēja?"}, "loginInitiated": {"message": "<PERSON>zsā<PERSON><PERSON>"}, "logInRequestSent": {"message": "Pieprasīju<PERSON> nosūt<PERSON>ts"}, "exposedMasterPassword": {"message": "Noplūdusi galvenā parole"}, "exposedMasterPasswordDesc": {"message": "Parole atrasta datu noplūdē. <PERSON><PERSON><PERSON><PERSON><PERSON>, lai aizsargātu savu kontu. Vai tiešām izmantot noplūdušu paroli?"}, "weakAndExposedMasterPassword": {"message": "Vāja un noplūdusi galvenā parole"}, "weakAndBreachedMasterPasswordDesc": {"message": "Noteikta vāja parole, un tā ir atrasta datu noplūdē. Jāizmanto spēcīga un neatkārtojama parole, lai aizsargātu savu kontu. Vai tiešām izmantot šo paroli?"}, "checkForBreaches": {"message": "Me<PERSON><PERSON><PERSON><PERSON> šo paroli zināmās datu noplūdēs"}, "important": {"message": "Svarīgi:"}, "masterPasswordHint": {"message": "Galvenā parole nevar tikt at<PERSON>, ja tā ir a<PERSON>!"}, "characterMinimum": {"message": "Vismaz $LENGTH$ rakstzīmes", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "autofillPageLoadPolicyActivated": {"message": "Tavas apvienības nosacījumos ir ieslēgta automātiskā aizpilde lapas ielādes brīdī."}, "howToAutofill": {"message": "<PERSON>ā automātis<PERSON>"}, "autofillSelectInfoWithCommand": {"message": "J<PERSON><PERSON><PERSON><PERSON><PERSON> skata vien<PERSON>, j<PERSON><PERSON><PERSON><PERSON> $COMMAND$ vai iestatījumos jāizpēta citas iespējas.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillSelectInfoWithoutCommand": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> š<PERSON> skata vienums vai iestatījumos jāizpēta citas iespējas."}, "gotIt": {"message": "<PERSON><PERSON><PERSON>"}, "autofillSettings": {"message": "Automātis<PERSON><PERSON><PERSON> a<PERSON>"}, "autofillKeyboardShortcutSectionTitle": {"message": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "autofillKeyboardShortcutUpdateLabel": {"message": "<PERSON><PERSON><PERSON>"}, "autofillKeyboardManagerShortcutsLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "autofillShortcut": {"message": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "autofillLoginShortcutNotSet": {"message": "Automātis<PERSON><PERSON><PERSON> a<PERSON> ī<PERSON><PERSON><PERSON><PERSON>s vienumiem nav uzstādīts. To var izdarīt pārlūka iestatījumos."}, "autofillLoginShortcutText": {"message": "Automātiskās aizpildes īs<PERSON><PERSON><PERSON>ās vienumiem ir: $COMMAND$. Visus īsinājumtaustiņus var pārvaldīt pārlūka iestatījumos.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillShortcutTextSafari": {"message": "Automātiskās aizpildes noklusējuma īsceļš: $COMMAND$.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "opensInANewWindow": {"message": "Atver jaunā logā"}, "rememberThisDeviceToMakeFutureLoginsSeamless": {"message": "Atcerēties š<PERSON>, lai nākotnes pieteikšanos padarītu plūden<PERSON>ku"}, "deviceApprovalRequired": {"message": "Nepieciešams ierīces apstiprinājums. Zemāk jāatlasa apstiprinājuma iespēja:"}, "deviceApprovalRequiredV2": {"message": "Nepieciešama ierī<PERSON> a<PERSON>"}, "selectAnApprovalOptionBelow": {"message": "Zemāk jāatlasa apstiprināšnas iespēja"}, "rememberThisDevice": {"message": "Atcerēties šo <PERSON>"}, "uncheckIfPublicDevice": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ja tiek izmantota publis<PERSON> i<PERSON>"}, "approveFromYourOtherDevice": {"message": "Jāapstiprina citā savā ierīcē"}, "requestAdminApproval": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>"}, "ssoIdentifierRequired": {"message": "<PERSON><PERSON> <PERSON>ms apvienības SSO identifikators."}, "creatingAccountOn": {"message": "Tiek veidots konts"}, "checkYourEmail": {"message": "Jāpārbauda e-pasts"}, "followTheLinkInTheEmailSentTo": {"message": "<PERSON><PERSON><PERSON><PERSON> saite, kas tika nosūtīta uz e-pasta adresi"}, "andContinueCreatingYourAccount": {"message": "un jāturpina sava konta iz<PERSON>ide."}, "noEmail": {"message": "Nav e-pasta?"}, "goBack": {"message": "Atgriezties"}, "toEditYourEmailAddress": {"message": ", lai labotu savu e-pasta adresi."}, "eu": {"message": "ES", "description": "European Union"}, "accessDenied": {"message": "Piekļuve liegta. <PERSON>v <PERSON><PERSON><PERSON><PERSON> atļauju, lai skat<PERSON>tu <PERSON>o <PERSON>."}, "general": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "display": {"message": "Attēlojums"}, "accountSuccessfullyCreated": {"message": "<PERSON><PERSON> ir veiksmīgi izveido<PERSON>."}, "adminApprovalRequested": {"message": "Pie<PERSON><PERSON><PERSON><PERSON> pārvaldītāja a<PERSON>tiprinājums"}, "adminApprovalRequestSentToAdmins": {"message": "Pieprasījums tika nosūtīts pārvaldītājam."}, "troubleLoggingIn": {"message": "Neizdodas pieteikties?"}, "loginApproved": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "userEmailMissing": {"message": "Trūkst lietotāja e-pasta adreses"}, "activeUserEmailNotFoundLoggingYouOut": {"message": "Aktīva lietotāja e-pasta adrese netika atrasta. Notiek atteikšanās."}, "deviceTrusted": {"message": "<PERSON><PERSON><PERSON><PERSON> ir <PERSON>"}, "trustOrganization": {"message": "Uzticēties apvienībai"}, "trust": {"message": "Uzticēties"}, "doNotTrust": {"message": "Neuzticēties"}, "organizationNotTrusted": {"message": "Apvienība nav uzticama"}, "emergencyAccessTrustWarning": {"message": "<PERSON> sava konta <PERSON>, j<PERSON><PERSON><PERSON><PERSON><PERSON> tikai tad, ja šim lietot<PERSON>jam ir nodrošināta ārkārtas piekļuve un tā pirkstu nospiedums atbilsta tam, kas ir attēlots tā kontā"}, "orgTrustWarning": {"message": "<PERSON> sava konta <PERSON>, j<PERSON><PERSON><PERSON> tikai tad, ja esi <PERSON><PERSON> apvien<PERSON>, ir iesp<PERSON><PERSON>ta konta atkope un zemāk attēlotais pirkstu nospiedums atbilst apvienības pirkstu nospiedumam."}, "orgTrustWarning1": {"message": "Šai apvienībai ir uzņēmējdarbības pamatnostādne, kas <PERSON> iekļ<PERSON> konta atkopē. Iekļaušana ļaus apvienības pārvaldītājiem nomainīt Tavu paroli. <PERSON><PERSON><PERSON> tikai tad, ja zini šo apvienību un zemāk attēlotā pirkstu nospieduma vārdkopa atbilst apvienības pirkstu nospiedumam!"}, "trustUser": {"message": "Uzticēties lietotājam"}, "sendsTitleNoItems": {"message": "<PERSON><PERSON><PERSON><PERSON> veidā nosūti jūt<PERSON>gu <PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendsBodyNoItems": {"message": "<PERSON><PERSON><PERSON><PERSON> veidā kopīgo datnes un datus ar ikvienu jebkurā platformā! Tava informācija paliks pilnībā <PERSON>, vien<PERSON><PERSON> ierobežojot risk<PERSON>u.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "inputRequired": {"message": "Jāievada vērtība."}, "required": {"message": "nepieciešams"}, "search": {"message": "Meklēt"}, "inputMinLength": {"message": "Ievadītajai vērtībai ir jābūt vismaz $COUNT$ rakstzīmes garai.", "placeholders": {"count": {"content": "$1", "example": "8"}}}, "inputMaxLength": {"message": "Ievadītās vērtības garums nedrīkst pārsniegt $COUNT$ rakstzīmes.", "placeholders": {"count": {"content": "$1", "example": "20"}}}, "inputForbiddenCharacters": {"message": "<PERSON><PERSON><PERSON> r<PERSON> nav atļautas: $CHARACTERS$", "placeholders": {"characters": {"content": "$1", "example": "@, #, $, %"}}}, "inputMinValue": {"message": "Ievadītajai vērtībai jābūt vismaz $MIN$.", "placeholders": {"min": {"content": "$1", "example": "8"}}}, "inputMaxValue": {"message": "Ievadītā vērtība nedrīkst pārsniegt $MAX$.", "placeholders": {"max": {"content": "$1", "example": "100"}}}, "multipleInputEmails": {"message": "1 vai vairākas e-pasta adreses nav derīgas"}, "inputTrimValidator": {"message": "<PERSON>eva<PERSON><PERSON><PERSON><PERSON> vērtība nevar sastāvēt tikai no atstarpēm.", "description": "Notification to inform the user that a form's input can't contain only whitespace."}, "inputEmail": {"message": "Ievadītā vērtība nav e-pasta adrese."}, "fieldsNeedAttention": {"message": "$COUNT$ augstāk eso<PERSON>m(iem) laukam(iem) ir j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "placeholders": {"count": {"content": "$1", "example": "4"}}}, "singleFieldNeedsAttention": {"message": "1 laukam jā<PERSON><PERSON><PERSON><PERSON><PERSON>."}, "multipleFieldsNeedAttention": {"message": "$COUNT$ laukiem ir j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "selectPlaceholder": {"message": "-- <PERSON><PERSON><PERSON> --"}, "multiSelectPlaceholder": {"message": "-- <PERSON><PERSON><PERSON><PERSON><PERSON>, lai atlasītu --"}, "multiSelectLoading": {"message": "<PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON>..."}, "multiSelectNotFound": {"message": "<PERSON><PERSON> at<PERSON> vienumi"}, "multiSelectClearAll": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> visu"}, "plusNMore": {"message": "+ vēl $QUANTITY$", "placeholders": {"quantity": {"content": "$1", "example": "5"}}}, "submenu": {"message": "Apakšizvēlne"}, "toggleCollapse": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Toggling an expand/collapse state."}, "aliasDomain": {"message": "Aizst<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "passwordRepromptDisabledAutofillOnPageLoad": {"message": "Vienumus ar galvenās paroles p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nevar automātiski aizpildīt lapas ielādes brīdī. Automātiskā aizpilde lapas ielādes brīdī ir izslēgta.", "description": "Toast message for describing that master password re-prompt cannot be autofilled on page load."}, "autofillOnPageLoadSetToDefault": {"message": "Automātisk<PERSON> a<PERSON>pilde lapas ielādes brīdī iestatīta izmantot noklusējuma iestatījumu.", "description": "Toast message for informing the user that autofill on page load has been set to the default setting."}, "turnOffMasterPasswordPromptToEditField": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> galvenās paroles <PERSON><PERSON><PERSON>, lai labotu šo lauku", "description": "Message appearing below the autofill on load message when master password reprompt is set for a vault item."}, "toggleSideNavigation": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON> p<PERSON> j<PERSON>"}, "skipToContent": {"message": "P<PERSON><PERSON><PERSON> uz saturu"}, "bitwardenOverlayButton": {"message": "Bitwarden automātiskās aizpildes izvēlnes poga", "description": "Page title for the iframe containing the overlay button"}, "toggleBitwardenVaultOverlay": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> automātiskās aizpi<PERSON> i<PERSON>i", "description": "Screen reader and tool tip label for the overlay button"}, "bitwardenVault": {"message": "Bitwarden automātiskās aizpildes izvēlne", "description": "Page title in overlay"}, "unlockYourAccountToViewMatchingLogins": {"message": "Jāatsl<PERSON><PERSON><PERSON> savs kont<PERSON>, lai aps<PERSON><PERSON><PERSON> atbilstoš<PERSON> pie<PERSON>ik<PERSON> vienumus", "description": "Text to display in overlay when the account is locked."}, "unlockYourAccountToViewAutofillSuggestions": {"message": "Jāatslēdz savs kont<PERSON>, lai apskatītu automātiskā<PERSON> a<PERSON>lde<PERSON> iete<PERSON>us", "description": "Text to display in overlay when the account is locked."}, "unlockAccount": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> kontu", "description": "Button text to display in overlay when the account is locked."}, "unlockAccountAria": {"message": "Atslēgt savu kontu, tiks atv<PERSON><PERSON> jaunā logā", "description": "Screen reader text (aria-label) for unlock account button in overlay"}, "totpCodeAria": {"message": "Laikā balstīts vienreizējas izmantošanas paroles apliecinājuma kods", "description": "Aria label for the totp code displayed in the inline menu for autofill"}, "totpSecondsSpanAria": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>, pirms beigsies pašreizējā TOTP derīgums", "description": "Aria label for the totp seconds displayed in the inline menu for autofill"}, "fillCredentialsFor": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON>", "description": "Screen reader text for when overlay item is in focused"}, "partialUsername": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Screen reader text for when a login item is focused where a partial username is displayed. SR will announce this phrase before reading the text of the partial username"}, "noItemsToShow": {"message": "Nav vienumu, ko parād<PERSON>t", "description": "Text to show in overlay if there are no matching items"}, "newItem": {"message": "Jauns vienums", "description": "Button text to display in overlay when there are no matching items"}, "addNewVaultItem": {"message": "<PERSON><PERSON><PERSON> j<PERSON> g<PERSON> vienumu", "description": "Screen reader text (aria-label) for new item button in overlay"}, "newLogin": {"message": "<PERSON><PERSON>ns piete<PERSON> vienums", "description": "Button text to display within inline menu when there are no matching items on a login field"}, "addNewLoginItemAria": {"message": "<PERSON><PERSON><PERSON> jaunu g<PERSON> pie<PERSON> vien<PERSON>, tiks atvē<PERSON> jaunā logā", "description": "Screen reader text (aria-label) for new login button within inline menu"}, "newCard": {"message": "<PERSON><PERSON>na karte", "description": "Button text to display within inline menu when there are no matching items on a credit card field"}, "addNewCardItemAria": {"message": "<PERSON><PERSON><PERSON> jaunu g<PERSON> kartes vien<PERSON>, tiks atv<PERSON><PERSON> jaun<PERSON> logā", "description": "Screen reader text (aria-label) for new card button within inline menu"}, "newIdentity": {"message": "Jauna identitāte", "description": "Button text to display within inline menu when there are no matching items on an identity field"}, "addNewIdentityItemAria": {"message": "<PERSON><PERSON><PERSON> jaunu gla<PERSON>vas identitātes vienumu, tiks atvērts jaunā logā", "description": "Screen reader text (aria-label) for new identity button within inline menu"}, "bitwardenOverlayMenuAvailable": {"message": "Ir pieejama Bitwarden automātiskās aizpildes izvēlne. Jānospiež poga ar bultu uz leju, lai atlasī<PERSON>.", "description": "Screen reader text for announcing when the overlay opens on the page"}, "turnOn": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ignore": {"message": "Neņemt vērā"}, "importData": {"message": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>", "description": "Used for the header of the import dialog, the import button and within the file-password-prompt"}, "importError": {"message": "Ievietošanas k<PERSON>"}, "importErrorDesc": {"message": "<PERSON>r ne<PERSON>lnī<PERSON> ievietoja<PERSON> da<PERSON>. Lūgums novērst zemāk uzskaitītās kļūdas avota datnē un mēģināt vēlreiz."}, "resolveTheErrorsBelowAndTryAgain": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> norād<PERSON>t<PERSON><PERSON> k<PERSON>ū<PERSON> un jāmēģina vēlreiz."}, "description": {"message": "<PERSON><PERSON><PERSON>"}, "importSuccess": {"message": "<PERSON><PERSON> ve<PERSON> i<PERSON>i"}, "importSuccessNumberOfItems": {"message": "Kopumā tika ievietoti $AMOUNT$ vienumi.", "placeholders": {"amount": {"content": "$1", "example": "2"}}}, "tryAgain": {"message": "Jāmēģina vēlreiz"}, "verificationRequiredForActionSetPinToContinue": {"message": "Šai darbībai ir nepieciešama apliecinājums. Jāiestata PIN, lai turpin<PERSON>."}, "setPin": {"message": "Iestatīt PIN"}, "verifyWithBiometrics": {"message": "Apliecināt ar biometriju"}, "awaitingConfirmation": {"message": "<PERSON><PERSON><PERSON>"}, "couldNotCompleteBiometrics": {"message": "Nevarēja pabeigt biometriju."}, "needADifferentMethod": {"message": "Nepieciešams cits veids?"}, "useMasterPassword": {"message": "Izmantot galveno paroli"}, "usePin": {"message": "Izmantot PIN"}, "useBiometrics": {"message": "Izmantot biometriju"}, "enterVerificationCodeSentToEmail": {"message": "J<PERSON>ieva<PERSON> ap<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>, kas tika nosūtīts e-pastā."}, "resendCode": {"message": "Atkārtoti nosūtīt kodu"}, "total": {"message": "Kopā"}, "importWarning": {"message": "Tiek ievietoti dati apvienībā $ORGANIZATION$. Tie var tikt kopīgoti ar citiem apvienības dalībniekiem. Vai turpināt?", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "duoHealthCheckResultsInNullAuthUrlError": {"message": "Kļ<PERSON>da savienojuma izveidošanā ar Duo pakalpojumu. Jāizmanto cits divpakāpju pieteikšanās veids vai jāvēršas pie Duo pēc palīdzības."}, "duoRequiredForAccount": {"message": "Kontam ir nepieciešama Duo divpakāpju pieteikšanās."}, "popoutExtension": {"message": "<PERSON><PERSON><PERSON><PERSON> papla<PERSON>jumu atsevišķi"}, "launchDuo": {"message": "Palaist DUO"}, "importFormatError": {"message": "Dati nav pareizi formatēti. <PERSON><PERSON><PERSON><PERSON> pārbaud<PERSON>t ieviet<PERSON>ša<PERSON> datni un mēģināt vēlreiz."}, "importNothingError": {"message": "<PERSON>ekas netika ievietots."}, "importEncKeyError": {"message": "<PERSON><PERSON><PERSON><PERSON> izguves datnes atšifrēšanā. Izmantotā atslēga neatbilst tai, kas tika izmantota satura izgūšanai."}, "invalidFilePassword": {"message": "<PERSON><PERSON><PERSON><PERSON> datnes parole, l<PERSON><PERSON><PERSON> to paroli, kas tika ievad<PERSON>ta izg<PERSON>šanas datnes izveidošanas brīdī."}, "destination": {"message": "Galamērķis"}, "learnAboutImportOptions": {"message": "Uzzināt par ievietošanas iespējām"}, "selectImportFolder": {"message": "<PERSON>īt mapi"}, "selectImportCollection": {"message": "<PERSON><PERSON><PERSON>"}, "importTargetHint": {"message": "<PERSON><PERSON> iespēja j<PERSON>, ja ir vēl<PERSON><PERSON><PERSON><PERSON><PERSON> ievietotās datnes saturu pārvietot uz $DESTINATION$", "description": "Located as a hint under the import target. Will be appended by either folder or collection, depending if the user is importing into an individual or an organizational vault.", "placeholders": {"destination": {"content": "$1", "example": "folder or collection"}}}, "importUnassignedItemsError": {"message": "<PERSON><PERSON>ne satur nepiešķirtus vienumus."}, "selectFormat": {"message": "<PERSON><PERSON><PERSON> iev<PERSON> dat<PERSON> ve<PERSON>lu"}, "selectImportFile": {"message": "<PERSON><PERSON><PERSON> da<PERSON>"}, "chooseFile": {"message": "Izvē<PERSON>ē<PERSON> datni"}, "noFileChosen": {"message": "Nav izv<PERSON><PERSON><PERSON>ta neviena datne"}, "orCopyPasteFileContents": {"message": "vai ievietot starpliktuvē un ielīmēt ievietošanas datnes saturu"}, "instructionsFor": {"message": "Norādījumi $NAME$", "description": "The title for the import tool instructions.", "placeholders": {"name": {"content": "$1", "example": "LastPass (csv)"}}}, "confirmVaultImport": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>vas satura ieviet<PERSON>"}, "confirmVaultImportDesc": {"message": "<PERSON><PERSON> datne ir aizsargāta ar paroli. <PERSON><PERSON><PERSON><PERSON> ievadīt datnes paroli, lai ievietotu datus."}, "confirmFilePassword": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datnes paroli"}, "exportSuccess": {"message": "G<PERSON><PERSON><PERSON><PERSON><PERSON> saturs izg<PERSON>ts"}, "typePasskey": {"message": "Piekļuves atslēga"}, "accessing": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "loggedInExclamation": {"message": "Pieteicies."}, "passkeyNotCopied": {"message": "Piekļuves atslēga netiks ievietota starpliktuvē"}, "passkeyNotCopiedAlert": {"message": "Piekļuves atslēga netiks ievietota klonētajā vienumā. Vai turpināt šī vienuma klonēšanu?"}, "passkeyFeatureIsNotImplementedForAccountsWithoutMasterPassword": {"message": "<PERSON><PERSON><PERSON>, kur<PERSON> tika uz<PERSON>, pie<PERSON><PERSON> a<PERSON>. <PERSON><PERSON> iespēja vēl nav īstenota kontiem, kuriem nav galvenās paroles."}, "logInWithPasskeyQuestion": {"message": "Pieteikties ar piekļuves atslēgu?"}, "passkeyAlreadyExists": {"message": "Šai lietotnei jau pastāv piekļuves atslēga."}, "noPasskeysFoundForThisApplication": {"message": "Šai lietotnei netika atrastas piekļuves atslēgas."}, "noMatchingPasskeyLogin": {"message": "Nav šai vietnei atbilstoša pieteikša<PERSON>ā<PERSON> vienuma."}, "noMatchingLoginsForSite": {"message": "Šai vietnei nav atbilstošu pieteikšanās vietnumu"}, "searchSavePasskeyNewLogin": {"message": "Meklēt vai saglabāt piekļuves atslēgu kā jaunu pieteik<PERSON>n<PERSON> vienumu"}, "confirm": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "savePasskey": {"message": "Saglabāt <PERSON> atslēgu"}, "savePasskeyNewLogin": {"message": "Saglabāt piekļ<PERSON> at<PERSON>lēgu kā jaunu piete<PERSON> vienumu"}, "chooseCipherForPasskeySave": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kur<PERSON> saglabāt šo piekļ<PERSON> at<PERSON>lēgu"}, "chooseCipherForPasskeyAuth": {"message": "Izvēlēties piekļuves at<PERSON>lēgu, ar kuru pieteikties"}, "passkeyItem": {"message": "Piekļuves atslēgas vienums"}, "overwritePasskey": {"message": "Pārrakstīt pie<PERSON>ļuve<PERSON> atslēgu?"}, "overwritePasskeyAlert": {"message": "Šis vienums jau satur piekļuves atslēgu. Vai tiešām pārrakstīt pašreizējo piekļuves atslēgu?"}, "featureNotSupported": {"message": "Iespēja vēl netiek nodrošināta"}, "yourPasskeyIsLocked": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lai i<PERSON><PERSON><PERSON> pie<PERSON> at<PERSON>lēgu. Jāapliecina sava identitāte, lai turpin<PERSON><PERSON>."}, "multifactorAuthenticationCancelled": {"message": "Daudzpakāpju pieteik<PERSON>ā<PERSON>"}, "noLastPassDataFound": {"message": "<PERSON><PERSON> dati"}, "incorrectUsernameOrPassword": {"message": "Nepareizs lietotājvārds vai parole"}, "incorrectPassword": {"message": "Nepareiza parole"}, "incorrectCode": {"message": "Nepareizs kods"}, "incorrectPin": {"message": "Nepareizs PIN"}, "multifactorAuthenticationFailed": {"message": "Daudzpakā<PERSON><PERSON>"}, "includeSharedFolders": {"message": "<PERSON><PERSON><PERSON><PERSON> mapes"}, "lastPassEmail": {"message": "LastPass e-pasta adrese"}, "importingYourAccount": {"message": "Ievieto kont<PERSON>..."}, "lastPassMFARequired": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> LastPass daudzpakāpju pieteikšanās"}, "lastPassMFADesc": {"message": "Jāievada vienreizējais piekļuves kods no autentificētāja lietotnes"}, "lastPassOOBDesc": {"message": "<PERSON>āap<PERSON><PERSON><PERSON>ā<PERSON> pieprasījums autentificētāja lietotnē vai jāievada vienreizējs piekļuves kods."}, "passcode": {"message": "Piekļuves kods"}, "lastPassMasterPassword": {"message": "LastPass galvenā parole"}, "lastPassAuthRequired": {"message": "Nepiecie<PERSON><PERSON> LastPass"}, "awaitingSSO": {"message": "<PERSON><PERSON><PERSON> vienoto <PERSON>"}, "awaitingSSODesc": {"message": "<PERSON><PERSON><PERSON><PERSON> turpin<PERSON>t pieteikša<PERSON> ar savas apvienības pieteikšan<PERSON>s datiem."}, "seeDetailedInstructions": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nor<PERSON> ir aplū<PERSON><PERSON><PERSON> mūsu palīdz<PERSON> vietnē", "description": "This is followed a by a hyperlink to the help website."}, "importDirectlyFromLastPass": {"message": "Iev<PERSON><PERSON> tie<PERSON>i no LastPass"}, "importFromCSV": {"message": "Ievietot no CSV"}, "lastPassTryAgainCheckEmail": {"message": "Jāmēģina vēlreiz vai jālūko pēc e-pasta no LastPass, lai aplie<PERSON><PERSON><PERSON> sevi."}, "collection": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lastPassYubikeyDesc": {"message": "Jāievieto ar <PERSON>ass kontu sasaistītā <PERSON> datora USB ligzdā, tad jā<PERSON><PERSON> tās pogai."}, "switchAccount": {"message": "Pārslēgties starp kont<PERSON>m"}, "switchAccounts": {"message": "Pārslēgties starp kont<PERSON>m"}, "switchToAccount": {"message": "Pārslēgties uz kontu"}, "activeAccount": {"message": "<PERSON><PERSON><PERSON><PERSON> i<PERSON>tais konts"}, "bitwardenAccount": {"message": "Bitwarden konts"}, "availableAccounts": {"message": "<PERSON><PERSON><PERSON><PERSON> konti"}, "accountLimitReached": {"message": "Sasniegti konta ierobežojumi. Jāizrakstās no konta, lai pievienotu citu."}, "active": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "locked": {"message": "aiz<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "unlocked": {"message": "atslēgta"}, "server": {"message": "serveris"}, "hostedAt": {"message": "izvietots"}, "useDeviceOrHardwareKey": {"message": "Jāizmanto sava ierīce vai aparatūras atslēga"}, "justOnce": {"message": "<PERSON><PERSON><PERSON> vienreiz"}, "alwaysForThisSite": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>i"}, "domainAddedToExcludedDomains": {"message": "$DOMAIN$ pievienots neiekļautajiem domēniem.", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "commonImportFormats": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Label indicating the most common import formats"}, "confirmContinueToBrowserSettingsTitle": {"message": "Pāriet uz pārlūka iestatījumiem?", "description": "Title for dialog which asks if the user wants to proceed to a relevant browser settings page"}, "confirmContinueToHelpCenter": {"message": "Pāriet uz palīdzības centru?", "description": "Title for dialog which asks if the user wants to proceed to a relevant Help Center page"}, "confirmContinueToHelpCenterPasswordManagementContent": {"message": "<PERSON>āmaina sava pārlūka automātiskās aizpildes un paroļu pārvaldības iestatījumi.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser password management settings"}, "confirmContinueToHelpCenterKeyboardShortcutsContent": {"message": "Pap<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>jum<PERSON>ustiņus skatīt un iestatīt var pārlūka iestatījumos.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser keyboard shortcut settings"}, "confirmContinueToBrowserPasswordManagementSettingsContent": {"message": "<PERSON>āmaina sava pārlūka automātiskās aizpildes un paroļu pārvaldības iestatījumi.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's password management settings page"}, "confirmContinueToBrowserKeyboardShortcutSettingsContent": {"message": "Pap<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>jum<PERSON>ustiņus skatīt un iestatīt var pārlūka iestatījumos.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's keyboard shortcut settings page"}, "overrideDefaultBrowserAutofillTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>ward<PERSON> par noklusējuma paroļu pārvaldnieku?", "description": "Dialog title facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutofillDescription": {"message": "<PERSON><PERSON><PERSON> iespējas neņemšana vērā var radīt nesaderības starp Bitwarden automātiskās aizpildes izvēlni un pārlūka.", "description": "Dialog message facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutoFillSettings": {"message": "<PERSON><PERSON><PERSON><PERSON>ward<PERSON> par noklusējuma paroļu pārva<PERSON>ku", "description": "Label for the setting that allows overriding the default browser autofill settings"}, "privacyPermissionAdditionNotGrantedTitle": {"message": "Nebija iespējams iestatīt Bitwarden kā noklusējuma paroļu pārva<PERSON>ku", "description": "Title for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "privacyPermissionAdditionNotGrantedDescription": {"message": "<PERSON><PERSON> <PERSON><PERSON> Bitwarden nodrošināt pārlūka privātuma atļaujas, lai iestatītu to kā noklusējuma paroļu pārvaldnieku.", "description": "Description for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "makeDefault": {"message": "<PERSON><PERSON><PERSON><PERSON> par noklusējumu", "description": "Button text for the setting that allows overriding the default browser autofill settings"}, "saveCipherAttemptSuccess": {"message": "Piekļuves informācija veiksmīgi saglabāta.", "description": "Notification message for when saving credentials has succeeded."}, "passwordSaved": {"message": "<PERSON><PERSON><PERSON>.", "description": "Notification message for when saving credentials has succeeded."}, "updateCipherAttemptSuccess": {"message": "Piekļuves informācija veiksmīgi at<PERSON>.", "description": "Notification message for when updating credentials has succeeded."}, "passwordUpdated": {"message": "<PERSON><PERSON><PERSON>.", "description": "Notification message for when updating credentials has succeeded."}, "saveCipherAttemptFailed": {"message": "<PERSON><PERSON><PERSON><PERSON> piekļuves informācijas saglabāšanā. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, vai konsolē ir izvērstāka informācija.", "description": "Notification message for when saving credentials has failed."}, "success": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "removePasskey": {"message": "Noņemt piekļuves atslēgu"}, "passkeyRemoved": {"message": "Piekļuves atslēga noņemta"}, "autofillSuggestions": {"message": "Automātisk<PERSON><PERSON> a<PERSON> i<PERSON>"}, "itemSuggestions": {"message": "<PERSON><PERSON><PERSON><PERSON> vien<PERSON>"}, "autofillSuggestionsTip": {"message": "Saglabāt pie<PERSON> v<PERSON>, ko automātiski a<PERSON>ld<PERSON>t šaj<PERSON> vietnē"}, "yourVaultIsEmpty": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir tuk<PERSON>"}, "noItemsMatchSearch": {"message": "Neviens vienums neatbilst meklētajam"}, "clearFiltersOrTryAnother": {"message": "Jānotīra atlases vērtības vai jāmēģina cits mekl<PERSON><PERSON><PERSON> vaicājums"}, "copyInfoTitle": {"message": "Ievietot starpliktuvē informāciju - $ITEMNAME$", "description": "Title for a button that opens a menu with options to copy information from an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "copyNoteTitle": {"message": "Ievietot starpliktuvē piezīmi - $ITEMNAME$", "description": "Title for a button copies a note to the clipboard.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Note Item"}}}, "moreOptionsLabel": {"message": "Vairāk iespēju, $ITEMNAME$", "description": "Aria label for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "moreOptionsTitle": {"message": "Vairāk iespēju - $ITEMNAME$", "description": "Title for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitle": {"message": "Skatīt vienumu - $ITEMNAME$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitleWithField": {"message": "Apskatīt vienumu - $ITEMNAME$ - $FIELD$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "autofillTitle": {"message": "Automātiski aizpildīt - $ITEMNAME$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "autofillTitleWithField": {"message": "Automātiskā aizpilde - $ITEMNAME$ - $FIELD$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "copyFieldValue": {"message": "Ievietot starpliktuvē $FIELD$, $VALUE$", "description": "Title for a button that copies a field value to the clipboard.", "placeholders": {"field": {"content": "$1", "example": "Username"}, "value": {"content": "$2", "example": "Foo"}}}, "noValuesToCopy": {"message": "Nav v<PERSON><PERSON><PERSON><PERSON>, ko ievietot starpliktuvē"}, "assignToCollections": {"message": "Piešķirt krājumiem"}, "copyEmail": {"message": "Ievietot starpliktuvē e-pasta adresi"}, "copyPhone": {"message": "Ievietot starpliktuvē tālruņa numuru"}, "copyAddress": {"message": "Ievietot starpliktuvē adresi"}, "adminConsole": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kons<PERSON>,"}, "accountSecurity": {"message": "Konta drošība"}, "notifications": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "appearance": {"message": "Izskats"}, "errorAssigningTargetCollection": {"message": "Kļūda mērķa krājuma piešķiršanā."}, "errorAssigningTargetFolder": {"message": "Kļūda mērķa mapes piešķiršanā."}, "viewItemsIn": {"message": "Skatīt $NAME$ vienumus", "description": "Button to view the contents of a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "backTo": {"message": "Atgriezties uz $NAME$", "description": "Navigate back to a previous folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "new": {"message": "<PERSON><PERSON><PERSON>"}, "removeItem": {"message": "Noņemt $NAME$", "description": "Remove a selected option, such as a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "itemsWithNoFolder": {"message": "Vienumi bez mapes"}, "itemDetails": {"message": "Vienuma dati"}, "itemName": {"message": "Vienuma nosaukums"}, "organizationIsDeactivated": {"message": "Apvienība ir at<PERSON>"}, "owner": {"message": "Īpašnieks"}, "selfOwnershipLabel": {"message": "Tu", "description": "Used as a label to indicate that the user is the owner of an item."}, "contactYourOrgAdmin": {"message": "Atspējotu apvienību vienumiem nevar piekļūt. Jāsazinās ar apvienības <PERSON>, lai iegū<PERSON> palīdzī<PERSON>."}, "additionalInformation": {"message": "<PERSON><PERSON><PERSON><PERSON> informā<PERSON>"}, "itemHistory": {"message": "Vienuma vēsture"}, "lastEdited": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> reizi labots"}, "ownerYou": {"message": "Īpašnieks: <PERSON>"}, "linked": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "copySuccessful": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>pliktuvē ve<PERSON>ga"}, "upload": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "addAttachment": {"message": "<PERSON><PERSON><PERSON>"}, "maxFileSizeSansPunctuation": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> datnes izmērs ir 500 MB"}, "deleteAttachmentName": {"message": "Izdzēst pielikumu $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadAttachmentName": {"message": "Lejupielādēt $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadBitwarden": {"message": "Leju<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "downloadBitwardenOnAllDevices": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> visās ier<PERSON>s"}, "getTheMobileApp": {"message": "Iegūsti viedierīču lietotni"}, "getTheMobileAppDesc": {"message": "Piekļūsti savām parolēm kustībā ar Bitwarden viedierīču lietot<PERSON>!"}, "getTheDesktopApp": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON>"}, "getTheDesktopAppDesc": {"message": "Piekļūsti savai glabātavai bez pārlūka, tad iestati atslēgšanu ar biometriju, lai paātrinātu atslēgšanu gan darbvirsmas lietotn<PERSON>, gan pārlūka paplašinājumā!"}, "downloadFromBitwardenNow": {"message": "Lejupielādē no bitwarden.com tagad"}, "getItOnGooglePlay": {"message": "<PERSON><PERSON><PERSON><PERSON> to Google Play"}, "downloadOnTheAppStore": {"message": "Lejupielādēt no App Store"}, "permanentlyDeleteAttachmentConfirmation": {"message": "Vai tiešām neatgriezeniski izdzēst šo pielikumu?"}, "premium": {"message": "Premium"}, "freeOrgsCannotUseAttachments": {"message": "Bezmaksas apvienības nevar izman<PERSON>t pie<PERSON>us"}, "filters": {"message": "Atlases"}, "filterVault": {"message": "<PERSON><PERSON><PERSON>"}, "filterApplied": {"message": "Pielietots viens atlasītājs"}, "filterAppliedPlural": {"message": "Pielietoti $COUNT$ atlasītāji", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "personalDetails": {"message": "Personiskā informācija"}, "identification": {"message": "Identifikācija"}, "contactInfo": {"message": "Saziņas informācija"}, "downloadAttachment": {"message": "Lejupielādēt $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Your File"}}}, "cardNumberEndsWith": {"message": "kartes numurs beidzas ar", "description": "Used within the inline menu to provide an aria description when users are attempting to fill a card cipher."}, "loginCredentials": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dati"}, "authenticatorKey": {"message": "Autentificētāja atslēga"}, "autofillOptions": {"message": "Automātisk<PERSON><PERSON> a<PERSON>"}, "websiteUri": {"message": "Tīmekļvietne (URI)"}, "websiteUriCount": {"message": "Tīmekļvietne (URI) $COUNT$", "description": "Label for an input field that contains a website URI. The input field is part of a list of fields, and the count indicates the position of the field in the list.", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "websiteAdded": {"message": "Tīmekļvietne pievienota"}, "addWebsite": {"message": "Pievient tīmekļvietni"}, "deleteWebsite": {"message": "Izdzēst tīmekļvietni"}, "defaultLabel": {"message": "Noklusējums ($VALUE$)", "description": "A label that indicates the default value for a field with the current default value in parentheses.", "placeholders": {"value": {"content": "$1", "example": "Base domain"}}}, "showMatchDetection": {"message": "Rādīt atbilstības noteikšanu $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "hideMatchDetection": {"message": "Paslēpt atbilstības noteikšanu $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "autoFillOnPageLoad": {"message": "Automā<PERSON><PERSON> a<PERSON>t lapas ielādes brīdī?"}, "cardExpiredTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> kartes der<PERSON>s"}, "cardExpiredMessage": {"message": "<PERSON>a <PERSON><PERSON> to, j<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON> inform<PERSON>"}, "cardDetails": {"message": "<PERSON><PERSON><PERSON>"}, "cardBrandDetails": {"message": "$BRAND$ dati", "placeholders": {"brand": {"content": "$1", "example": "Visa"}}}, "enableAnimations": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "showAnimations": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "addAccount": {"message": "<PERSON><PERSON><PERSON> k<PERSON>"}, "loading": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "data": {"message": "<PERSON><PERSON>"}, "passkeys": {"message": "Piekļuves atslēgas", "description": "A section header for a list of passkeys."}, "passwords": {"message": "Paroles", "description": "A section header for a list of passwords."}, "logInWithPasskeyAriaLabel": {"message": "Pieteikties ar piekļuves atslēgu", "description": "ARIA label for the inline menu button that logs in with a passkey."}, "assign": {"message": "Piešķirt"}, "bulkCollectionAssignmentDialogDescriptionSingular": {"message": "Vienum<PERSON> varēs red<PERSON>t tikai apvnienības dalībnieki ar piekļuvi šiem krājumiem."}, "bulkCollectionAssignmentDialogDescriptionPlural": {"message": "V<PERSON><PERSON>us varēs red<PERSON>t tikai apvnienības dalībnieki ar piekļuvi šiem krājumiem."}, "bulkCollectionAssignmentWarning": {"message": "<PERSON>r atlasīti $TOTAL_COUNT$ vienumi. Nevar atjaunināt $READONLY_COUNT$ no vienumiem, jo trū<PERSON>t lab<PERSON> atļaujas.", "placeholders": {"total_count": {"content": "$1", "example": "10"}, "readonly_count": {"content": "$2"}}}, "addField": {"message": "<PERSON><PERSON><PERSON>"}, "add": {"message": "<PERSON><PERSON><PERSON>"}, "fieldType": {"message": "Lau<PERSON> veids"}, "fieldLabel": {"message": "<PERSON><PERSON>"}, "textHelpText": {"message": "<PERSON><PERSON><PERSON> lauki ir izmantoja<PERSON> tādai informācijai kā droš<PERSON> j<PERSON>āju<PERSON>"}, "hiddenHelpText": {"message": "Paslēpt<PERSON> lauki ir izman<PERSON>ja<PERSON> tādai slepenai informācijai kā <PERSON>"}, "checkBoxHelpText": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> rūtiņas ir <PERSON><PERSON><PERSON>, ja ir vajadzība automātiski aizpildīt veidlapas izvē<PERSON> rūti<PERSON>, pie<PERSON><PERSON><PERSON>, atcerēties e-pasta adresi"}, "linkedHelpText": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> lauks ir <PERSON>, kad note<PERSON><PERSON><PERSON> lapā tiek pier<PERSON>tas nepilnības ar automā<PERSON>."}, "linkedLabelHelpText": {"message": "Jāievada lauka HTML id, name, aria-label vai placeholder v<PERSON><PERSON><PERSON><PERSON>."}, "editField": {"message": "<PERSON><PERSON>"}, "editFieldLabel": {"message": "Labot $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "deleteCustomField": {"message": "Izdzēst $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "fieldAdded": {"message": "$LABEL$ pievienots", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderToggleButton": {"message": "Pārkārtot $LABEL$. <PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>, lai pārvie<PERSON>tu vienumu augšup vai lejup.", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderWebsiteUriButton": {"message": "Pārkārtot tīmekļvietņu URI. Bultas uz augšu tausti<PERSON> ir i<PERSON>, lai pārvietotu vienumu augšup vai lejup."}, "reorderFieldUp": {"message": "$LABEL$ pārvietots augšup, $INDEX$. no $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "selectCollectionsToAssign": {"message": "<PERSON><PERSON><PERSON>, lai piešķirtu"}, "personalItemTransferWarningSingular": {"message": "1 vienums tiks neatgriezeniski nodots atlasītajai apvienībai. Šis vienums Tev vairs nepiederēs."}, "personalItemsTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ vienumi tiks neatgriezeniski nodoti atlasītajai apvienībai. Šie vienumi Tev vairs ne<PERSON>.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}}}, "personalItemWithOrgTransferWarningSingular": {"message": "1 vienums tiks neatgriezeniski nodots $ORG$. Šis vienums Tev vairs nepiederēs.", "placeholders": {"org": {"content": "$1", "example": "Organization name"}}}, "personalItemsWithOrgTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ vienumi tiks neatgriezeniski nodoti $ORG$. Šie vienumi Tev vairs nepieder<PERSON>.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}, "org": {"content": "$2", "example": "Organization name"}}}, "successfullyAssignedCollections": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> piešķirti"}, "nothingSelected": {"message": "<PERSON>ekas nav atlasīts."}, "itemsMovedToOrg": {"message": "Vienumi pārvietoti uz $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "itemMovedToOrg": {"message": "Vienums pārvietots uz $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "reorderFieldDown": {"message": "$LABEL$ pārvietots lejup, $INDEX$. no $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "itemLocation": {"message": "Vienuma atrašanās vieta"}, "fileSend": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "fileSends": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "textSend": {"message": "Teksta Send"}, "textSends": {"message": "Teksta Send"}, "accountActions": {"message": "Konta darbības"}, "showNumberOfAutofillSuggestions": {"message": "Pa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>ā rādīt piete<PERSON>ās automātiskās a<PERSON>pildes ieteikumu skaitu"}, "showQuickCopyActions": {"message": "Glabāta<PERSON><PERSON> rādīt ā<PERSON><PERSON> k<PERSON> darb<PERSON>"}, "systemDefault": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "enterprisePolicyRequirementsApplied": {"message": "<PERSON><PERSON>m tika piemērotas uzņēmējdarbības nosacījumu pras<PERSON>bas"}, "sshPrivateKey": {"message": "Privātā at<PERSON>lēga"}, "sshPublicKey": {"message": "Publiskā atslēga"}, "sshFingerprint": {"message": "Pirkstu nospiedums"}, "sshKeyAlgorithm": {"message": "Atslēgas veids"}, "sshKeyAlgorithmED25519": {"message": "ED25519"}, "sshKeyAlgorithmRSA2048": {"message": "RSA 2048-Bit"}, "sshKeyAlgorithmRSA3072": {"message": "RSA 3072-Bit"}, "sshKeyAlgorithmRSA4096": {"message": "RSA 4096-Bit"}, "retry": {"message": "Mēģināt vēlreiz"}, "vaultCustomTimeoutMinimum": {"message": "Mazākā pieļaujamā pielāgotā noildze ir 1 minūte."}, "additionalContentAvailable": {"message": "<PERSON>r pieejams papildu saturs"}, "fileSavedToDevice": {"message": "Datne saglabāta ierīcē. Tā ir atrodama ierīces lejupielāžu mapē."}, "showCharacterCount": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "hideCharacterCount": {"message": "Paslē<PERSON> r<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>"}, "itemsInTrash": {"message": "<PERSON><PERSON><PERSON>"}, "noItemsInTrash": {"message": "Atkritnē nav vienumu"}, "noItemsInTrashDesc": {"message": "Izdzēstie vienumi parādīsies šeit, un tie tiks neatgriezeniski izdzēsti pēc 30 dienām"}, "trashWarning": {"message": "<PERSON><PERSON><PERSON>, kas atkritnē atrodas vairāk nekā 30 dienas, tiks automatiski izdzēsti"}, "restore": {"message": "At<PERSON>uno<PERSON>"}, "deleteForever": {"message": "<PERSON><PERSON>d<PERSON><PERSON><PERSON> pavisam"}, "noEditPermissions": {"message": "Nav ne<PERSON><PERSON><PERSON><PERSON> atļauju, lai labotu šo vienumu"}, "biometricsStatusHelptextUnlockNeeded": {"message": "Atslēgšana ar biometriju nav pieejama, jo vispirms ir nepieciešama atslēgšana ar PIN vai paroli."}, "biometricsStatusHelptextHardwareUnavailable": {"message": "Atslēgšana ar biometriju pašlaik nav pieejama."}, "biometricsStatusHelptextAutoSetupNeeded": {"message": "Atslēgšana ar biometriju nav pieejama nepareizi konfigurētu sistēmas datņu dē<PERSON>."}, "biometricsStatusHelptextManualSetupNeeded": {"message": "Atslēgšana ar biometriju nav pieejama nepareizi konfigurētu sistēmas datņu dē<PERSON>."}, "biometricsStatusHelptextDesktopDisconnected": {"message": "Atslēgšana ar biometriju nav pieejama, jo <PERSON> darbvirsmas lietotne ir aizvērta."}, "biometricsStatusHelptextNotEnabledInDesktop": {"message": "Atslēgšana ar biometriju nav pieejama, jo tā nav iespējota $EMAIL$ Bitwarden darbvirsmas lietotnē.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "biometricsStatusHelptextUnavailableReasonUnknown": {"message": "Atslēgšana ar biometriju pašlaik nav pieejama nezināma iem<PERSON>la dēļ."}, "unlockVault": {"message": "Atslēdz savu glabātavu dažās sekund<PERSON>s"}, "unlockVaultDesc": {"message": "Tu vari pielāgot savus atslēgšanas un noildzes iestatījumus, lai ātrāk piekļūtu savai glabātavai."}, "unlockPinSet": {"message": "Atslēgšanas PIN iestatīts"}, "authenticating": {"message": "Autentificē"}, "fillGeneratedPassword": {"message": "Aizpildīt izveidoto paroli", "description": "Heading for the password generator within the inline menu"}, "passwordRegenerated": {"message": "Pa<PERSON><PERSON>", "description": "Notification message for when a password has been regenerated"}, "saveToBitwarden": {"message": "<PERSON>g<PERSON><PERSON><PERSON><PERSON>", "description": "Confirmation message for saving a login to Bitwarden"}, "spaceCharacterDescriptor": {"message": "Atstarpe", "description": "Represents the space key in screen reader content as a readable word"}, "tildeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ~ key in screen reader content as a readable word"}, "backtickCharacterDescriptor": {"message": "Atpa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Represents the ` key in screen reader content as a readable word"}, "exclamationCharacterDescriptor": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Represents the ! key in screen reader content as a readable word"}, "atSignCharacterDescriptor": {"message": "At zīme", "description": "Represents the @ key in screen reader content as a readable word"}, "hashSignCharacterDescriptor": {"message": "<PERSON><PERSON> z<PERSON>", "description": "Represents the # key in screen reader content as a readable word"}, "dollarSignCharacterDescriptor": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Represents the $ key in screen reader content as a readable word"}, "percentSignCharacterDescriptor": {"message": "<PERSON><PERSON><PERSON>", "description": "Represents the % key in screen reader content as a readable word"}, "caretCharacterDescriptor": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Represents the ^ key in screen reader content as a readable word"}, "ampersandCharacterDescriptor": {"message": "Un zīme", "description": "Represents the & key in screen reader content as a readable word"}, "asteriskCharacterDescriptor": {"message": "Asterisks", "description": "Represents the * key in screen reader content as a readable word"}, "parenLeftCharacterDescriptor": {"message": "<PERSON><PERSON><PERSON>", "description": "Represents the ( key in screen reader content as a readable word"}, "parenRightCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ) key in screen reader content as a readable word"}, "hyphenCharacterDescriptor": {"message": "Apakšsvītra", "description": "Represents the _ key in screen reader content as a readable word"}, "underscoreCharacterDescriptor": {"message": "De<PERSON><PERSON>", "description": "Represents the - key in screen reader content as a readable word"}, "plusCharacterDescriptor": {"message": "Pluss", "description": "Represents the + key in screen reader content as a readable word"}, "equalsCharacterDescriptor": {"message": "Vienād<PERSON><PERSON>", "description": "Represents the = key in screen reader content as a readable word"}, "braceLeftCharacterDescriptor": {"message": "<PERSON><PERSON><PERSON>", "description": "Represents the { key in screen reader content as a readable word"}, "braceRightCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the } key in screen reader content as a readable word"}, "bracketLeftCharacterDescriptor": {"message": "<PERSON><PERSON><PERSON>", "description": "Represents the [ key in screen reader content as a readable word"}, "bracketRightCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ] key in screen reader content as a readable word"}, "pipeCharacterDescriptor": {"message": "Stateniska svī<PERSON>", "description": "Represents the | key in screen reader content as a readable word"}, "backSlashCharacterDescriptor": {"message": "Atpakaļslīpsvītra", "description": "Represents the back slash key in screen reader content as a readable word"}, "colonCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the : key in screen reader content as a readable word"}, "semicolonCharacterDescriptor": {"message": "Semikols", "description": "Represents the ; key in screen reader content as a readable word"}, "doubleQuoteCharacterDescriptor": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Represents the double quote key in screen reader content as a readable word"}, "singleQuoteCharacterDescriptor": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Represents the ' key in screen reader content as a readable word"}, "lessThanCharacterDescriptor": {"message": "Mazā<PERSON> par", "description": "Represents the < key in screen reader content as a readable word"}, "greaterThanCharacterDescriptor": {"message": "<PERSON><PERSON><PERSON><PERSON> par", "description": "Represents the > key in screen reader content as a readable word"}, "commaCharacterDescriptor": {"message": "<PERSON><PERSON><PERSON>", "description": "Represents the , key in screen reader content as a readable word"}, "periodCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the . key in screen reader content as a readable word"}, "questionCharacterDescriptor": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Represents the ? key in screen reader content as a readable word"}, "forwardSlashCharacterDescriptor": {"message": "Slīpsvītra", "description": "Represents the / key in screen reader content as a readable word"}, "lowercaseAriaLabel": {"message": "<PERSON><PERSON> b<PERSON>i"}, "uppercaseAriaLabel": {"message": "<PERSON><PERSON> b<PERSON>"}, "generatedPassword": {"message": "Izveidotā parole"}, "compactMode": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "beta": {"message": "Beta"}, "extensionWidth": {"message": "Paplašināju<PERSON> platums"}, "wide": {"message": "Plats"}, "extraWide": {"message": "Ļoti plats"}, "sshKeyWrongPassword": {"message": "Ievadītā parole ir nepareiza."}, "importSshKey": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "confirmSshKeyPassword": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> paroli"}, "enterSshKeyPasswordDesc": {"message": "Ievadīt SSH atslēgas paroli."}, "enterSshKeyPassword": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>oli"}, "invalidSshKey": {"message": "SSH atslēga ir nederīga"}, "sshKeyTypeUnsupported": {"message": "SSH atslēgas veids netiek atbalstīts"}, "importSshKeyFromClipboard": {"message": "Ievietot atslēgu no starpliktuves"}, "sshKeyImported": {"message": "SSH atslēga tika sekmīgi ievietota"}, "cannotRemoveViewOnlyCollections": {"message": "Nevar no<PERSON>t krā<PERSON><PERSON> ar <PERSON> \"Tikai skatīt\": $COLLECTIONS$", "placeholders": {"collections": {"content": "$1", "example": "Work, Personal"}}}, "updateDesktopAppOrDisableFingerprintDialogTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> da<PERSON> lie<PERSON>"}, "updateDesktopAppOrDisableFingerprintDialogMessage": {"message": "<PERSON> atslēg<PERSON><PERSON> ar biometriju, l<PERSON><PERSON><PERSON> at<PERSON>t darbvirsmas lietotni vai atspējot atslēgšanu ar pirkstu nospiedumu darbvirsmas iestatīju<PERSON>."}, "changeAtRiskPassword": {"message": "<PERSON><PERSON><PERSON> p<PERSON> paroli"}, "settingsVaultOptions": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "emptyVaultDescription": {"message": "Glabātava aizsargā vairāk kā tikai paroles. <PERSON><PERSON><PERSON><PERSON> veidā glabā šeit pieteikšanās vienumus, identit<PERSON><PERSON>, kartes un piezīmes!"}, "introCarouselLabel": {"message": "<PERSON><PERSON><PERSON> lūd<PERSON>"}, "securityPrioritized": {"message": "Dr<PERSON><PERSON><PERSON><PERSON> pirmajā vietā"}, "securityPrioritizedBody": {"message": "Piete<PERSON><PERSON><PERSON><PERSON><PERSON> vien<PERSON>, karšu un identitāšu glabāšana savā drošajā glabātavā. Bitwarden izmanto nulles zin<PERSON><PERSON><PERSON> pilnīgu <PERSON>, lai a<PERSON> to, kas <PERSON>v ir svarī<PERSON>."}, "quickLogin": {"message": "Ātra un vienk<PERSON><PERSON><PERSON>"}, "quickLoginBody": {"message": "Iestati biometrisko atslēgšanu un automātiski aizpildi, lai pieteiktos savos kontos bez neviena burta ievadīšanas."}, "secureUser": {"message": "Uzlabo savu piete<PERSON>"}, "secureUserBody": {"message": "Veidotā<PERSON><PERSON> i<PERSON>, lai izveidotu un saglabātu s<PERSON>, neatk<PERSON>rtojamas paroles visiem Tavi<PERSON> kont<PERSON>."}, "secureDevices": {"message": "<PERSON> da<PERSON>, kad un kur vien tie ir nepiecieša<PERSON>"}, "secureDevicesBody": {"message": "Neierobežotu paroļu skaitu var saglabāt neierobežotā ierīdžu daudzumā ar Bitwarden viedtālruņa, pārlūka un darbvirsmas lietotni."}, "nudgeBadgeAria": {"message": "1 paziņojums"}, "emptyVaultNudgeTitle": {"message": "Ieviet<PERSON> es<PERSON><PERSON><PERSON> paroles"}, "emptyVaultNudgeBody": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>, lai pie<PERSON><PERSON><PERSON><PERSON><PERSON> vienumus <PERSON> pā<PERSON>st uz Bitwarden bez p<PERSON><PERSON><PERSON> to <PERSON><PERSON><PERSON>šanas."}, "emptyVaultNudgeButton": {"message": "Ievietot tagad"}, "hasItemsVaultNudgeTitle": {"message": "Lai<PERSON><PERSON> lūdzam Tavā glabātavā!"}, "hasItemsVaultNudgeBodyOne": {"message": "Automātiska pašreizējās lapas vienumu a<PERSON>"}, "hasItemsVaultNudgeBodyTwo": {"message": "Vienumu izlase vieglai piekļuvei"}, "hasItemsVaultNudgeBodyThree": {"message": "Me<PERSON><PERSON><PERSON> savā glabātavā kaut ko citu"}, "newLoginNudgeTitle": {"message": "Laika ietaupīšana ar automātisko <PERSON>"}, "newLoginNudgeBodyOne": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyBold": {"message": "tīmekļ<PERSON>ni,", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyTwo": {"message": "lai šis pieteikšanās vienums parādītos kā automātiskās aizpildes ieteikums!", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newCardNudgeTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON> tie<PERSON>"}, "newCardNudgeBody": {"message": "Ar kartēm ir viegli automātiski aizpildīt maksājumu veidlapu droši un rūpīgi."}, "newIdentityNudgeTitle": {"message": "Kontu izveidošanas vienkā<PERSON>š<PERSON>na"}, "newIdentityNudgeBody": {"message": "Ar identitātēm var ātri automātiski aizpildīt garas reģistrēšanās vai saziņas veidlapas."}, "newNoteNudgeTitle": {"message": "<PERSON><PERSON> savus j<PERSON><PERSON> datus dro<PERSON>"}, "newNoteNudgeBody": {"message": "Pie<PERSON>ī<PERSON><PERSON><PERSON> var droši glabāt jūtīgus datus, pie<PERSON><PERSON><PERSON>, bankas vai apdrošin<PERSON><PERSON>nas informāciju."}, "newSshNudgeTitle": {"message": "Izstr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> draudzīga SSH piekļuve"}, "newSshNudgeBodyOne": {"message": "Glabā savas atslēgas un savienojies ar SSH aģentu ātrai, šifr<PERSON>tai autentificēšanai!", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "newSshNudgeBodyTwo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON> par SSH aģentu", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "generatorNudgeTitle": {"message": "Ātra paroļu izve<PERSON>"}, "generatorNudgeBodyOne": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> spēcīgu un neat<PERSON>ārtojamu paroļu izveido<PERSON> ar", "description": "Two part message", "example": "Easily create strong and unique passwords by clicking on {icon} to help you keep your logins secure."}, "generatorNudgeBodyTwo": {"message": ", lai palīd<PERSON><PERSON><PERSON> uztur<PERSON>rt piete<PERSON> vienumus dro<PERSON>.", "description": "Two part message", "example": "Easily create strong and unique passwords by clicking on {icon} to help you keep your logins secure."}, "generatorNudgeBodyAria": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> spēcīgu un neatkārtojamu paroļu izveidošana ar pogu \"Izveidot paroli\", lai palīdz<PERSON><PERSON> uzturēt pieteikšanās vienumus droš<PERSON>.", "description": "Aria label for the body content of the generator nudge"}, "noPermissionsViewPage": {"message": "Nav atļaujas apskatīt šo lapu. Jāmēģina pieteikties ar citu kontu."}}